{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/DocumentenSchrijver/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/DocumentenSchrijver/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/DocumentenSchrijver/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/DocumentenSchrijver/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 231, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/DocumentenSchrijver/frontend/src/components/ui/resizable.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { GripVerticalIcon } from \"lucide-react\"\nimport * as ResizablePrimitive from \"react-resizable-panels\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction ResizablePanelGroup({\n  className,\n  ...props\n}: React.ComponentProps<typeof ResizablePrimitive.PanelGroup>) {\n  return (\n    <ResizablePrimitive.PanelGroup\n      data-slot=\"resizable-panel-group\"\n      className={cn(\n        \"flex h-full w-full data-[panel-group-direction=vertical]:flex-col\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction ResizablePanel({\n  ...props\n}: React.ComponentProps<typeof ResizablePrimitive.Panel>) {\n  return <ResizablePrimitive.Panel data-slot=\"resizable-panel\" {...props} />\n}\n\nfunction ResizableHandle({\n  withHandle,\n  className,\n  ...props\n}: React.ComponentProps<typeof ResizablePrimitive.PanelResizeHandle> & {\n  withHandle?: boolean\n}) {\n  return (\n    <ResizablePrimitive.PanelResizeHandle\n      data-slot=\"resizable-handle\"\n      className={cn(\n        \"bg-border focus-visible:ring-ring relative flex w-px items-center justify-center after:absolute after:inset-y-0 after:left-1/2 after:w-1 after:-translate-x-1/2 focus-visible:ring-1 focus-visible:ring-offset-1 focus-visible:outline-hidden data-[panel-group-direction=vertical]:h-px data-[panel-group-direction=vertical]:w-full data-[panel-group-direction=vertical]:after:left-0 data-[panel-group-direction=vertical]:after:h-1 data-[panel-group-direction=vertical]:after:w-full data-[panel-group-direction=vertical]:after:-translate-y-1/2 data-[panel-group-direction=vertical]:after:translate-x-0 [&[data-panel-group-direction=vertical]>div]:rotate-90\",\n        className\n      )}\n      {...props}\n    >\n      {withHandle && (\n        <div className=\"bg-border z-10 flex h-4 w-3 items-center justify-center rounded-xs border\">\n          <GripVerticalIcon className=\"size-2.5\" />\n        </div>\n      )}\n    </ResizablePrimitive.PanelResizeHandle>\n  )\n}\n\nexport { ResizablePanelGroup, ResizablePanel, ResizableHandle }\n"], "names": [], "mappings": ";;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,oBAAoB,EAC3B,SAAS,EACT,GAAG,OACwD;IAC3D,qBACE,8OAAC,oNAAA,CAAA,aAA6B;QAC5B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,8OAAC,oNAAA,CAAA,QAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,gBAAgB,EACvB,UAAU,EACV,SAAS,EACT,GAAG,OAGJ;IACC,qBACE,8OAAC,oNAAA,CAAA,oBAAoC;QACnC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6oBACA;QAED,GAAG,KAAK;kBAER,4BACC,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;;;;;AAKtC", "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/DocumentenSchrijver/frontend/src/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useTheme } from \"next-themes\"\nimport { Toaster as Son<PERSON>, ToasterP<PERSON> } from \"sonner\"\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = \"system\" } = useTheme()\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps[\"theme\"]}\n      className=\"toaster group\"\n      style={\n        {\n          \"--normal-bg\": \"var(--popover)\",\n          \"--normal-text\": \"var(--popover-foreground)\",\n          \"--normal-border\": \"var(--border)\",\n        } as React.CSSProperties\n      }\n      {...props}\n    />\n  )\n}\n\nexport { Toaster }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,8OAAC,wIAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/DocumentenSchrijver/frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 358, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/DocumentenSchrijver/frontend/src/components/file-explorer.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { \n  FolderOpen, \n  File, \n  Plus, \n  Trash2, \n  RefreshCw,\n  Search,\n  Home,\n  ChevronRight,\n  FileText,\n  Folder\n} from \"lucide-react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Card } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { toast } from \"sonner\";\n\ninterface FileItem {\n  path: string;\n  name: string;\n  filename?: string;\n  type: \"file\" | \"folder\";\n  size?: number;\n  modified_at: string;\n  content_preview?: string;\n}\n\ninterface SelectedFile {\n  path: string;\n  filename: string;\n  type: string;\n  size?: number;\n  modified_at: string;\n}\n\ninterface FileExplorerProps {\n  onFileSelect: (file: SelectedFile) => void;\n  selectedFile: SelectedFile | null;\n  currentFolder: string;\n  onFolderChange: (folder: string) => void;\n}\n\nexport function FileExplorer({\n  onFileSelect,\n  selectedFile,\n  currentFolder,\n  onFolderChange,\n}: FileExplorerProps) {\n  const [files, setFiles] = useState<FileItem[]>([]);\n  const [folders, setFolders] = useState<FileItem[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [searchResults, setSearchResults] = useState<FileItem[]>([]);\n  const [isSearching, setIsSearching] = useState(false);\n  const [showNewFileDialog, setShowNewFileDialog] = useState(false);\n  const [showNewFolderDialog, setShowNewFolderDialog] = useState(false);\n  const [newFileName, setNewFileName] = useState(\"\");\n  const [newFolderName, setNewFolderName] = useState(\"\");\n\n  useEffect(() => {\n    loadFilesAndFolders();\n  }, [currentFolder]);\n\n  useEffect(() => {\n    if (searchQuery.length >= 2) {\n      performSearch();\n    } else {\n      setSearchResults([]);\n      setIsSearching(false);\n    }\n  }, [searchQuery]);\n\n  const loadFilesAndFolders = async () => {\n    setLoading(true);\n    try {\n      const response = await fetch(\n        `http://localhost:8001/files?folder_path=${currentFolder}`\n      );\n      if (response.ok) {\n        const data = await response.json();\n        setFiles(data.files || []);\n        setFolders(data.folders || []);\n      }\n    } catch (error) {\n      console.error(\"Error loading files:\", error);\n      toast.error(\"Kon bestanden niet laden\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const performSearch = async () => {\n    setIsSearching(true);\n    try {\n      const response = await fetch(\n        `http://localhost:8001/search?query=${encodeURIComponent(\n          searchQuery\n        )}&folder_path=${currentFolder}`\n      );\n      if (response.ok) {\n        const data = await response.json();\n        setSearchResults(data);\n      }\n    } catch (error) {\n      console.error(\"Error searching:\", error);\n      toast.error(\"Zoeken mislukt\");\n    } finally {\n      setIsSearching(false);\n    }\n  };\n\n  const handleCreateFile = async () => {\n    if (!newFileName.trim()) return;\n\n    try {\n      const filename = newFileName.endsWith(\".md\") ? newFileName : `${newFileName}.md`;\n      const filePath = currentFolder ? `${currentFolder}/${filename}` : filename;\n\n      const response = await fetch(`http://localhost:8001/files/${filePath}`, {\n        method: \"POST\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify({ content: `# ${newFileName}\\n\\nNieuw document...` }),\n      });\n\n      if (response.ok) {\n        setNewFileName(\"\");\n        setShowNewFileDialog(false);\n        loadFilesAndFolders();\n        toast.success(\"Bestand aangemaakt\");\n      }\n    } catch (error) {\n      console.error(\"Error creating file:\", error);\n      toast.error(\"Kon bestand niet aanmaken\");\n    }\n  };\n\n  const handleCreateFolder = async () => {\n    if (!newFolderName.trim()) return;\n\n    try {\n      const response = await fetch(\"http://localhost:8001/folders\", {\n        method: \"POST\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify({\n          folder_path: currentFolder,\n          folder_name: newFolderName,\n        }),\n      });\n\n      if (response.ok) {\n        setNewFolderName(\"\");\n        setShowNewFolderDialog(false);\n        loadFilesAndFolders();\n        toast.success(\"Map aangemaakt\");\n      }\n    } catch (error) {\n      console.error(\"Error creating folder:\", error);\n      toast.error(\"Kon map niet aanmaken\");\n    }\n  };\n\n  const handleDeleteItem = async (item: FileItem) => {\n    if (!confirm(`Weet je zeker dat je ${item.name} wilt verwijderen?`)) return;\n\n    try {\n      const endpoint = item.type === \"folder\" ? \"folders\" : \"files\";\n      const response = await fetch(`http://localhost:8001/${endpoint}/${item.path}`, {\n        method: \"DELETE\",\n      });\n\n      if (response.ok) {\n        loadFilesAndFolders();\n        toast.success(`${item.type === \"folder\" ? \"Map\" : \"Bestand\"} verwijderd`);\n\n        if (selectedFile && selectedFile.path === item.path) {\n          onFileSelect(null as any);\n        }\n      }\n    } catch (error) {\n      console.error(\"Error deleting item:\", error);\n      toast.error(\"Kon item niet verwijderen\");\n    }\n  };\n\n  const handleFolderClick = (folder: FileItem) => {\n    onFolderChange(folder.path);\n  };\n\n  const handleFileClick = (file: FileItem) => {\n    onFileSelect({\n      path: file.path,\n      filename: file.name || file.filename || \"\",\n      type: file.type,\n      size: file.size,\n      modified_at: file.modified_at,\n    });\n  };\n\n  const formatFileSize = (bytes?: number) => {\n    if (!bytes || bytes === 0) return \"0 B\";\n    const k = 1024;\n    const sizes = [\"B\", \"KB\", \"MB\"];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + \" \" + sizes[i];\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString(\"nl-NL\", {\n      day: \"2-digit\",\n      month: \"2-digit\",\n      year: \"numeric\",\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n    });\n  };\n\n  const displayItems = searchQuery.length >= 2 ? searchResults : [...folders, ...files];\n\n  return (\n    <div className=\"h-full bg-card/30 flex flex-col border-r\">\n      {/* Header */}\n      <div className=\"p-4 border-b\">\n        <div className=\"flex items-center justify-between mb-3\">\n          <div className=\"flex items-center space-x-2\">\n            <FolderOpen className=\"w-5 h-5 text-primary\" />\n            <h3 className=\"font-semibold\">Bestanden</h3>\n          </div>\n          <div className=\"flex items-center space-x-1\">\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={() => setShowNewFileDialog(true)}\n              className=\"h-8 w-8\"\n            >\n              <FileText className=\"w-4 h-4\" />\n            </Button>\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={() => setShowNewFolderDialog(true)}\n              className=\"h-8 w-8\"\n            >\n              <Folder className=\"w-4 h-4\" />\n            </Button>\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={loadFilesAndFolders}\n              className=\"h-8 w-8\"\n            >\n              <RefreshCw className=\"w-4 h-4\" />\n            </Button>\n          </div>\n        </div>\n\n        {/* Search */}\n        <div className=\"relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground\" />\n          <Input\n            placeholder=\"Zoeken...\"\n            value={searchQuery}\n            onChange={(e) => setSearchQuery(e.target.value)}\n            className=\"pl-10\"\n          />\n        </div>\n\n        {/* Breadcrumb */}\n        {currentFolder && (\n          <div className=\"flex items-center space-x-1 mt-2 text-sm text-muted-foreground\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => onFolderChange(\"\")}\n              className=\"h-6 px-2\"\n            >\n              <Home className=\"w-3 h-3\" />\n            </Button>\n            {currentFolder.split(\"/\").map((folder, index, array) => (\n              <div key={index} className=\"flex items-center space-x-1\">\n                <ChevronRight className=\"w-3 h-3\" />\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={() =>\n                    onFolderChange(array.slice(0, index + 1).join(\"/\"))\n                  }\n                  className=\"h-6 px-2 text-xs\"\n                >\n                  {folder}\n                </Button>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Content */}\n      <div className=\"flex-1 overflow-y-auto\">\n        {loading ? (\n          <div className=\"flex items-center justify-center h-32\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary\"></div>\n          </div>\n        ) : displayItems.length === 0 ? (\n          <div className=\"flex flex-col items-center justify-center h-32 text-muted-foreground p-4\">\n            {searchQuery ? (\n              <>\n                <Search className=\"w-8 h-8 mb-2\" />\n                <p className=\"text-center\">Geen resultaten gevonden</p>\n              </>\n            ) : (\n              <>\n                <Folder className=\"w-8 h-8 mb-2\" />\n                <p className=\"text-center mb-2\">Geen bestanden gevonden</p>\n                <Button\n                  onClick={() => setShowNewFileDialog(true)}\n                  size=\"sm\"\n                  className=\"text-xs\"\n                >\n                  Maak je eerste document\n                </Button>\n              </>\n            )}\n          </div>\n        ) : (\n          <div className=\"p-2\">\n            <AnimatePresence>\n              {displayItems.map((item, index) => (\n                <motion.div\n                  key={item.path}\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  exit={{ opacity: 0, y: -10 }}\n                  transition={{ delay: index * 0.05 }}\n                  className={`group flex items-center justify-between p-3 rounded-lg cursor-pointer transition-all hover:bg-accent ${\n                    selectedFile?.path === item.path\n                      ? \"bg-primary/10 border border-primary/30\"\n                      : \"\"\n                  }`}\n                  onClick={() =>\n                    item.type === \"folder\"\n                      ? handleFolderClick(item)\n                      : handleFileClick(item)\n                  }\n                >\n                  <div className=\"flex items-center space-x-3 flex-1 min-w-0\">\n                    {item.type === \"folder\" ? (\n                      <Folder className=\"w-5 h-5 text-yellow-500 flex-shrink-0\" />\n                    ) : (\n                      <FileText className=\"w-5 h-5 text-blue-400 flex-shrink-0\" />\n                    )}\n\n                    <div className=\"flex-1 min-w-0\">\n                      <p className=\"text-sm font-medium truncate\">\n                        {item.name || item.filename}\n                      </p>\n                      <div className=\"flex items-center space-x-2 text-xs text-muted-foreground\">\n                        {item.type === \"file\" && item.size && (\n                          <>\n                            <span>{formatFileSize(item.size)}</span>\n                            <span>•</span>\n                          </>\n                        )}\n                        <span>{formatDate(item.modified_at)}</span>\n                      </div>\n                      {item.content_preview && (\n                        <p className=\"text-xs text-muted-foreground mt-1 line-clamp-2\">\n                          {item.content_preview}\n                        </p>\n                      )}\n                    </div>\n                  </div>\n\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      handleDeleteItem(item);\n                    }}\n                    className=\"opacity-0 group-hover:opacity-100 h-8 w-8 text-destructive hover:text-destructive\"\n                  >\n                    <Trash2 className=\"w-4 h-4\" />\n                  </Button>\n                </motion.div>\n              ))}\n            </AnimatePresence>\n          </div>\n        )}\n      </div>\n\n      {/* New File Dialog */}\n      {showNewFileDialog && (\n        <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center p-4 z-50\">\n          <Card className=\"p-6 w-full max-w-sm\">\n            <h3 className=\"text-lg font-semibold mb-4\">Nieuw bestand</h3>\n            <Input\n              placeholder=\"Bestandsnaam...\"\n              value={newFileName}\n              onChange={(e) => setNewFileName(e.target.value)}\n              onKeyPress={(e) => e.key === \"Enter\" && handleCreateFile()}\n              className=\"mb-4\"\n              autoFocus\n            />\n            <div className=\"flex justify-end space-x-3\">\n              <Button\n                variant=\"outline\"\n                onClick={() => setShowNewFileDialog(false)}\n              >\n                Annuleren\n              </Button>\n              <Button onClick={handleCreateFile}>Maken</Button>\n            </div>\n          </Card>\n        </div>\n      )}\n\n      {/* New Folder Dialog */}\n      {showNewFolderDialog && (\n        <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center p-4 z-50\">\n          <Card className=\"p-6 w-full max-w-sm\">\n            <h3 className=\"text-lg font-semibold mb-4\">Nieuwe map</h3>\n            <Input\n              placeholder=\"Mapnaam...\"\n              value={newFolderName}\n              onChange={(e) => setNewFolderName(e.target.value)}\n              onKeyPress={(e) => e.key === \"Enter\" && handleCreateFolder()}\n              className=\"mb-4\"\n              autoFocus\n            />\n            <div className=\"flex justify-end space-x-3\">\n              <Button\n                variant=\"outline\"\n                onClick={() => setShowNewFolderDialog(false)}\n              >\n                Annuleren\n              </Button>\n              <Button onClick={handleCreateFolder}>Maken</Button>\n            </div>\n          </Card>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;AAEA;AApBA;;;;;;;;;AA+CO,SAAS,aAAa,EAC3B,YAAY,EACZ,YAAY,EACZ,aAAa,EACb,cAAc,EACI;IAClB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAc;IAElB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,MAAM,IAAI,GAAG;YAC3B;QACF,OAAO;YACL,iBAAiB,EAAE;YACnB,eAAe;QACjB;IACF,GAAG;QAAC;KAAY;IAEhB,MAAM,sBAAsB;QAC1B,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MACrB,CAAC,wCAAwC,EAAE,eAAe;YAE5D,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,KAAK,KAAK,IAAI,EAAE;gBACzB,WAAW,KAAK,OAAO,IAAI,EAAE;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB;QACpB,eAAe;QACf,IAAI;YACF,MAAM,WAAW,MAAM,MACrB,CAAC,mCAAmC,EAAE,mBACpC,aACA,aAAa,EAAE,eAAe;YAElC,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,iBAAiB;YACnB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oBAAoB;YAClC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,YAAY,IAAI,IAAI;QAEzB,IAAI;YACF,MAAM,WAAW,YAAY,QAAQ,CAAC,SAAS,cAAc,GAAG,YAAY,GAAG,CAAC;YAChF,MAAM,WAAW,gBAAgB,GAAG,cAAc,CAAC,EAAE,UAAU,GAAG;YAElE,MAAM,WAAW,MAAM,MAAM,CAAC,4BAA4B,EAAE,UAAU,EAAE;gBACtE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,SAAS,CAAC,EAAE,EAAE,YAAY,qBAAqB,CAAC;gBAAC;YAC1E;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,eAAe;gBACf,qBAAqB;gBACrB;gBACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,cAAc,IAAI,IAAI;QAE3B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,iCAAiC;gBAC5D,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,aAAa;oBACb,aAAa;gBACf;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,iBAAiB;gBACjB,uBAAuB;gBACvB;gBACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,QAAQ,CAAC,qBAAqB,EAAE,KAAK,IAAI,CAAC,kBAAkB,CAAC,GAAG;QAErE,IAAI;YACF,MAAM,WAAW,KAAK,IAAI,KAAK,WAAW,YAAY;YACtD,MAAM,WAAW,MAAM,MAAM,CAAC,sBAAsB,EAAE,SAAS,CAAC,EAAE,KAAK,IAAI,EAAE,EAAE;gBAC7E,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf;gBACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,KAAK,IAAI,KAAK,WAAW,QAAQ,UAAU,WAAW,CAAC;gBAExE,IAAI,gBAAgB,aAAa,IAAI,KAAK,KAAK,IAAI,EAAE;oBACnD,aAAa;gBACf;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,eAAe,OAAO,IAAI;IAC5B;IAEA,MAAM,kBAAkB,CAAC;QACvB,aAAa;YACX,MAAM,KAAK,IAAI;YACf,UAAU,KAAK,IAAI,IAAI,KAAK,QAAQ,IAAI;YACxC,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,aAAa,KAAK,WAAW;QAC/B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,SAAS,UAAU,GAAG,OAAO;QAClC,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAK;YAAM;SAAK;QAC/B,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,KAAK;YACL,OAAO;YACP,MAAM;YACN,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,eAAe,YAAY,MAAM,IAAI,IAAI,gBAAgB;WAAI;WAAY;KAAM;IAErF,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;wCAAG,WAAU;kDAAgB;;;;;;;;;;;;0CAEhC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,qBAAqB;wCACpC,WAAU;kDAEV,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,uBAAuB;wCACtC,WAAU;kDAEV,cAAA,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAM3B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC,iIAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;;;;;;;;;;;;oBAKb,+BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CAEV,cAAA,8OAAC,mMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;4BAEjB,cAAc,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,QAAQ,OAAO,sBAC5C,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;sDACxB,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IACP,eAAe,MAAM,KAAK,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC;4CAEhD,WAAU;sDAET;;;;;;;mCAVK;;;;;;;;;;;;;;;;;0BAmBlB,8OAAC;gBAAI,WAAU;0BACZ,wBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;2BAEf,aAAa,MAAM,KAAK,kBAC1B,8OAAC;oBAAI,WAAU;8BACZ,4BACC;;0CACE,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCAAE,WAAU;0CAAc;;;;;;;qDAG7B;;0CACE,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCAAE,WAAU;0CAAmB;;;;;;0CAChC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,qBAAqB;gCACpC,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;yCAOP,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;kCACb,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,MAAM;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC3B,YAAY;oCAAE,OAAO,QAAQ;gCAAK;gCAClC,WAAW,CAAC,qGAAqG,EAC/G,cAAc,SAAS,KAAK,IAAI,GAC5B,2CACA,IACJ;gCACF,SAAS,IACP,KAAK,IAAI,KAAK,WACV,kBAAkB,QAClB,gBAAgB;;kDAGtB,8OAAC;wCAAI,WAAU;;4CACZ,KAAK,IAAI,KAAK,yBACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;qEAElB,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DAGtB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEACV,KAAK,IAAI,IAAI,KAAK,QAAQ;;;;;;kEAE7B,8OAAC;wDAAI,WAAU;;4DACZ,KAAK,IAAI,KAAK,UAAU,KAAK,IAAI,kBAChC;;kFACE,8OAAC;kFAAM,eAAe,KAAK,IAAI;;;;;;kFAC/B,8OAAC;kFAAK;;;;;;;;0EAGV,8OAAC;0EAAM,WAAW,KAAK,WAAW;;;;;;;;;;;;oDAEnC,KAAK,eAAe,kBACnB,8OAAC;wDAAE,WAAU;kEACV,KAAK,eAAe;;;;;;;;;;;;;;;;;;kDAM7B,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,CAAC;4CACR,EAAE,eAAe;4CACjB,iBAAiB;wCACnB;wCACA,WAAU;kDAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;+BArDf,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;YA+DzB,mCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,8OAAC,iIAAA,CAAA,QAAK;4BACJ,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4BAC9C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;4BACxC,WAAU;4BACV,SAAS;;;;;;sCAEX,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,qBAAqB;8CACrC;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;8CAAkB;;;;;;;;;;;;;;;;;;;;;;;YAO1C,qCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,8OAAC,iIAAA,CAAA,QAAK;4BACJ,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;4BAChD,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;4BACxC,WAAU;4BACV,SAAS;;;;;;sCAEX,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,uBAAuB;8CACvC;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;8CAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnD", "debugId": null}}, {"offset": {"line": 1158, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/DocumentenSchrijver/frontend/src/components/document-canvas.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, useRef } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { \n  Save, \n  Edit3, \n  Eye, \n  Split,\n  FileText\n} from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { \n  ResizableHandle, \n  ResizablePanel, \n  ResizablePanelGroup \n} from \"@/components/ui/resizable\";\nimport { Editor } from \"@monaco-editor/react\";\nimport ReactMarkdown from \"react-markdown\";\nimport remarkGfm from \"remark-gfm\";\nimport { toast } from \"sonner\";\n\ninterface SelectedFile {\n  path: string;\n  filename: string;\n  type: string;\n  size?: number;\n  modified_at: string;\n}\n\ninterface DocumentCanvasProps {\n  content: string;\n  onContentChange: (content: string) => void;\n  onSave: () => void;\n  websocket: WebSocket | null;\n  selectedFile: SelectedFile | null;\n}\n\ntype ViewMode = \"edit\" | \"split\" | \"preview\";\n\nexport function DocumentCanvas({\n  content,\n  onContentChange,\n  onSave,\n  websocket,\n  selectedFile,\n}: DocumentCanvasProps) {\n  const [viewMode, setViewMode] = useState<ViewMode>(\"split\");\n  const [selectedText, setSelectedText] = useState(\"\");\n  const [isAutoSaving, setIsAutoSaving] = useState(false);\n  const editorRef = useRef<any>(null);\n  const previewRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if (e.ctrlKey && e.key === \"s\") {\n        e.preventDefault();\n        onSave();\n      }\n    };\n\n    window.addEventListener(\"keydown\", handleKeyDown);\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\n  }, [onSave]);\n\n  // Auto-save functionality\n  useEffect(() => {\n    if (!selectedFile || !content) return;\n\n    const autoSaveTimer = setTimeout(() => {\n      setIsAutoSaving(true);\n      onSave();\n      setTimeout(() => setIsAutoSaving(false), 1000);\n    }, 30000); // Auto-save after 30 seconds of inactivity\n\n    return () => clearTimeout(autoSaveTimer);\n  }, [content, selectedFile, onSave]);\n\n  const handleEditorDidMount = (editor: any) => {\n    editorRef.current = editor;\n\n    // Handle text selection\n    editor.onDidChangeCursorSelection((e: any) => {\n      const selection = editor.getModel()?.getValueInRange(e.selection);\n      setSelectedText(selection || \"\");\n    });\n\n    // Configure editor theme\n    editor.updateOptions({\n      theme: \"vs-dark\",\n      fontSize: 14,\n      lineHeight: 1.6,\n      wordWrap: \"on\",\n      minimap: { enabled: false },\n      scrollBeyondLastLine: false,\n      automaticLayout: true,\n    });\n  };\n\n  const handleRewriteSelection = () => {\n    if (!selectedText || !websocket) {\n      toast.error(\"Selecteer eerst tekst om te herschrijven\");\n      return;\n    }\n\n    const message = {\n      type: \"rewrite\",\n      content: selectedText,\n      context: content,\n    };\n\n    websocket.send(JSON.stringify(message));\n    toast.info(\"AI herschrijft de geselecteerde tekst...\");\n  };\n\n  const ViewModeButtons = () => (\n    <div className=\"flex bg-muted rounded-lg p-1\">\n      <Button\n        variant={viewMode === \"edit\" ? \"default\" : \"ghost\"}\n        size=\"sm\"\n        onClick={() => setViewMode(\"edit\")}\n        className=\"h-8\"\n      >\n        <Edit3 className=\"w-4 h-4 mr-1\" />\n        Bewerken\n      </Button>\n      <Button\n        variant={viewMode === \"split\" ? \"default\" : \"ghost\"}\n        size=\"sm\"\n        onClick={() => setViewMode(\"split\")}\n        className=\"h-8\"\n      >\n        <Split className=\"w-4 h-4 mr-1\" />\n        Gesplitst\n      </Button>\n      <Button\n        variant={viewMode === \"preview\" ? \"default\" : \"ghost\"}\n        size=\"sm\"\n        onClick={() => setViewMode(\"preview\")}\n        className=\"h-8\"\n      >\n        <Eye className=\"w-4 h-4 mr-1\" />\n        Voorvertoning\n      </Button>\n    </div>\n  );\n\n  return (\n    <div className=\"h-full bg-card/30 flex flex-col\">\n      {/* Header */}\n      <div className=\"p-4 border-b flex items-center justify-between\">\n        <div className=\"flex items-center space-x-3\">\n          <FileText className=\"w-5 h-5 text-primary\" />\n          <div>\n            <h3 className=\"font-semibold\">\n              {selectedFile ? selectedFile.filename : \"Document Canvas\"}\n            </h3>\n            {selectedFile && (\n              <p className=\"text-xs text-muted-foreground\">\n                {selectedFile.path}\n              </p>\n            )}\n          </div>\n          {isAutoSaving && (\n            <Badge variant=\"outline\" className=\"text-xs\">\n              Auto-opslaan...\n            </Badge>\n          )}\n        </div>\n\n        <div className=\"flex items-center space-x-3\">\n          <ViewModeButtons />\n          \n          {selectedText && (\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={handleRewriteSelection}\n              disabled={!websocket}\n            >\n              Herschrijven\n            </Button>\n          )}\n          \n          <Button\n            onClick={onSave}\n            size=\"sm\"\n            disabled={!selectedFile}\n          >\n            <Save className=\"w-4 h-4 mr-1\" />\n            Opslaan\n          </Button>\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"flex-1 overflow-hidden\">\n        {!selectedFile ? (\n          <div className=\"flex flex-col items-center justify-center h-full text-muted-foreground\">\n            <FileText className=\"w-16 h-16 mb-4 opacity-50\" />\n            <h3 className=\"text-lg font-medium mb-2\">Geen bestand geselecteerd</h3>\n            <p className=\"text-center max-w-md\">\n              Selecteer een bestand in de file explorer om te beginnen met bewerken,\n              of maak een nieuw document aan.\n            </p>\n          </div>\n        ) : (\n          <>\n            {viewMode === \"edit\" && (\n              <div className=\"h-full\">\n                <Editor\n                  height=\"100%\"\n                  defaultLanguage=\"markdown\"\n                  value={content}\n                  onChange={(value) => onContentChange(value || \"\")}\n                  onMount={handleEditorDidMount}\n                  theme=\"vs-dark\"\n                  options={{\n                    wordWrap: \"on\",\n                    minimap: { enabled: false },\n                    fontSize: 14,\n                    lineHeight: 1.6,\n                    padding: { top: 16, bottom: 16 },\n                    fontFamily: \"'JetBrains Mono', 'Fira Code', monospace\",\n                    scrollBeyondLastLine: false,\n                    automaticLayout: true,\n                  }}\n                />\n              </div>\n            )}\n\n            {viewMode === \"preview\" && (\n              <div className=\"h-full overflow-y-auto\" ref={previewRef}>\n                <div className=\"p-6 max-w-4xl mx-auto\">\n                  <div className=\"prose prose-invert prose-lg max-w-none\">\n                    <ReactMarkdown remarkPlugins={[remarkGfm]}>\n                      {content || \"# Leeg document\\n\\nBegin met typen...\"}\n                    </ReactMarkdown>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {viewMode === \"split\" && (\n              <ResizablePanelGroup direction=\"horizontal\" className=\"h-full\">\n                <ResizablePanel defaultSize={50}>\n                  <div className=\"h-full border-r\">\n                    <Editor\n                      height=\"100%\"\n                      defaultLanguage=\"markdown\"\n                      value={content}\n                      onChange={(value) => onContentChange(value || \"\")}\n                      onMount={handleEditorDidMount}\n                      theme=\"vs-dark\"\n                      options={{\n                        wordWrap: \"on\",\n                        minimap: { enabled: false },\n                        fontSize: 14,\n                        lineHeight: 1.6,\n                        padding: { top: 16, bottom: 16 },\n                        fontFamily: \"'JetBrains Mono', 'Fira Code', monospace\",\n                        scrollBeyondLastLine: false,\n                        automaticLayout: true,\n                      }}\n                    />\n                  </div>\n                </ResizablePanel>\n\n                <ResizableHandle />\n\n                <ResizablePanel defaultSize={50}>\n                  <div className=\"h-full overflow-y-auto\" ref={previewRef}>\n                    <div className=\"p-6\">\n                      <div className=\"prose prose-invert prose-lg max-w-none\">\n                        <ReactMarkdown remarkPlugins={[remarkGfm]}>\n                          {content || \"# Leeg document\\n\\nBegin met typen...\"}\n                        </ReactMarkdown>\n                      </div>\n                    </div>\n                  </div>\n                </ResizablePanel>\n              </ResizablePanelGroup>\n            )}\n          </>\n        )}\n      </div>\n\n      {/* Selection Indicator */}\n      {selectedText && (\n        <motion.div\n          initial={{ opacity: 0, y: 10 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"px-4 py-2 bg-primary/10 border-t border-primary/30 text-primary text-sm\"\n        >\n          <span className=\"font-medium\">Geselecteerd:</span> \"\n          {selectedText.substring(0, 50)}\n          {selectedText.length > 50 ? \"...\" : \"\"}\"\n        </motion.div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAEA;AACA;AAKA;AAAA;AACA;AACA;AACA;AAtBA;;;;;;;;;;;;AA0CO,SAAS,eAAe,EAC7B,OAAO,EACP,eAAe,EACf,MAAM,EACN,SAAS,EACT,YAAY,EACQ;IACpB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IAC9B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,IAAI,EAAE,OAAO,IAAI,EAAE,GAAG,KAAK,KAAK;gBAC9B,EAAE,cAAc;gBAChB;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG;QAAC;KAAO;IAEX,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,gBAAgB,CAAC,SAAS;QAE/B,MAAM,gBAAgB,WAAW;YAC/B,gBAAgB;YAChB;YACA,WAAW,IAAM,gBAAgB,QAAQ;QAC3C,GAAG,QAAQ,2CAA2C;QAEtD,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAS;QAAc;KAAO;IAElC,MAAM,uBAAuB,CAAC;QAC5B,UAAU,OAAO,GAAG;QAEpB,wBAAwB;QACxB,OAAO,0BAA0B,CAAC,CAAC;YACjC,MAAM,YAAY,OAAO,QAAQ,IAAI,gBAAgB,EAAE,SAAS;YAChE,gBAAgB,aAAa;QAC/B;QAEA,yBAAyB;QACzB,OAAO,aAAa,CAAC;YACnB,OAAO;YACP,UAAU;YACV,YAAY;YACZ,UAAU;YACV,SAAS;gBAAE,SAAS;YAAM;YAC1B,sBAAsB;YACtB,iBAAiB;QACnB;IACF;IAEA,MAAM,yBAAyB;QAC7B,IAAI,CAAC,gBAAgB,CAAC,WAAW;YAC/B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,UAAU;YACd,MAAM;YACN,SAAS;YACT,SAAS;QACX;QAEA,UAAU,IAAI,CAAC,KAAK,SAAS,CAAC;QAC9B,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC;IACb;IAEA,MAAM,kBAAkB,kBACtB,8OAAC;YAAI,WAAU;;8BACb,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAS,aAAa,SAAS,YAAY;oBAC3C,MAAK;oBACL,SAAS,IAAM,YAAY;oBAC3B,WAAU;;sCAEV,8OAAC,0MAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;8BAGpC,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAS,aAAa,UAAU,YAAY;oBAC5C,MAAK;oBACL,SAAS,IAAM,YAAY;oBAC3B,WAAU;;sCAEV,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;8BAGpC,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAS,aAAa,YAAY,YAAY;oBAC9C,MAAK;oBACL,SAAS,IAAM,YAAY;oBAC3B,WAAU;;sCAEV,8OAAC,gMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;;IAMtC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDACX,eAAe,aAAa,QAAQ,GAAG;;;;;;oCAEzC,8BACC,8OAAC;wCAAE,WAAU;kDACV,aAAa,IAAI;;;;;;;;;;;;4BAIvB,8BACC,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;0CAAU;;;;;;;;;;;;kCAMjD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;;;;4BAEA,8BACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU,CAAC;0CACZ;;;;;;0CAKH,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,MAAK;gCACL,UAAU,CAAC;;kDAEX,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAOvC,8OAAC;gBAAI,WAAU;0BACZ,CAAC,6BACA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAG,WAAU;sCAA2B;;;;;;sCACzC,8OAAC;4BAAE,WAAU;sCAAuB;;;;;;;;;;;yCAMtC;;wBACG,aAAa,wBACZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,6KAAA,CAAA,SAAM;gCACL,QAAO;gCACP,iBAAgB;gCAChB,OAAO;gCACP,UAAU,CAAC,QAAU,gBAAgB,SAAS;gCAC9C,SAAS;gCACT,OAAM;gCACN,SAAS;oCACP,UAAU;oCACV,SAAS;wCAAE,SAAS;oCAAM;oCAC1B,UAAU;oCACV,YAAY;oCACZ,SAAS;wCAAE,KAAK;wCAAI,QAAQ;oCAAG;oCAC/B,YAAY;oCACZ,sBAAsB;oCACtB,iBAAiB;gCACnB;;;;;;;;;;;wBAKL,aAAa,2BACZ,8OAAC;4BAAI,WAAU;4BAAyB,KAAK;sCAC3C,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,wLAAA,CAAA,UAAa;wCAAC,eAAe;4CAAC,6IAAA,CAAA,UAAS;yCAAC;kDACtC,WAAW;;;;;;;;;;;;;;;;;;;;;wBAOrB,aAAa,yBACZ,8OAAC,qIAAA,CAAA,sBAAmB;4BAAC,WAAU;4BAAa,WAAU;;8CACpD,8OAAC,qIAAA,CAAA,iBAAc;oCAAC,aAAa;8CAC3B,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,6KAAA,CAAA,SAAM;4CACL,QAAO;4CACP,iBAAgB;4CAChB,OAAO;4CACP,UAAU,CAAC,QAAU,gBAAgB,SAAS;4CAC9C,SAAS;4CACT,OAAM;4CACN,SAAS;gDACP,UAAU;gDACV,SAAS;oDAAE,SAAS;gDAAM;gDAC1B,UAAU;gDACV,YAAY;gDACZ,SAAS;oDAAE,KAAK;oDAAI,QAAQ;gDAAG;gDAC/B,YAAY;gDACZ,sBAAsB;gDACtB,iBAAiB;4CACnB;;;;;;;;;;;;;;;;8CAKN,8OAAC,qIAAA,CAAA,kBAAe;;;;;8CAEhB,8OAAC,qIAAA,CAAA,iBAAc;oCAAC,aAAa;8CAC3B,cAAA,8OAAC;wCAAI,WAAU;wCAAyB,KAAK;kDAC3C,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,wLAAA,CAAA,UAAa;oDAAC,eAAe;wDAAC,6IAAA,CAAA,UAAS;qDAAC;8DACtC,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAa/B,8BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;;kCAEV,8OAAC;wBAAK,WAAU;kCAAc;;;;;;oBAAoB;oBACjD,aAAa,SAAS,CAAC,GAAG;oBAC1B,aAAa,MAAM,GAAG,KAAK,QAAQ;oBAAG;;;;;;;;;;;;;AAKjD", "debugId": null}}, {"offset": {"line": 1673, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/DocumentenSchrijver/frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1698, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/DocumentenSchrijver/frontend/src/components/agent-interface.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, useRef } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { \n  Bot, \n  Send, \n  Refresh<PERSON>w, \n  User, \n  Loader2,\n  <PERSON>ert<PERSON><PERSON>gle,\n  <PERSON>rk<PERSON>,\n  FileEdit,\n  LayoutTemplate\n} from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Card } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Separator } from \"@/components/ui/separator\";\nimport { toast } from \"sonner\";\n\ninterface Settings {\n  groq_api_key: string;\n  model_name: string;\n  max_tokens: number;\n  temperature: number;\n}\n\ninterface AgentInterfaceProps {\n  websocket: WebSocket | null;\n  onDocumentGenerated: (content: string) => void;\n  settings: Settings;\n}\n\ninterface ChatMessage {\n  type: \"user\" | \"assistant\" | \"error\";\n  content: string;\n  timestamp: Date;\n  agent?: string;\n}\n\nconst agents = {\n  writer: \"Schrijft nieuwe content en artikelen\",\n  rewriter: \"Verbetert en herschrijft bestaande tekst\",\n  structure: \"Organiseert en structureert documenten\",\n};\n\nconst agentIcons = {\n  writer: Spark<PERSON>,\n  rewriter: FileEdit,\n  structure: LayoutTemplate,\n};\n\nconst agentColors = {\n  writer: \"text-green-500\",\n  rewriter: \"text-blue-500\",\n  structure: \"text-purple-500\",\n};\n\nexport function AgentInterface({\n  websocket,\n  onDocumentGenerated,\n  settings,\n}: AgentInterfaceProps) {\n  const [selectedAgent, setSelectedAgent] = useState<keyof typeof agents>(\"writer\");\n  const [instruction, setInstruction] = useState(\"\");\n  const [chatHistory, setChatHistory] = useState<ChatMessage[]>([]);\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [streamingContent, setStreamingContent] = useState(\"\");\n  const chatEndRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    if (!websocket) return;\n\n    const handleMessage = (event: MessageEvent) => {\n      try {\n        const data = JSON.parse(event.data);\n\n        switch (data.type) {\n          case \"stream\":\n            setStreamingContent((prev) => prev + data.content);\n            break;\n\n          case \"complete\":\n            const completeMessage: ChatMessage = {\n              type: \"assistant\",\n              content: streamingContent + data.content,\n              timestamp: new Date(),\n              agent: selectedAgent,\n            };\n            setChatHistory((prev) => [...prev, completeMessage]);\n            setStreamingContent(\"\");\n            setIsGenerating(false);\n\n            if (data.replace_document) {\n              onDocumentGenerated(completeMessage.content);\n            }\n            break;\n\n          case \"error\":\n            const errorMessage: ChatMessage = {\n              type: \"error\",\n              content: data.message || \"Er is een fout opgetreden\",\n              timestamp: new Date(),\n            };\n            setChatHistory((prev) => [...prev, errorMessage]);\n            setStreamingContent(\"\");\n            setIsGenerating(false);\n            toast.error(data.message || \"Er is een fout opgetreden\");\n            break;\n        }\n      } catch (error) {\n        console.error(\"Error parsing WebSocket message:\", error);\n      }\n    };\n\n    websocket.addEventListener(\"message\", handleMessage);\n    return () => websocket.removeEventListener(\"message\", handleMessage);\n  }, [websocket, selectedAgent, streamingContent, onDocumentGenerated]);\n\n  useEffect(() => {\n    chatEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\n  }, [chatHistory, streamingContent]);\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!instruction.trim() || !websocket || isGenerating) return;\n\n    if (!settings.groq_api_key) {\n      toast.error(\"GROQ API key is niet geconfigureerd\");\n      return;\n    }\n\n    const userMessage: ChatMessage = {\n      type: \"user\",\n      content: instruction,\n      timestamp: new Date(),\n    };\n\n    setChatHistory((prev) => [...prev, userMessage]);\n\n    const message = {\n      type: \"generate\",\n      agent: selectedAgent,\n      instruction: instruction,\n      settings: {\n        model: settings.model_name,\n        max_tokens: settings.max_tokens,\n        temperature: settings.temperature,\n      },\n    };\n\n    websocket.send(JSON.stringify(message));\n    setInstruction(\"\");\n    setIsGenerating(true);\n    setStreamingContent(\"\");\n  };\n\n  const clearChat = () => {\n    setChatHistory([]);\n    setStreamingContent(\"\");\n    setIsGenerating(false);\n  };\n\n  const formatTime = (date: Date) => {\n    return date.toLocaleTimeString(\"nl-NL\", {\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n    });\n  };\n\n  const getAgentIcon = (agent: string) => {\n    const IconComponent = agentIcons[agent as keyof typeof agentIcons] || Bot;\n    return <IconComponent className=\"w-4 h-4\" />;\n  };\n\n  const getAgentColor = (agent: string) => {\n    return agentColors[agent as keyof typeof agentColors] || \"text-gray-500\";\n  };\n\n  return (\n    <div className=\"h-full bg-card/30 flex flex-col border-l\">\n      {/* Header */}\n      <div className=\"p-4 border-b\">\n        <div className=\"flex items-center justify-between mb-3\">\n          <div className=\"flex items-center space-x-2\">\n            <Bot className=\"w-5 h-5 text-primary\" />\n            <h3 className=\"font-semibold\">AI Agents</h3>\n          </div>\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={clearChat}\n            className=\"h-8 w-8\"\n          >\n            <RefreshCw className=\"w-4 h-4\" />\n          </Button>\n        </div>\n\n        {/* Agent Selector */}\n        <div>\n          <label className=\"block text-sm font-medium mb-2\">\n            Kies een agent:\n          </label>\n          <div className=\"grid gap-2\">\n            {Object.entries(agents).map(([key, description]) => {\n              const IconComponent = agentIcons[key as keyof typeof agentIcons];\n              const isSelected = selectedAgent === key;\n              \n              return (\n                <Button\n                  key={key}\n                  variant={isSelected ? \"default\" : \"outline\"}\n                  size=\"sm\"\n                  onClick={() => setSelectedAgent(key as keyof typeof agents)}\n                  className={`justify-start h-auto p-3 ${\n                    isSelected ? \"\" : \"hover:bg-accent\"\n                  }`}\n                >\n                  <div className=\"flex items-start space-x-3\">\n                    <IconComponent className={`w-4 h-4 mt-0.5 ${\n                      isSelected ? \"text-primary-foreground\" : getAgentColor(key)\n                    }`} />\n                    <div className=\"text-left\">\n                      <div className=\"font-medium capitalize\">{key}</div>\n                      <div className={`text-xs ${\n                        isSelected ? \"text-primary-foreground/80\" : \"text-muted-foreground\"\n                      }`}>\n                        {description}\n                      </div>\n                    </div>\n                  </div>\n                </Button>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n\n      {/* Chat Messages */}\n      <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n        <AnimatePresence>\n          {chatHistory.map((message, index) => (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, y: 10 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -10 }}\n              className={`flex ${\n                message.type === \"user\" ? \"justify-end\" : \"justify-start\"\n              }`}\n            >\n              <Card className={`max-w-[85%] p-3 ${\n                message.type === \"user\"\n                  ? \"bg-primary text-primary-foreground\"\n                  : message.type === \"error\"\n                  ? \"bg-destructive text-destructive-foreground\"\n                  : \"bg-muted\"\n              }`}>\n                <div className=\"flex items-center space-x-2 mb-1\">\n                  <div className=\"flex items-center space-x-1\">\n                    {message.type === \"user\" ? (\n                      <User className=\"w-3 h-3\" />\n                    ) : message.type === \"error\" ? (\n                      <AlertTriangle className=\"w-3 h-3\" />\n                    ) : (\n                      <div className={message.type === \"user\" ? \"text-primary-foreground\" : getAgentColor(message.agent || \"\")}>\n                        {getAgentIcon(message.agent || \"\")}\n                      </div>\n                    )}\n                    <span className=\"text-xs font-medium\">\n                      {message.type === \"user\"\n                        ? \"Jij\"\n                        : message.type === \"error\"\n                        ? \"Systeem\"\n                        : message.agent?.charAt(0).toUpperCase() +\n                          message.agent?.slice(1)}\n                    </span>\n                  </div>\n                  <span className=\"text-xs opacity-70\">\n                    {formatTime(message.timestamp)}\n                  </span>\n                </div>\n                <div className=\"text-sm whitespace-pre-wrap\">\n                  {message.content}\n                </div>\n              </Card>\n            </motion.div>\n          ))}\n        </AnimatePresence>\n\n        {/* Streaming Message */}\n        {isGenerating && (\n          <motion.div\n            initial={{ opacity: 0, y: 10 }}\n            animate={{ opacity: 1, y: 0 }}\n            className=\"flex justify-start\"\n          >\n            <Card className=\"max-w-[85%] p-3 bg-muted\">\n              <div className=\"flex items-center space-x-2 mb-1\">\n                <div className=\"flex items-center space-x-1\">\n                  <div className={getAgentColor(selectedAgent)}>\n                    {getAgentIcon(selectedAgent)}\n                  </div>\n                  <span className=\"text-xs font-medium\">\n                    {selectedAgent.charAt(0).toUpperCase() + selectedAgent.slice(1)}\n                  </span>\n                </div>\n                <Loader2 className=\"w-3 h-3 animate-spin text-primary\" />\n              </div>\n              <div className=\"text-sm whitespace-pre-wrap\">\n                {streamingContent}\n                <span className=\"animate-pulse\">|</span>\n              </div>\n            </Card>\n          </motion.div>\n        )}\n\n        <div ref={chatEndRef} />\n      </div>\n\n      {/* Input Form */}\n      <div className=\"p-4 border-t\">\n        <form onSubmit={handleSubmit} className=\"space-y-3\">\n          <Textarea\n            value={instruction}\n            onChange={(e) => setInstruction(e.target.value)}\n            placeholder={`Geef een opdracht aan de ${selectedAgent} agent...`}\n            rows={3}\n            disabled={isGenerating || !websocket}\n            onKeyDown={(e) => {\n              if (e.key === \"Enter\" && !e.shiftKey) {\n                e.preventDefault();\n                handleSubmit(e);\n              }\n            }}\n            className=\"resize-none\"\n          />\n          <div className=\"flex items-center justify-between\">\n            <div className=\"text-xs text-muted-foreground\">\n              Enter om te verzenden, Shift+Enter voor nieuwe regel\n            </div>\n            <Button\n              type=\"submit\"\n              disabled={!instruction.trim() || isGenerating || !websocket}\n              size=\"sm\"\n            >\n              {isGenerating ? (\n                <Loader2 className=\"w-4 h-4 animate-spin\" />\n              ) : (\n                <Send className=\"w-4 h-4\" />\n              )}\n            </Button>\n          </div>\n        </form>\n\n        {!settings.groq_api_key && (\n          <div className=\"mt-3 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg\">\n            <div className=\"flex items-center space-x-2\">\n              <AlertTriangle className=\"w-4 h-4 text-yellow-500\" />\n              <span className=\"text-xs text-yellow-600 dark:text-yellow-400\">\n                API key vereist voor AI functies\n              </span>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AAGA;AApBA;;;;;;;;;AA0CA,MAAM,SAAS;IACb,QAAQ;IACR,UAAU;IACV,WAAW;AACb;AAEA,MAAM,aAAa;IACjB,QAAQ,0MAAA,CAAA,WAAQ;IAChB,UAAU,6MAAA,CAAA,WAAQ;IAClB,WAAW,0NAAA,CAAA,iBAAc;AAC3B;AAEA,MAAM,cAAc;IAClB,QAAQ;IACR,UAAU;IACV,WAAW;AACb;AAEO,SAAS,eAAe,EAC7B,SAAS,EACT,mBAAmB,EACnB,QAAQ,EACY;IACpB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAChE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW;QAEhB,MAAM,gBAAgB,CAAC;YACrB,IAAI;gBACF,MAAM,OAAO,KAAK,KAAK,CAAC,MAAM,IAAI;gBAElC,OAAQ,KAAK,IAAI;oBACf,KAAK;wBACH,oBAAoB,CAAC,OAAS,OAAO,KAAK,OAAO;wBACjD;oBAEF,KAAK;wBACH,MAAM,kBAA+B;4BACnC,MAAM;4BACN,SAAS,mBAAmB,KAAK,OAAO;4BACxC,WAAW,IAAI;4BACf,OAAO;wBACT;wBACA,eAAe,CAAC,OAAS;mCAAI;gCAAM;6BAAgB;wBACnD,oBAAoB;wBACpB,gBAAgB;wBAEhB,IAAI,KAAK,gBAAgB,EAAE;4BACzB,oBAAoB,gBAAgB,OAAO;wBAC7C;wBACA;oBAEF,KAAK;wBACH,MAAM,eAA4B;4BAChC,MAAM;4BACN,SAAS,KAAK,OAAO,IAAI;4BACzB,WAAW,IAAI;wBACjB;wBACA,eAAe,CAAC,OAAS;mCAAI;gCAAM;6BAAa;wBAChD,oBAAoB;wBACpB,gBAAgB;wBAChB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,KAAK,OAAO,IAAI;wBAC5B;gBACJ;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;YACpD;QACF;QAEA,UAAU,gBAAgB,CAAC,WAAW;QACtC,OAAO,IAAM,UAAU,mBAAmB,CAAC,WAAW;IACxD,GAAG;QAAC;QAAW;QAAe;QAAkB;KAAoB;IAEpE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC1D,GAAG;QAAC;QAAa;KAAiB;IAElC,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC,aAAa,cAAc;QAEvD,IAAI,CAAC,SAAS,YAAY,EAAE;YAC1B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,cAA2B;YAC/B,MAAM;YACN,SAAS;YACT,WAAW,IAAI;QACjB;QAEA,eAAe,CAAC,OAAS;mBAAI;gBAAM;aAAY;QAE/C,MAAM,UAAU;YACd,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;gBACR,OAAO,SAAS,UAAU;gBAC1B,YAAY,SAAS,UAAU;gBAC/B,aAAa,SAAS,WAAW;YACnC;QACF;QAEA,UAAU,IAAI,CAAC,KAAK,SAAS,CAAC;QAC9B,eAAe;QACf,gBAAgB;QAChB,oBAAoB;IACtB;IAEA,MAAM,YAAY;QAChB,eAAe,EAAE;QACjB,oBAAoB;QACpB,gBAAgB;IAClB;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,gBAAgB,UAAU,CAAC,MAAiC,IAAI,gMAAA,CAAA,MAAG;QACzE,qBAAO,8OAAC;YAAc,WAAU;;;;;;IAClC;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAO,WAAW,CAAC,MAAkC,IAAI;IAC3D;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,8OAAC;wCAAG,WAAU;kDAAgB;;;;;;;;;;;;0CAEhC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKzB,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAiC;;;;;;0CAGlD,8OAAC;gCAAI,WAAU;0CACZ,OAAO,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,KAAK,YAAY;oCAC7C,MAAM,gBAAgB,UAAU,CAAC,IAA+B;oCAChE,MAAM,aAAa,kBAAkB;oCAErC,qBACE,8OAAC,kIAAA,CAAA,SAAM;wCAEL,SAAS,aAAa,YAAY;wCAClC,MAAK;wCACL,SAAS,IAAM,iBAAiB;wCAChC,WAAW,CAAC,yBAAyB,EACnC,aAAa,KAAK,mBAClB;kDAEF,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAc,WAAW,CAAC,eAAe,EACxC,aAAa,4BAA4B,cAAc,MACvD;;;;;;8DACF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAA0B;;;;;;sEACzC,8OAAC;4DAAI,WAAW,CAAC,QAAQ,EACvB,aAAa,+BAA+B,yBAC5C;sEACC;;;;;;;;;;;;;;;;;;uCAjBF;;;;;gCAuBX;;;;;;;;;;;;;;;;;;0BAMN,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,yLAAA,CAAA,kBAAe;kCACb,YAAY,GAAG,CAAC,CAAC,SAAS,sBACzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,MAAM;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC3B,WAAW,CAAC,KAAK,EACf,QAAQ,IAAI,KAAK,SAAS,gBAAgB,iBAC1C;0CAEF,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAW,CAAC,gBAAgB,EAChC,QAAQ,IAAI,KAAK,SACb,uCACA,QAAQ,IAAI,KAAK,UACjB,+CACA,YACJ;;sDACA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;wDACZ,QAAQ,IAAI,KAAK,uBAChB,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;mEACd,QAAQ,IAAI,KAAK,wBACnB,8OAAC,wNAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;iFAEzB,8OAAC;4DAAI,WAAW,QAAQ,IAAI,KAAK,SAAS,4BAA4B,cAAc,QAAQ,KAAK,IAAI;sEAClG,aAAa,QAAQ,KAAK,IAAI;;;;;;sEAGnC,8OAAC;4DAAK,WAAU;sEACb,QAAQ,IAAI,KAAK,SACd,QACA,QAAQ,IAAI,KAAK,UACjB,YACA,QAAQ,KAAK,EAAE,OAAO,GAAG,gBACzB,QAAQ,KAAK,EAAE,MAAM;;;;;;;;;;;;8DAG7B,8OAAC;oDAAK,WAAU;8DACb,WAAW,QAAQ,SAAS;;;;;;;;;;;;sDAGjC,8OAAC;4CAAI,WAAU;sDACZ,QAAQ,OAAO;;;;;;;;;;;;+BAxCf;;;;;;;;;;oBAgDV,8BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,WAAU;kCAEV,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAW,cAAc;8DAC3B,aAAa;;;;;;8DAEhB,8OAAC;oDAAK,WAAU;8DACb,cAAc,MAAM,CAAC,GAAG,WAAW,KAAK,cAAc,KAAK,CAAC;;;;;;;;;;;;sDAGjE,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;8CAErB,8OAAC;oCAAI,WAAU;;wCACZ;sDACD,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;kCAMxC,8OAAC;wBAAI,KAAK;;;;;;;;;;;;0BAIZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,8OAAC,oIAAA,CAAA,WAAQ;gCACP,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,aAAa,CAAC,yBAAyB,EAAE,cAAc,SAAS,CAAC;gCACjE,MAAM;gCACN,UAAU,gBAAgB,CAAC;gCAC3B,WAAW,CAAC;oCACV,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;wCACpC,EAAE,cAAc;wCAChB,aAAa;oCACf;gCACF;gCACA,WAAU;;;;;;0CAEZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAgC;;;;;;kDAG/C,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,UAAU,CAAC,YAAY,IAAI,MAAM,gBAAgB,CAAC;wCAClD,MAAK;kDAEJ,6BACC,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAEnB,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;oBAMvB,CAAC,SAAS,YAAY,kBACrB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC;oCAAK,WAAU;8CAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7E", "debugId": null}}, {"offset": {"line": 2320, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/DocumentenSchrijver/frontend/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\n          <XIcon />\n          <span className=\"sr-only\">Close</span>\n        </DialogPrimitive.Close>\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2493, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/DocumentenSchrijver/frontend/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2557, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/DocumentenSchrijver/frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2585, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/DocumentenSchrijver/frontend/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SwitchPrimitive from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Switch({\n  className,\n  ...props\n}: React.ComponentProps<typeof SwitchPrimitive.Root>) {\n  return (\n    <SwitchPrimitive.Root\n      data-slot=\"switch\"\n      className={cn(\n        \"peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <SwitchPrimitive.Thumb\n        data-slot=\"switch-thumb\"\n        className={cn(\n          \"bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0\"\n        )}\n      />\n    </SwitchPrimitive.Root>\n  )\n}\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6WACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,kKAAA,CAAA,QAAqB;YACpB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 2621, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/DocumentenSchrijver/frontend/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 2846, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/DocumentenSchrijver/frontend/src/components/settings-dialog.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { \n  Setting<PERSON>, \n  Key, \n  Folder, \n  Eye, \n  EyeOff,\n  ExternalLink\n} from \"lucide-react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Card } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Separator } from \"@/components/ui/separator\";\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogHeader,\n  DialogTitle,\n} from \"@/components/ui/dialog\";\nimport {\n  Ta<PERSON>,\n  Ta<PERSON><PERSON>ontent,\n  Ta<PERSON>List,\n  TabsTrigger,\n} from \"@/components/ui/tabs\";\nimport { Label } from \"@/components/ui/label\";\nimport { Switch } from \"@/components/ui/switch\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { toast } from \"sonner\";\n\ninterface Settings {\n  groq_api_key: string;\n  root_folder: string;\n  model_name: string;\n  max_tokens: number;\n  temperature: number;\n  auto_save_interval: number;\n  theme: string;\n  language: string;\n  enable_ai_suggestions: boolean;\n  enable_auto_backup: boolean;\n  backup_interval: number;\n  max_backups: number;\n}\n\ninterface SettingsDialogProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  settings: Settings;\n  onSave: (settings: Partial<Settings>) => void;\n}\n\nexport function SettingsDialog({\n  open,\n  onOpenChange,\n  settings,\n  onSave,\n}: SettingsDialogProps) {\n  const [showApiKey, setShowApiKey] = useState(false);\n  const [activeTab, setActiveTab] = useState(\"general\");\n\n  const { register, handleSubmit, formState: { errors }, watch, setValue } = useForm({\n    defaultValues: settings,\n  });\n\n  const onSubmit = (data: any) => {\n    onSave(data);\n    onOpenChange(false);\n    toast.success(\"Instellingen opgeslagen\");\n  };\n\n  const temperatureValue = watch(\"temperature\", 0.7);\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-2xl max-h-[80vh] overflow-hidden\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center space-x-2\">\n            <Settings className=\"w-5 h-5\" />\n            <span>Instellingen</span>\n          </DialogTitle>\n          <DialogDescription>\n            Configureer je Document Schrijver applicatie\n          </DialogDescription>\n        </DialogHeader>\n\n        <Tabs value={activeTab} onValueChange={setActiveTab} className=\"flex-1\">\n          <TabsList className=\"grid w-full grid-cols-3\">\n            <TabsTrigger value=\"general\" className=\"flex items-center space-x-2\">\n              <Settings className=\"w-4 h-4\" />\n              <span>Algemeen</span>\n            </TabsTrigger>\n            <TabsTrigger value=\"api\" className=\"flex items-center space-x-2\">\n              <Key className=\"w-4 h-4\" />\n              <span>API</span>\n            </TabsTrigger>\n            <TabsTrigger value=\"folders\" className=\"flex items-center space-x-2\">\n              <Folder className=\"w-4 h-4\" />\n              <span>Mappen</span>\n            </TabsTrigger>\n          </TabsList>\n\n          <form onSubmit={handleSubmit(onSubmit)} className=\"flex-1 overflow-y-auto\">\n            <div className=\"space-y-6 py-4\">\n              \n              {/* General Tab */}\n              <TabsContent value=\"general\" className=\"space-y-4\">\n                <div className=\"grid gap-4\">\n                  <div>\n                    <Label htmlFor=\"theme\">Thema</Label>\n                    <Select\n                      value={watch(\"theme\")}\n                      onValueChange={(value) => setValue(\"theme\", value)}\n                    >\n                      <SelectTrigger>\n                        <SelectValue />\n                      </SelectTrigger>\n                      <SelectContent>\n                        <SelectItem value=\"dark\">Donker</SelectItem>\n                        <SelectItem value=\"light\">Licht</SelectItem>\n                        <SelectItem value=\"system\">Systeem</SelectItem>\n                      </SelectContent>\n                    </Select>\n                  </div>\n\n                  <div>\n                    <Label htmlFor=\"language\">Taal</Label>\n                    <Select\n                      value={watch(\"language\")}\n                      onValueChange={(value) => setValue(\"language\", value)}\n                    >\n                      <SelectTrigger>\n                        <SelectValue />\n                      </SelectTrigger>\n                      <SelectContent>\n                        <SelectItem value=\"nl\">Nederlands</SelectItem>\n                        <SelectItem value=\"en\">English</SelectItem>\n                      </SelectContent>\n                    </Select>\n                  </div>\n\n                  <div>\n                    <Label htmlFor=\"auto_save_interval\">\n                      Auto-opslaan interval (seconden)\n                    </Label>\n                    <Input\n                      type=\"number\"\n                      min=\"10\"\n                      max=\"300\"\n                      {...register(\"auto_save_interval\", {\n                        valueAsNumber: true,\n                        min: 10,\n                        max: 300,\n                      })}\n                    />\n                    {errors.auto_save_interval && (\n                      <p className=\"text-sm text-destructive mt-1\">\n                        Interval moet tussen 10 en 300 seconden zijn\n                      </p>\n                    )}\n                  </div>\n\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"space-y-0.5\">\n                      <Label>AI suggesties</Label>\n                      <p className=\"text-sm text-muted-foreground\">\n                        Ontvang automatische suggesties tijdens het typen\n                      </p>\n                    </div>\n                    <Switch\n                      checked={watch(\"enable_ai_suggestions\")}\n                      onCheckedChange={(checked) => setValue(\"enable_ai_suggestions\", checked)}\n                    />\n                  </div>\n\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"space-y-0.5\">\n                      <Label>Automatische back-ups</Label>\n                      <p className=\"text-sm text-muted-foreground\">\n                        Maak automatisch back-ups van je documenten\n                      </p>\n                    </div>\n                    <Switch\n                      checked={watch(\"enable_auto_backup\")}\n                      onCheckedChange={(checked) => setValue(\"enable_auto_backup\", checked)}\n                    />\n                  </div>\n                </div>\n              </TabsContent>\n\n              {/* API Tab */}\n              <TabsContent value=\"api\" className=\"space-y-4\">\n                <div className=\"grid gap-4\">\n                  <div>\n                    <Label htmlFor=\"groq_api_key\">GROQ API Key</Label>\n                    <div className=\"relative\">\n                      <Input\n                        type={showApiKey ? \"text\" : \"password\"}\n                        {...register(\"groq_api_key\", {\n                          required: \"API key is vereist\",\n                        })}\n                        placeholder=\"Voer je GROQ API key in...\"\n                        className=\"pr-20\"\n                      />\n                      <div className=\"absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1\">\n                        <Button\n                          type=\"button\"\n                          variant=\"ghost\"\n                          size=\"icon\"\n                          onClick={() => setShowApiKey(!showApiKey)}\n                          className=\"h-8 w-8\"\n                        >\n                          {showApiKey ? (\n                            <EyeOff className=\"w-4 h-4\" />\n                          ) : (\n                            <Eye className=\"w-4 h-4\" />\n                          )}\n                        </Button>\n                        <Button\n                          type=\"button\"\n                          variant=\"ghost\"\n                          size=\"icon\"\n                          onClick={() => window.open(\"https://console.groq.com\", \"_blank\")}\n                          className=\"h-8 w-8\"\n                        >\n                          <ExternalLink className=\"w-4 h-4\" />\n                        </Button>\n                      </div>\n                    </div>\n                    {errors.groq_api_key && (\n                      <p className=\"text-sm text-destructive mt-1\">\n                        {errors.groq_api_key.message}\n                      </p>\n                    )}\n                    <p className=\"text-xs text-muted-foreground mt-1\">\n                      Krijg je API key van{\" \"}\n                      <a\n                        href=\"https://console.groq.com\"\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className=\"text-primary hover:underline\"\n                      >\n                        console.groq.com\n                      </a>\n                    </p>\n                  </div>\n\n                  <div>\n                    <Label htmlFor=\"model_name\">Model</Label>\n                    <Select\n                      value={watch(\"model_name\")}\n                      onValueChange={(value) => setValue(\"model_name\", value)}\n                    >\n                      <SelectTrigger>\n                        <SelectValue />\n                      </SelectTrigger>\n                      <SelectContent>\n                        <SelectItem value=\"deepseek-r1-distill-llama-70b\">\n                          DeepSeek-R1 70B\n                        </SelectItem>\n                        <SelectItem value=\"llama-3.1-70b-versatile\">\n                          Llama 3.1 70B\n                        </SelectItem>\n                        <SelectItem value=\"mixtral-8x7b-32768\">\n                          Mixtral 8x7B\n                        </SelectItem>\n                      </SelectContent>\n                    </Select>\n                  </div>\n\n                  <div>\n                    <Label htmlFor=\"max_tokens\">Max Tokens</Label>\n                    <Input\n                      type=\"number\"\n                      min=\"1000\"\n                      max=\"32000\"\n                      {...register(\"max_tokens\", {\n                        valueAsNumber: true,\n                        min: 1000,\n                        max: 32000,\n                      })}\n                    />\n                  </div>\n\n                  <div>\n                    <Label htmlFor=\"temperature\">\n                      Temperature ({temperatureValue.toFixed(1)})\n                    </Label>\n                    <div className=\"space-y-2\">\n                      <input\n                        type=\"range\"\n                        min=\"0\"\n                        max=\"2\"\n                        step=\"0.1\"\n                        value={temperatureValue}\n                        onChange={(e) => setValue(\"temperature\", parseFloat(e.target.value))}\n                        className=\"w-full h-2 bg-muted rounded-lg appearance-none cursor-pointer\"\n                      />\n                      <div className=\"flex justify-between text-xs text-muted-foreground\">\n                        <span>Conservatief</span>\n                        <span>Creatief</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </TabsContent>\n\n              {/* Folders Tab */}\n              <TabsContent value=\"folders\" className=\"space-y-4\">\n                <div className=\"grid gap-4\">\n                  <div>\n                    <Label htmlFor=\"root_folder\">Root Map</Label>\n                    <Input\n                      {...register(\"root_folder\", {\n                        required: \"Root map is vereist\",\n                      })}\n                      placeholder=\"/pad/naar/documenten\"\n                    />\n                    {errors.root_folder && (\n                      <p className=\"text-sm text-destructive mt-1\">\n                        {errors.root_folder.message}\n                      </p>\n                    )}\n                    <p className=\"text-xs text-muted-foreground mt-1\">\n                      Alle documenten worden opgeslagen in deze map\n                    </p>\n                  </div>\n\n                  <div>\n                    <Label htmlFor=\"max_backups\">Max Back-ups</Label>\n                    <Input\n                      type=\"number\"\n                      min=\"1\"\n                      max=\"50\"\n                      {...register(\"max_backups\", {\n                        valueAsNumber: true,\n                        min: 1,\n                        max: 50,\n                      })}\n                    />\n                  </div>\n\n                  <div>\n                    <Label htmlFor=\"backup_interval\">\n                      Back-up interval (seconden)\n                    </Label>\n                    <Input\n                      type=\"number\"\n                      min=\"60\"\n                      max=\"3600\"\n                      {...register(\"backup_interval\", {\n                        valueAsNumber: true,\n                        min: 60,\n                        max: 3600,\n                      })}\n                    />\n                  </div>\n                </div>\n              </TabsContent>\n            </div>\n\n            {/* Footer */}\n            <div className=\"flex justify-end space-x-3 pt-4 border-t\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => onOpenChange(false)}\n              >\n                Annuleren\n              </Button>\n              <Button type=\"submit\">Opslaan</Button>\n            </div>\n          </form>\n        </Tabs>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AAKA;AAOA;AAMA;AACA;AACA;AAOA;AAxCA;;;;;;;;;;;;;AAgEO,SAAS,eAAe,EAC7B,IAAI,EACJ,YAAY,EACZ,QAAQ,EACR,MAAM,EACc;IACpB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE;QACjF,eAAe;IACjB;IAEA,MAAM,WAAW,CAAC;QAChB,OAAO;QACP,aAAa;QACb,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,mBAAmB,MAAM,eAAe;IAE9C,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;8CAAK;;;;;;;;;;;;sCAER,8OAAC,kIAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,8OAAC,gIAAA,CAAA,OAAI;oBAAC,OAAO;oBAAW,eAAe;oBAAc,WAAU;;sCAC7D,8OAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;;sDACrC,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAM,WAAU;;sDACjC,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;;sDACrC,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAIV,8OAAC;4BAAK,UAAU,aAAa;4BAAW,WAAU;;8CAChD,8OAAC;oCAAI,WAAU;;sDAGb,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAU,WAAU;sDACrC,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAQ;;;;;;0EACvB,8OAAC,kIAAA,CAAA,SAAM;gEACL,OAAO,MAAM;gEACb,eAAe,CAAC,QAAU,SAAS,SAAS;;kFAE5C,8OAAC,kIAAA,CAAA,gBAAa;kFACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kFAEd,8OAAC,kIAAA,CAAA,gBAAa;;0FACZ,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAO;;;;;;0FACzB,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAQ;;;;;;0FAC1B,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAS;;;;;;;;;;;;;;;;;;;;;;;;kEAKjC,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAW;;;;;;0EAC1B,8OAAC,kIAAA,CAAA,SAAM;gEACL,OAAO,MAAM;gEACb,eAAe,CAAC,QAAU,SAAS,YAAY;;kFAE/C,8OAAC,kIAAA,CAAA,gBAAa;kFACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kFAEd,8OAAC,kIAAA,CAAA,gBAAa;;0FACZ,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAK;;;;;;0FACvB,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAK;;;;;;;;;;;;;;;;;;;;;;;;kEAK7B,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAqB;;;;;;0EAGpC,8OAAC,iIAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,KAAI;gEACJ,KAAI;gEACH,GAAG,SAAS,sBAAsB;oEACjC,eAAe;oEACf,KAAK;oEACL,KAAK;gEACP,EAAE;;;;;;4DAEH,OAAO,kBAAkB,kBACxB,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;kEAMjD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;kFAAC;;;;;;kFACP,8OAAC;wEAAE,WAAU;kFAAgC;;;;;;;;;;;;0EAI/C,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAS,MAAM;gEACf,iBAAiB,CAAC,UAAY,SAAS,yBAAyB;;;;;;;;;;;;kEAIpE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;kFAAC;;;;;;kFACP,8OAAC;wEAAE,WAAU;kFAAgC;;;;;;;;;;;;0EAI/C,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAS,MAAM;gEACf,iBAAiB,CAAC,UAAY,SAAS,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;sDAOrE,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAM,WAAU;sDACjC,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAe;;;;;;0EAC9B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEACJ,MAAM,aAAa,SAAS;wEAC3B,GAAG,SAAS,gBAAgB;4EAC3B,UAAU;wEACZ,EAAE;wEACF,aAAY;wEACZ,WAAU;;;;;;kFAEZ,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,kIAAA,CAAA,SAAM;gFACL,MAAK;gFACL,SAAQ;gFACR,MAAK;gFACL,SAAS,IAAM,cAAc,CAAC;gFAC9B,WAAU;0FAET,2BACC,8OAAC,0MAAA,CAAA,SAAM;oFAAC,WAAU;;;;;yGAElB,8OAAC,gMAAA,CAAA,MAAG;oFAAC,WAAU;;;;;;;;;;;0FAGnB,8OAAC,kIAAA,CAAA,SAAM;gFACL,MAAK;gFACL,SAAQ;gFACR,MAAK;gFACL,SAAS,IAAM,OAAO,IAAI,CAAC,4BAA4B;gFACvD,WAAU;0FAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;oFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;4DAI7B,OAAO,YAAY,kBAClB,8OAAC;gEAAE,WAAU;0EACV,OAAO,YAAY,CAAC,OAAO;;;;;;0EAGhC,8OAAC;gEAAE,WAAU;;oEAAqC;oEAC3B;kFACrB,8OAAC;wEACC,MAAK;wEACL,QAAO;wEACP,KAAI;wEACJ,WAAU;kFACX;;;;;;;;;;;;;;;;;;kEAML,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAa;;;;;;0EAC5B,8OAAC,kIAAA,CAAA,SAAM;gEACL,OAAO,MAAM;gEACb,eAAe,CAAC,QAAU,SAAS,cAAc;;kFAEjD,8OAAC,kIAAA,CAAA,gBAAa;kFACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kFAEd,8OAAC,kIAAA,CAAA,gBAAa;;0FACZ,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAgC;;;;;;0FAGlD,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAA0B;;;;;;0FAG5C,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAqB;;;;;;;;;;;;;;;;;;;;;;;;kEAO7C,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAa;;;;;;0EAC5B,8OAAC,iIAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,KAAI;gEACJ,KAAI;gEACH,GAAG,SAAS,cAAc;oEACzB,eAAe;oEACf,KAAK;oEACL,KAAK;gEACP,EAAE;;;;;;;;;;;;kEAIN,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;;oEAAc;oEACb,iBAAiB,OAAO,CAAC;oEAAG;;;;;;;0EAE5C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEACC,MAAK;wEACL,KAAI;wEACJ,KAAI;wEACJ,MAAK;wEACL,OAAO;wEACP,UAAU,CAAC,IAAM,SAAS,eAAe,WAAW,EAAE,MAAM,CAAC,KAAK;wEAClE,WAAU;;;;;;kFAEZ,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;0FAAK;;;;;;0FACN,8OAAC;0FAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAQhB,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAU,WAAU;sDACrC,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAc;;;;;;0EAC7B,8OAAC,iIAAA,CAAA,QAAK;gEACH,GAAG,SAAS,eAAe;oEAC1B,UAAU;gEACZ,EAAE;gEACF,aAAY;;;;;;4DAEb,OAAO,WAAW,kBACjB,8OAAC;gEAAE,WAAU;0EACV,OAAO,WAAW,CAAC,OAAO;;;;;;0EAG/B,8OAAC;gEAAE,WAAU;0EAAqC;;;;;;;;;;;;kEAKpD,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAc;;;;;;0EAC7B,8OAAC,iIAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,KAAI;gEACJ,KAAI;gEACH,GAAG,SAAS,eAAe;oEAC1B,eAAe;oEACf,KAAK;oEACL,KAAK;gEACP,EAAE;;;;;;;;;;;;kEAIN,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAkB;;;;;;0EAGjC,8OAAC,iIAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,KAAI;gEACJ,KAAI;gEACH,GAAG,SAAS,mBAAmB;oEAC9B,eAAe;oEACf,KAAK;oEACL,KAAK;gEACP,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,SAAS,IAAM,aAAa;sDAC7B;;;;;;sDAGD,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;sDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC", "debugId": null}}, {"offset": {"line": 3776, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/DocumentenSchrijver/frontend/src/components/document-writer.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { \n  <PERSON><PERSON><PERSON><PERSON>, \n  <PERSON>ting<PERSON>, \n  Wif<PERSON>, \n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>\n} from \"lucide-react\";\nimport { <PERSON>ton } from \"@/components/ui/button\";\nimport { Card } from \"@/components/ui/card\";\nimport { Separator } from \"@/components/ui/separator\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { \n  ResizableHandle, \n  ResizablePanel, \n  ResizablePanelGroup \n} from \"@/components/ui/resizable\";\nimport { Toaster } from \"@/components/ui/sonner\";\nimport { toast } from \"sonner\";\n\nimport { FileExplorer } from \"./file-explorer\";\nimport { DocumentCanvas } from \"./document-canvas\";\nimport { AgentInterface } from \"./agent-interface\";\nimport { SettingsDialog } from \"./settings-dialog\";\n\ninterface Settings {\n  groq_api_key: string;\n  root_folder: string;\n  model_name: string;\n  max_tokens: number;\n  temperature: number;\n  auto_save_interval: number;\n  theme: string;\n  language: string;\n  enable_ai_suggestions: boolean;\n  enable_auto_backup: boolean;\n  backup_interval: number;\n  max_backups: number;\n}\n\ninterface SelectedFile {\n  path: string;\n  filename: string;\n  type: string;\n  size?: number;\n  modified_at: string;\n}\n\nexport function DocumentWriter() {\n  const [selectedFile, setSelectedFile] = useState<SelectedFile | null>(null);\n  const [documentContent, setDocumentContent] = useState(\"\");\n  const [isConnected, setIsConnected] = useState(false);\n  const [websocket, setWebsocket] = useState<WebSocket | null>(null);\n  const [currentFolder, setCurrentFolder] = useState(\"\");\n  const [showSettings, setShowSettings] = useState(false);\n  const [settings, setSettings] = useState<Settings>({\n    groq_api_key: \"\",\n    root_folder: \"\",\n    model_name: \"deepseek-r1-distill-llama-70b\",\n    max_tokens: 8000,\n    temperature: 0.7,\n    auto_save_interval: 30,\n    theme: \"dark\",\n    language: \"nl\",\n    enable_ai_suggestions: true,\n    enable_auto_backup: true,\n    backup_interval: 300,\n    max_backups: 10,\n  });\n  const [connectionRetries, setConnectionRetries] = useState(0);\n\n  useEffect(() => {\n    loadSettings();\n  }, []);\n\n  useEffect(() => {\n    if (settings.groq_api_key) {\n      connectWebSocket();\n    }\n  }, [settings.groq_api_key]);\n\n  const loadSettings = async () => {\n    try {\n      const response = await fetch(\"http://localhost:8001/settings\");\n      if (response.ok) {\n        const data = await response.json();\n        setSettings(data);\n      }\n    } catch (error) {\n      console.error(\"Error loading settings:\", error);\n      toast.error(\"Kon instellingen niet laden\");\n    }\n  };\n\n  const connectWebSocket = () => {\n    if (websocket) {\n      websocket.close();\n    }\n\n    const ws = new WebSocket(\"ws://localhost:8001/ws\");\n\n    ws.onopen = () => {\n      setIsConnected(true);\n      setWebsocket(ws);\n      setConnectionRetries(0);\n      toast.success(\"Verbonden met server\");\n    };\n\n    ws.onclose = () => {\n      setIsConnected(false);\n      setWebsocket(null);\n\n      // Auto-reconnect with exponential backoff\n      if (connectionRetries < 5) {\n        const delay = Math.pow(2, connectionRetries) * 1000;\n        setTimeout(() => {\n          setConnectionRetries((prev) => prev + 1);\n          connectWebSocket();\n        }, delay);\n      } else {\n        toast.error(\"Verbinding verloren. Probeer de pagina te vernieuwen.\");\n      }\n    };\n\n    ws.onerror = (error) => {\n      console.error(\"WebSocket error:\", error);\n      toast.error(\"Verbindingsfout\");\n    };\n\n    return () => {\n      if (ws) {\n        ws.close();\n      }\n    };\n  };\n\n  const handleFileSelect = async (file: SelectedFile) => {\n    setSelectedFile(file);\n\n    if (file.type === \"file\") {\n      try {\n        const response = await fetch(`http://localhost:8001/files/${file.path}`);\n        if (response.ok) {\n          const data = await response.json();\n          setDocumentContent(data.content);\n          toast.success(`${file.filename} geopend`);\n        }\n      } catch (error) {\n        console.error(\"Error loading file:\", error);\n        toast.error(\"Kon bestand niet laden\");\n      }\n    }\n  };\n\n  const handleContentChange = (content: string) => {\n    setDocumentContent(content);\n  };\n\n  const handleSaveFile = async () => {\n    if (!selectedFile || selectedFile.type !== \"file\") return;\n\n    try {\n      const response = await fetch(`http://localhost:8001/files/${selectedFile.path}`, {\n        method: \"POST\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify({ content: documentContent }),\n      });\n\n      if (response.ok) {\n        toast.success(\"Bestand opgeslagen\");\n      } else {\n        throw new Error(\"Failed to save file\");\n      }\n    } catch (error) {\n      console.error(\"Error saving file:\", error);\n      toast.error(\"Kon bestand niet opslaan\");\n    }\n  };\n\n  const handleSettingsUpdate = async (newSettings: Partial<Settings>) => {\n    try {\n      const response = await fetch(\"http://localhost:8001/settings\", {\n        method: \"POST\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify(newSettings),\n      });\n\n      if (response.ok) {\n        setSettings({ ...settings, ...newSettings });\n        toast.success(\"Instellingen opgeslagen\");\n\n        // Reconnect if API key changed\n        if (newSettings.groq_api_key) {\n          connectWebSocket();\n        }\n      } else {\n        throw new Error(\"Failed to update settings\");\n      }\n    } catch (error) {\n      console.error(\"Error updating settings:\", error);\n      toast.error(\"Kon instellingen niet opslaan\");\n    }\n  };\n\n  return (\n    <div className=\"h-screen bg-background text-foreground flex flex-col overflow-hidden\">\n      <Toaster />\n\n      {/* Header */}\n      <motion.header\n        initial={{ y: -20, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        className=\"border-b bg-card/50 backdrop-blur-sm px-6 py-4 flex items-center justify-between\"\n      >\n        <div className=\"flex items-center space-x-3\">\n          <FileText className=\"w-8 h-8 text-primary\" />\n          <h1 className=\"text-xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent\">\n            Document Schrijver\n          </h1>\n        </div>\n\n        <div className=\"flex items-center space-x-4\">\n          {/* Connection Status */}\n          <div className=\"flex items-center space-x-2\">\n            {isConnected ? (\n              <Wifi className=\"w-5 h-5 text-green-500\" />\n            ) : (\n              <WifiOff className=\"w-5 h-5 text-red-500\" />\n            )}\n            <Badge variant={isConnected ? \"default\" : \"destructive\"}>\n              {isConnected\n                ? \"Verbonden\"\n                : connectionRetries > 0\n                ? \"Verbinden...\"\n                : \"Niet verbonden\"}\n            </Badge>\n          </div>\n\n          {/* Settings Button */}\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={() => setShowSettings(true)}\n          >\n            <Settings className=\"w-5 h-5\" />\n          </Button>\n        </div>\n      </motion.header>\n\n      {/* Main Content */}\n      <main className=\"flex-1 overflow-hidden\">\n        <ResizablePanelGroup direction=\"horizontal\" className=\"h-full\">\n          <ResizablePanel defaultSize={25} minSize={20}>\n            <FileExplorer\n              onFileSelect={handleFileSelect}\n              selectedFile={selectedFile}\n              currentFolder={currentFolder}\n              onFolderChange={setCurrentFolder}\n            />\n          </ResizablePanel>\n\n          <ResizableHandle />\n\n          <ResizablePanel defaultSize={50} minSize={30}>\n            <DocumentCanvas\n              content={documentContent}\n              onContentChange={handleContentChange}\n              onSave={handleSaveFile}\n              websocket={websocket}\n              selectedFile={selectedFile}\n            />\n          </ResizablePanel>\n\n          <ResizableHandle />\n\n          <ResizablePanel defaultSize={25} minSize={20}>\n            <AgentInterface\n              websocket={websocket}\n              onDocumentGenerated={setDocumentContent}\n              settings={settings}\n            />\n          </ResizablePanel>\n        </ResizablePanelGroup>\n      </main>\n\n      {/* Settings Dialog */}\n      <SettingsDialog\n        open={showSettings}\n        onOpenChange={setShowSettings}\n        settings={settings}\n        onSave={handleSettingsUpdate}\n      />\n\n      {/* API Key Warning */}\n      {!settings.groq_api_key && (\n        <motion.div\n          initial={{ y: 100, opacity: 0 }}\n          animate={{ y: 0, opacity: 1 }}\n          className=\"fixed bottom-4 right-4 max-w-sm\"\n        >\n          <Card className=\"p-4 border-yellow-500/50 bg-yellow-500/10\">\n            <div className=\"flex items-start space-x-3\">\n              <Bot className=\"w-6 h-6 text-yellow-500 flex-shrink-0 mt-0.5\" />\n              <div className=\"flex-1\">\n                <p className=\"font-medium text-yellow-500\">GROQ API Key vereist</p>\n                <p className=\"text-sm text-muted-foreground mt-1\">\n                  Configureer je API key in de instellingen om AI functies te gebruiken\n                </p>\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => setShowSettings(true)}\n                  className=\"mt-2 border-yellow-500/50 text-yellow-500 hover:bg-yellow-500/10\"\n                >\n                  Instellen\n                </Button>\n              </div>\n            </div>\n          </Card>\n        </motion.div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AAEA;AACA;AAKA;AACA;AAEA;AACA;AACA;AACA;AA3BA;;;;;;;;;;;;;;;AAoDO,SAAS;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,cAAc;QACd,aAAa;QACb,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,oBAAoB;QACpB,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,oBAAoB;QACpB,iBAAiB;QACjB,aAAa;IACf;IACA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,YAAY,EAAE;YACzB;QACF;IACF,GAAG;QAAC,SAAS,YAAY;KAAC;IAE1B,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,YAAY;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,WAAW;YACb,UAAU,KAAK;QACjB;QAEA,MAAM,KAAK,IAAI,UAAU;QAEzB,GAAG,MAAM,GAAG;YACV,eAAe;YACf,aAAa;YACb,qBAAqB;YACrB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;QAEA,GAAG,OAAO,GAAG;YACX,eAAe;YACf,aAAa;YAEb,0CAA0C;YAC1C,IAAI,oBAAoB,GAAG;gBACzB,MAAM,QAAQ,KAAK,GAAG,CAAC,GAAG,qBAAqB;gBAC/C,WAAW;oBACT,qBAAqB,CAAC,OAAS,OAAO;oBACtC;gBACF,GAAG;YACL,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;QAEA,GAAG,OAAO,GAAG,CAAC;YACZ,QAAQ,KAAK,CAAC,oBAAoB;YAClC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;QAEA,OAAO;YACL,IAAI,IAAI;gBACN,GAAG,KAAK;YACV;QACF;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,gBAAgB;QAEhB,IAAI,KAAK,IAAI,KAAK,QAAQ;YACxB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;gBACvE,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,mBAAmB,KAAK,OAAO;oBAC/B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,KAAK,QAAQ,CAAC,QAAQ,CAAC;gBAC1C;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uBAAuB;gBACrC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,mBAAmB;IACrB;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,gBAAgB,aAAa,IAAI,KAAK,QAAQ;QAEnD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,4BAA4B,EAAE,aAAa,IAAI,EAAE,EAAE;gBAC/E,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,SAAS;gBAAgB;YAClD;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,uBAAuB,OAAO;QAClC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,kCAAkC;gBAC7D,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,YAAY;oBAAE,GAAG,QAAQ;oBAAE,GAAG,WAAW;gBAAC;gBAC1C,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAEd,+BAA+B;gBAC/B,IAAI,YAAY,YAAY,EAAE;oBAC5B;gBACF;YACF,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,kIAAA,CAAA,UAAO;;;;;0BAGR,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;oBAAE,GAAG,CAAC;oBAAI,SAAS;gBAAE;gBAC9B,SAAS;oBAAE,GAAG;oBAAG,SAAS;gBAAE;gBAC5B,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;gCAAG,WAAU;0CAA8F;;;;;;;;;;;;kCAK9G,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;oCACZ,4BACC,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;6DAEhB,8OAAC,4MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDAErB,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAS,cAAc,YAAY;kDACvC,cACG,cACA,oBAAoB,IACpB,iBACA;;;;;;;;;;;;0CAKR,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,gBAAgB;0CAE/B,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAM1B,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,qIAAA,CAAA,sBAAmB;oBAAC,WAAU;oBAAa,WAAU;;sCACpD,8OAAC,qIAAA,CAAA,iBAAc;4BAAC,aAAa;4BAAI,SAAS;sCACxC,cAAA,8OAAC,sIAAA,CAAA,eAAY;gCACX,cAAc;gCACd,cAAc;gCACd,eAAe;gCACf,gBAAgB;;;;;;;;;;;sCAIpB,8OAAC,qIAAA,CAAA,kBAAe;;;;;sCAEhB,8OAAC,qIAAA,CAAA,iBAAc;4BAAC,aAAa;4BAAI,SAAS;sCACxC,cAAA,8OAAC,wIAAA,CAAA,iBAAc;gCACb,SAAS;gCACT,iBAAiB;gCACjB,QAAQ;gCACR,WAAW;gCACX,cAAc;;;;;;;;;;;sCAIlB,8OAAC,qIAAA,CAAA,kBAAe;;;;;sCAEhB,8OAAC,qIAAA,CAAA,iBAAc;4BAAC,aAAa;4BAAI,SAAS;sCACxC,cAAA,8OAAC,wIAAA,CAAA,iBAAc;gCACb,WAAW;gCACX,qBAAqB;gCACrB,UAAU;;;;;;;;;;;;;;;;;;;;;;0BAOlB,8OAAC,wIAAA,CAAA,iBAAc;gBACb,MAAM;gBACN,cAAc;gBACd,UAAU;gBACV,QAAQ;;;;;;YAIT,CAAC,SAAS,YAAY,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,GAAG;oBAAK,SAAS;gBAAE;gBAC9B,SAAS;oBAAE,GAAG;oBAAG,SAAS;gBAAE;gBAC5B,WAAU;0BAEV,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAA8B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAGlD,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,gBAAgB;wCAC/B,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}, {"offset": {"line": 4241, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/DocumentenSchrijver/frontend/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { DocumentWriter } from \"@/components/document-writer\";\n\nexport default function Home() {\n  return <DocumentWriter />;\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBAAO,8OAAC,wIAAA,CAAA,iBAAc;;;;;AACxB", "debugId": null}}]}