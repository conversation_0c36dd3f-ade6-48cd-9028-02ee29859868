import { useState, useEffect } from "react";
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import { motion, AnimatePresence } from "framer-motion";
import { Toaster, toast } from "react-hot-toast";
import {
  Cog6ToothIcon,
  DocumentTextIcon,
  WifiIcon,
  ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";
import DocumentCanvas from "./components/DocumentCanvas";
import AgentInterface from "./components/AgentInterface";

function App() {
  const [selectedFile, setSelectedFile] = useState(null);
  const [documentContent, setDocumentContent] = useState("");
  const [isConnected, setIsConnected] = useState(false);
  const [websocket, setWebsocket] = useState(null);
  const [currentFolder, setCurrentFolder] = useState("");
  const [showSettings, setShowSettings] = useState(false);
  const [settings, setSettings] = useState({});
  const [connectionRetries, setConnectionRetries] = useState(0);

  useEffect(() => {
    loadSettings();
  }, []);

  useEffect(() => {
    connectWebSocket();
  }, [settings]);

  const loadSettings = async () => {
    try {
      const response = await fetch("http://localhost:8001/settings");
      const data = await response.json();
      setSettings(data);
    } catch (error) {
      console.error("Error loading settings:", error);
      toast.error("Kon instellingen niet laden");
    }
  };

  const connectWebSocket = () => {
    if (websocket) {
      websocket.close();
    }

    const ws = new WebSocket("ws://localhost:8001/ws");

    ws.onopen = () => {
      setIsConnected(true);
      setWebsocket(ws);
      setConnectionRetries(0);
      toast.success("Verbonden met server");
    };

    ws.onclose = () => {
      setIsConnected(false);
      setWebsocket(null);

      // Auto-reconnect with exponential backoff
      if (connectionRetries < 5) {
        const delay = Math.pow(2, connectionRetries) * 1000;
        setTimeout(() => {
          setConnectionRetries((prev) => prev + 1);
          connectWebSocket();
        }, delay);
      } else {
        toast.error("Verbinding verloren. Probeer de pagina te vernieuwen.");
      }
    };

    ws.onerror = (error) => {
      console.error("WebSocket error:", error);
      toast.error("Verbindingsfout");
    };
  };

  const handleFileSelect = async (file) => {
    setSelectedFile(file);

    if (file.type === "file") {
      try {
        const response = await fetch(
          `http://localhost:8001/files/${file.path}`
        );
        const data = await response.json();
        setDocumentContent(data.content);
        toast.success(`${file.filename} geopend`);
      } catch (error) {
        console.error("Error loading file:", error);
        toast.error("Kon bestand niet laden");
      }
    }
  };

  const handleContentChange = (content) => {
    setDocumentContent(content);
  };

  const handleSaveFile = async () => {
    if (!selectedFile || selectedFile.type !== "file") return;

    try {
      await fetch(`http://localhost:8001/files/${selectedFile.path}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ content: documentContent }),
      });
      toast.success("Bestand opgeslagen");
    } catch (error) {
      console.error("Error saving file:", error);
      toast.error("Kon bestand niet opslaan");
    }
  };

  const handleSettingsUpdate = async (newSettings) => {
    try {
      await fetch("http://localhost:8001/settings", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(newSettings),
      });
      setSettings({ ...settings, ...newSettings });
      toast.success("Instellingen opgeslagen");

      // Reconnect if API key changed
      if (newSettings.groq_api_key) {
        connectWebSocket();
      }
    } catch (error) {
      console.error("Error updating settings:", error);
      toast.error("Kon instellingen niet opslaan");
    }
  };

  return (
    <div className="h-screen bg-gray-950 text-gray-100 flex flex-col overflow-hidden">
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: "#1f2937",
            color: "#f3f4f6",
            border: "1px solid #374151",
          },
        }}
      />

      {/* Header */}
      <motion.header
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="glass border-b border-gray-800/50 px-6 py-4 flex items-center justify-between"
      >
        <div className="flex items-center space-x-3">
          <DocumentTextIcon className="w-8 h-8 text-primary-500" />
          <h1 className="text-xl font-bold text-gradient">
            Document Schrijver
          </h1>
        </div>

        <div className="flex items-center space-x-4">
          {/* Connection Status */}
          <div className="flex items-center space-x-2">
            <WifiIcon
              className={`w-5 h-5 ${
                isConnected ? "text-success-500" : "text-error-500"
              }`}
            />
            <span
              className={`text-sm font-medium ${
                isConnected ? "text-success-500" : "text-error-500"
              }`}
            >
              {isConnected
                ? "Verbonden"
                : connectionRetries > 0
                ? "Verbinden..."
                : "Niet verbonden"}
            </span>
          </div>

          {/* Settings Button */}
          <button
            onClick={() => setShowSettings(true)}
            className="btn-ghost p-2"
            title="Instellingen"
          >
            <Cog6ToothIcon className="w-5 h-5" />
          </button>
        </div>
      </motion.header>

      {/* Main Content */}
      <main className="flex-1 overflow-hidden">
        <PanelGroup direction="horizontal" className="h-full">
          <Panel
            defaultSize={25}
            minSize={20}
            className="border-r border-gray-800"
          >
            <div className="h-full bg-gray-900 p-4">
              <h3 className="text-lg font-semibold mb-4">Bestanden</h3>
              <p className="text-gray-400">File Explorer wordt geladen...</p>
            </div>
          </Panel>

          <PanelResizeHandle className="w-1 bg-gray-800 hover:bg-gray-700 transition-colors" />

          <Panel defaultSize={50} minSize={30}>
            <DocumentCanvas
              content={documentContent}
              onContentChange={handleContentChange}
              onSave={handleSaveFile}
              websocket={websocket}
              selectedFile={selectedFile}
            />
          </Panel>

          <PanelResizeHandle className="w-1 bg-gray-800 hover:bg-gray-700 transition-colors" />

          <Panel
            defaultSize={25}
            minSize={20}
            className="border-l border-gray-800"
          >
            <AgentInterface
              websocket={websocket}
              onDocumentGenerated={setDocumentContent}
              settings={settings}
            />
          </Panel>
        </PanelGroup>
      </main>

      {/* Settings Modal */}
      <AnimatePresence>
        {showSettings && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowSettings(false)}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="card w-full max-w-md"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6">
                <h2 className="text-xl font-semibold mb-4">Instellingen</h2>
                <p className="text-gray-400">Instellingen worden geladen...</p>
                <div className="flex justify-end mt-6">
                  <button
                    onClick={() => setShowSettings(false)}
                    className="btn-secondary"
                  >
                    Sluiten
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* API Key Warning */}
      <AnimatePresence>
        {!settings.groq_api_key && (
          <motion.div
            initial={{ y: 100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: 100, opacity: 0 }}
            className="fixed bottom-4 right-4 bg-warning-600 text-white p-4 rounded-lg shadow-lg flex items-center space-x-3 max-w-sm"
          >
            <ExclamationTriangleIcon className="w-6 h-6 flex-shrink-0" />
            <div>
              <p className="font-medium">GROQ API Key vereist</p>
              <p className="text-sm opacity-90">
                Configureer je API key in de instellingen
              </p>
            </div>
            <button
              onClick={() => setShowSettings(true)}
              className="btn-ghost text-white hover:bg-warning-700 px-3 py-1 text-sm"
            >
              Instellen
            </button>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

export default App;
