import { useState, useEffect } from "react";
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import FileExplorer from "./components/FileExplorer";
import DocumentCanvas from "./components/DocumentCanvas";
import AgentInterface from "./components/AgentInterface";
import "./App.css";

function App() {
  const [selectedFile, setSelectedFile] = useState(null);
  const [documentContent, setDocumentContent] = useState("");
  const [isConnected, setIsConnected] = useState(false);
  const [websocket, setWebsocket] = useState(null);

  useEffect(() => {
    // WebSocket connection
    const ws = new WebSocket("ws://localhost:8000/ws");

    ws.onopen = () => {
      setIsConnected(true);
      setWebsocket(ws);
    };

    ws.onclose = () => {
      setIsConnected(false);
      setWebsocket(null);
    };

    ws.onerror = (error) => {
      console.error("WebSocket error:", error);
    };

    return () => {
      if (ws) {
        ws.close();
      }
    };
  }, []);

  const handleFileSelect = (file) => {
    setSelectedFile(file);
    // Load file content
    fetch(`http://localhost:8000/files/${file.filename}`)
      .then((res) => res.json())
      .then((data) => setDocumentContent(data.content))
      .catch((err) => console.error("Error loading file:", err));
  };

  const handleContentChange = (content) => {
    setDocumentContent(content);
  };

  const handleSaveFile = async () => {
    if (!selectedFile) return;

    try {
      await fetch(`http://localhost:8000/files/${selectedFile.filename}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ content: documentContent }),
      });
    } catch (err) {
      console.error("Error saving file:", err);
    }
  };

  return (
    <div className="app">
      <header className="app-header">
        <h1>Document Schrijver</h1>
        <div className="connection-status">
          <span
            className={`status-indicator ${
              isConnected ? "connected" : "disconnected"
            }`}
          >
            {isConnected ? "🟢 Verbonden" : "🔴 Niet verbonden"}
          </span>
        </div>
      </header>

      <main className="app-main">
        <PanelGroup direction="horizontal">
          <Panel defaultSize={25} minSize={20}>
            <FileExplorer
              onFileSelect={handleFileSelect}
              selectedFile={selectedFile}
            />
          </Panel>

          <PanelResizeHandle className="resize-handle" />

          <Panel defaultSize={50} minSize={30}>
            <DocumentCanvas
              content={documentContent}
              onContentChange={handleContentChange}
              onSave={handleSaveFile}
              websocket={websocket}
            />
          </Panel>

          <PanelResizeHandle className="resize-handle" />

          <Panel defaultSize={25} minSize={20}>
            <AgentInterface
              websocket={websocket}
              onDocumentGenerated={setDocumentContent}
            />
          </Panel>
        </PanelGroup>
      </main>
    </div>
  );
}

export default App;
