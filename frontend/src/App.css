.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #1a1a1a;
  color: #ffffff;
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: #2a2a2a;
  border-bottom: 1px solid #444;
}

.app-header h1 {
  margin: 0;
  font-size: 1.5rem;
  color: #ffffff;
}

.connection-status {
  display: flex;
  align-items: center;
}

.status-indicator {
  font-size: 0.9rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.status-indicator.connected {
  background-color: #1a4d1a;
  color: #4ade80;
}

.status-indicator.disconnected {
  background-color: #4d1a1a;
  color: #f87171;
}

.app-main {
  flex: 1;
  overflow: hidden;
}

.resize-handle {
  background-color: #444;
  width: 4px;
  cursor: col-resize;
  transition: background-color 0.2s;
}

.resize-handle:hover {
  background-color: #666;
}

/* Panel styling */
[data-panel] {
  background-color: #1e1e1e;
  border-right: 1px solid #444;
}

[data-panel]:last-child {
  border-right: none;
}
