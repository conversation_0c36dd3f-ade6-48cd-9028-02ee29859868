import { useState, useEffect } from "react";
import { FolderOpen, File, Plus, Trash2, RefreshCw } from "lucide-react";
import "./FileExplorer.css";

const FileExplorer = ({ onFileSelect, selectedFile }) => {
  const [files, setFiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showNewFileDialog, setShowNewFileDialog] = useState(false);
  const [newFileName, setNewFileName] = useState("");

  useEffect(() => {
    loadFiles();
  }, []);

  const loadFiles = async () => {
    setLoading(true);
    try {
      const response = await fetch("http://localhost:8001/files");
      const data = await response.json();
      setFiles(data);
    } catch (error) {
      console.error("Error loading files:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateFile = async () => {
    if (!newFileName.trim()) return;

    try {
      const filename = newFileName.endsWith(".md")
        ? newFileName
        : `${newFileName}.md`;
      await fetch(`http://localhost:8001/files/${filename}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          content: `# ${newFileName}\n\nNieuw document...`,
        }),
      });

      setNewFileName("");
      setShowNewFileDialog(false);
      loadFiles();
    } catch (error) {
      console.error("Error creating file:", error);
    }
  };

  const handleDeleteFile = async (filename) => {
    if (!confirm(`Weet je zeker dat je ${filename} wilt verwijderen?`)) return;

    try {
      await fetch(`http://localhost:8001/files/${filename}`, {
        method: "DELETE",
      });
      loadFiles();
      if (selectedFile && selectedFile.filename === filename) {
        onFileSelect(null);
      }
    } catch (error) {
      console.error("Error deleting file:", error);
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("nl-NL", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="file-explorer">
      <div className="file-explorer-header">
        <div className="header-title">
          <FolderOpen size={18} />
          <span>Documenten</span>
        </div>
        <div className="header-actions">
          <button
            className="action-btn"
            onClick={() => setShowNewFileDialog(true)}
            title="Nieuw bestand"
          >
            <Plus size={16} />
          </button>
          <button className="action-btn" onClick={loadFiles} title="Vernieuwen">
            <RefreshCw size={16} />
          </button>
        </div>
      </div>

      {showNewFileDialog && (
        <div className="new-file-dialog">
          <input
            type="text"
            placeholder="Bestandsnaam..."
            value={newFileName}
            onChange={(e) => setNewFileName(e.target.value)}
            onKeyPress={(e) => e.key === "Enter" && handleCreateFile()}
            autoFocus
          />
          <div className="dialog-actions">
            <button onClick={handleCreateFile}>Maken</button>
            <button onClick={() => setShowNewFileDialog(false)}>
              Annuleren
            </button>
          </div>
        </div>
      )}

      <div className="file-list">
        {loading ? (
          <div className="loading">Laden...</div>
        ) : files.length === 0 ? (
          <div className="empty-state">
            <p>Geen documenten gevonden</p>
            <button onClick={() => setShowNewFileDialog(true)}>
              Maak je eerste document
            </button>
          </div>
        ) : (
          files.map((file) => (
            <div
              key={file.filename}
              className={`file-item ${
                selectedFile?.filename === file.filename ? "selected" : ""
              }`}
              onClick={() => onFileSelect(file)}
            >
              <div className="file-info">
                <div className="file-name">
                  <File size={16} />
                  <span>{file.filename}</span>
                </div>
                <div className="file-meta">
                  <span className="file-size">{formatFileSize(file.size)}</span>
                  <span className="file-date">
                    {formatDate(file.modified_at)}
                  </span>
                </div>
                {file.content_preview && (
                  <div className="file-preview">{file.content_preview}</div>
                )}
              </div>
              <button
                className="delete-btn"
                onClick={(e) => {
                  e.stopPropagation();
                  handleDeleteFile(file.filename);
                }}
                title="Verwijderen"
              >
                <Trash2 size={14} />
              </button>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default FileExplorer;
