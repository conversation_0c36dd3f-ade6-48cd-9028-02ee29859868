import { useState, useEffect, useRef } from 'react'
import { Send, Bot, User, Loader, FileText, RefreshCw, Zap } from 'lucide-react'
import './AgentInterface.css'

const AgentInterface = ({ websocket, onDocumentGenerated }) => {
  const [selectedAgent, setSelectedAgent] = useState('writer')
  const [instruction, setInstruction] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [agents, setAgents] = useState({})
  const [chatHistory, setChatHistory] = useState([])
  const [streamingContent, setStreamingContent] = useState('')
  const chatEndRef = useRef(null)

  useEffect(() => {
    // Load available agents
    fetch('http://localhost:8000/agents')
      .then(res => res.json())
      .then(data => setAgents(data))
      .catch(err => console.error('Error loading agents:', err))
  }, [])

  useEffect(() => {
    // Auto-scroll chat to bottom
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [chatHistory, streamingContent])

  useEffect(() => {
    if (!websocket) return

    const handleMessage = (event) => {
      const data = JSON.parse(event.data)
      
      switch (data.type) {
        case 'stream_start':
          setIsGenerating(true)
          setStreamingContent('')
          break
          
        case 'stream_chunk':
          setStreamingContent(prev => prev + data.content)
          break
          
        case 'stream_end':
          setIsGenerating(false)
          if (streamingContent) {
            setChatHistory(prev => [...prev, {
              type: 'assistant',
              content: streamingContent,
              timestamp: new Date(),
              agent: selectedAgent
            }])
            onDocumentGenerated(streamingContent)
            setStreamingContent('')
          }
          break
          
        case 'stream_error':
          setIsGenerating(false)
          setChatHistory(prev => [...prev, {
            type: 'error',
            content: `Fout: ${data.error}`,
            timestamp: new Date()
          }])
          setStreamingContent('')
          break
      }
    }

    websocket.addEventListener('message', handleMessage)
    return () => websocket.removeEventListener('message', handleMessage)
  }, [websocket, selectedAgent, streamingContent, onDocumentGenerated])

  const handleSubmit = (e) => {
    e.preventDefault()
    if (!instruction.trim() || !websocket || isGenerating) return

    // Add user message to chat
    const userMessage = {
      type: 'user',
      content: instruction,
      timestamp: new Date(),
      agent: selectedAgent
    }
    setChatHistory(prev => [...prev, userMessage])

    // Send to websocket
    const message = {
      type: 'generate_stream',
      agent_type: selectedAgent,
      instruction: instruction,
      task_id: Date.now().toString()
    }
    
    websocket.send(JSON.stringify(message))
    setInstruction('')
  }

  const clearChat = () => {
    setChatHistory([])
    setStreamingContent('')
  }

  const getAgentIcon = (agentType) => {
    switch (agentType) {
      case 'writer': return <FileText size={16} />
      case 'rewriter': return <RefreshCw size={16} />
      case 'structure': return <Zap size={16} />
      default: return <Bot size={16} />
    }
  }

  const getAgentColor = (agentType) => {
    switch (agentType) {
      case 'writer': return '#10b981'
      case 'rewriter': return '#f59e0b'
      case 'structure': return '#8b5cf6'
      default: return '#6b7280'
    }
  }

  const formatTime = (date) => {
    return date.toLocaleTimeString('nl-NL', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="agent-interface">
      <div className="agent-header">
        <div className="header-title">
          <Bot size={18} />
          <span>AI Agents</span>
        </div>
        <button 
          className="clear-btn"
          onClick={clearChat}
          title="Chat wissen"
        >
          <RefreshCw size={16} />
        </button>
      </div>

      <div className="agent-selector">
        <label>Kies een agent:</label>
        <select 
          value={selectedAgent} 
          onChange={(e) => setSelectedAgent(e.target.value)}
        >
          {Object.entries(agents).map(([key, description]) => (
            <option key={key} value={key}>
              {key.charAt(0).toUpperCase() + key.slice(1)} - {description}
            </option>
          ))}
        </select>
      </div>

      <div className="chat-container">
        <div className="chat-messages">
          {chatHistory.map((message, index) => (
            <div key={index} className={`message ${message.type}`}>
              <div className="message-header">
                <div className="message-icon">
                  {message.type === 'user' ? (
                    <User size={16} />
                  ) : message.type === 'error' ? (
                    <span style={{ color: '#ef4444' }}>⚠️</span>
                  ) : (
                    <div style={{ color: getAgentColor(message.agent) }}>
                      {getAgentIcon(message.agent)}
                    </div>
                  )}
                </div>
                <div className="message-meta">
                  <span className="message-sender">
                    {message.type === 'user' ? 'Jij' : 
                     message.type === 'error' ? 'Systeem' :
                     message.agent?.charAt(0).toUpperCase() + message.agent?.slice(1)}
                  </span>
                  <span className="message-time">
                    {formatTime(message.timestamp)}
                  </span>
                </div>
              </div>
              <div className="message-content">
                {message.content}
              </div>
            </div>
          ))}
          
          {isGenerating && (
            <div className="message assistant streaming">
              <div className="message-header">
                <div className="message-icon">
                  <div style={{ color: getAgentColor(selectedAgent) }}>
                    {getAgentIcon(selectedAgent)}
                  </div>
                </div>
                <div className="message-meta">
                  <span className="message-sender">
                    {selectedAgent.charAt(0).toUpperCase() + selectedAgent.slice(1)}
                  </span>
                  <span className="message-time">
                    <Loader size={12} className="spinning" />
                  </span>
                </div>
              </div>
              <div className="message-content">
                {streamingContent}
                <span className="cursor">|</span>
              </div>
            </div>
          )}
          
          <div ref={chatEndRef} />
        </div>
      </div>

      <form className="input-form" onSubmit={handleSubmit}>
        <div className="input-container">
          <textarea
            value={instruction}
            onChange={(e) => setInstruction(e.target.value)}
            placeholder={`Geef een opdracht aan de ${selectedAgent} agent...`}
            rows={3}
            disabled={isGenerating}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault()
                handleSubmit(e)
              }
            }}
          />
          <button 
            type="submit" 
            disabled={!instruction.trim() || isGenerating}
            className="send-btn"
          >
            {isGenerating ? (
              <Loader size={16} className="spinning" />
            ) : (
              <Send size={16} />
            )}
          </button>
        </div>
        <div className="input-hint">
          Enter om te verzenden, Shift+Enter voor nieuwe regel
        </div>
      </form>
    </div>
  )
}

export default AgentInterface
