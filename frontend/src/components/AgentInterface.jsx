import { useState, useEffect, useRef } from "react";
import {
  Send,
  Bot,
  User,
  Loader,
  FileText,
  RefreshCw,
  Zap,
} from "lucide-react";

const AgentInterface = ({ websocket, onDocumentGenerated }) => {
  const [selectedAgent, setSelectedAgent] = useState("writer");
  const [instruction, setInstruction] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [agents, setAgents] = useState({});
  const [chatHistory, setChatHistory] = useState([]);
  const [streamingContent, setStreamingContent] = useState("");
  const chatEndRef = useRef(null);

  useEffect(() => {
    // Load available agents
    fetch("http://localhost:8001/agents")
      .then((res) => res.json())
      .then((data) => setAgents(data))
      .catch((err) => console.error("Error loading agents:", err));
  }, []);

  useEffect(() => {
    // Auto-scroll chat to bottom
    chatEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [chatHistory, streamingContent]);

  useEffect(() => {
    if (!websocket) return;

    const handleMessage = (event) => {
      const data = JSON.parse(event.data);

      switch (data.type) {
        case "stream_start":
          setIsGenerating(true);
          setStreamingContent("");
          break;

        case "stream_chunk":
          setStreamingContent((prev) => prev + data.content);
          break;

        case "stream_end":
          setIsGenerating(false);
          if (streamingContent) {
            setChatHistory((prev) => [
              ...prev,
              {
                type: "assistant",
                content: streamingContent,
                timestamp: new Date(),
                agent: selectedAgent,
              },
            ]);
            onDocumentGenerated(streamingContent);
            setStreamingContent("");
          }
          break;

        case "stream_error":
          setIsGenerating(false);
          setChatHistory((prev) => [
            ...prev,
            {
              type: "error",
              content: `Fout: ${data.error}`,
              timestamp: new Date(),
            },
          ]);
          setStreamingContent("");
          break;
      }
    };

    websocket.addEventListener("message", handleMessage);
    return () => websocket.removeEventListener("message", handleMessage);
  }, [websocket, selectedAgent, streamingContent, onDocumentGenerated]);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!instruction.trim() || !websocket || isGenerating) return;

    // Add user message to chat
    const userMessage = {
      type: "user",
      content: instruction,
      timestamp: new Date(),
      agent: selectedAgent,
    };
    setChatHistory((prev) => [...prev, userMessage]);

    // Send to websocket
    const message = {
      type: "generate_stream",
      agent_type: selectedAgent,
      instruction: instruction,
      task_id: Date.now().toString(),
    };

    websocket.send(JSON.stringify(message));
    setInstruction("");
  };

  const clearChat = () => {
    setChatHistory([]);
    setStreamingContent("");
  };

  const getAgentIcon = (agentType) => {
    switch (agentType) {
      case "writer":
        return <FileText size={16} />;
      case "rewriter":
        return <RefreshCw size={16} />;
      case "structure":
        return <Zap size={16} />;
      default:
        return <Bot size={16} />;
    }
  };

  const getAgentColor = (agentType) => {
    switch (agentType) {
      case "writer":
        return "#10b981";
      case "rewriter":
        return "#f59e0b";
      case "structure":
        return "#8b5cf6";
      default:
        return "#6b7280";
    }
  };

  const formatTime = (date) => {
    return date.toLocaleTimeString("nl-NL", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="h-full bg-gray-900 flex flex-col">
      <div className="p-4 border-b border-gray-800 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Bot size={18} className="text-blue-500" />
          <span className="font-semibold text-gray-100">AI Agents</span>
        </div>
        <button
          className="btn-ghost p-2"
          onClick={clearChat}
          title="Chat wissen"
        >
          <RefreshCw size={16} />
        </button>
      </div>

      <div className="p-4 border-b border-gray-800">
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Kies een agent:
        </label>
        <select
          value={selectedAgent}
          onChange={(e) => setSelectedAgent(e.target.value)}
          className="input text-sm"
        >
          {Object.entries(agents).map(([key, description]) => (
            <option key={key} value={key}>
              {key.charAt(0).toUpperCase() + key.slice(1)} - {description}
            </option>
          ))}
        </select>
      </div>

      <div className="flex-1 overflow-hidden">
        <div className="h-full overflow-y-auto scrollbar-thin p-4 space-y-4">
          {chatHistory.map((message, index) => (
            <div
              key={index}
              className={`flex ${
                message.type === "user" ? "justify-end" : "justify-start"
              }`}
            >
              <div
                className={`max-w-[80%] rounded-lg p-3 ${
                  message.type === "user"
                    ? "bg-blue-600 text-white"
                    : message.type === "error"
                    ? "bg-red-600 text-white"
                    : "bg-gray-800 text-gray-100"
                }`}
              >
                <div className="flex items-center space-x-2 mb-1">
                  <div className="flex items-center space-x-1">
                    {message.type === "user" ? (
                      <User size={14} />
                    ) : message.type === "error" ? (
                      <span className="text-red-200">⚠️</span>
                    ) : (
                      <div style={{ color: getAgentColor(message.agent) }}>
                        {getAgentIcon(message.agent)}
                      </div>
                    )}
                    <span className="text-xs font-medium">
                      {message.type === "user"
                        ? "Jij"
                        : message.type === "error"
                        ? "Systeem"
                        : message.agent?.charAt(0).toUpperCase() +
                          message.agent?.slice(1)}
                    </span>
                  </div>
                  <span className="text-xs opacity-70">
                    {formatTime(message.timestamp)}
                  </span>
                </div>
                <div className="text-sm whitespace-pre-wrap">
                  {message.content}
                </div>
              </div>
            </div>
          ))}

          {isGenerating && (
            <div className="flex justify-start">
              <div className="max-w-[80%] rounded-lg p-3 bg-gray-800 text-gray-100">
                <div className="flex items-center space-x-2 mb-1">
                  <div className="flex items-center space-x-1">
                    <div style={{ color: getAgentColor(selectedAgent) }}>
                      {getAgentIcon(selectedAgent)}
                    </div>
                    <span className="text-xs font-medium">
                      {selectedAgent.charAt(0).toUpperCase() +
                        selectedAgent.slice(1)}
                    </span>
                  </div>
                  <Loader size={12} className="animate-spin text-blue-500" />
                </div>
                <div className="text-sm whitespace-pre-wrap">
                  {streamingContent}
                  <span className="animate-pulse">|</span>
                </div>
              </div>
            </div>
          )}

          <div ref={chatEndRef} />
        </div>
      </div>

      <form onSubmit={handleSubmit} className="p-4 border-t border-gray-800">
        <div className="flex space-x-3">
          <textarea
            value={instruction}
            onChange={(e) => setInstruction(e.target.value)}
            placeholder={`Geef een opdracht aan de ${selectedAgent} agent...`}
            rows={3}
            disabled={isGenerating}
            onKeyDown={(e) => {
              if (e.key === "Enter" && !e.shiftKey) {
                e.preventDefault();
                handleSubmit(e);
              }
            }}
            className="input flex-1 resize-none"
          />
          <button
            type="submit"
            disabled={!instruction.trim() || isGenerating}
            className="btn-primary px-4 py-2 self-end"
          >
            {isGenerating ? (
              <Loader size={16} className="animate-spin" />
            ) : (
              <Send size={16} />
            )}
          </button>
        </div>
        <div className="text-xs text-gray-500 mt-2">
          Enter om te verzenden, Shift+Enter voor nieuwe regel
        </div>
      </form>
    </div>
  );
};

export default AgentInterface;
