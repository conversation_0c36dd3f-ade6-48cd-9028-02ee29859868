"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON>ex<PERSON>,
  Settings,
  Wifi,
  <PERSON>ifi<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>er<PERSON><PERSON>,
  Terminal,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import { Toaster } from "@/components/ui/sonner";
import { toast } from "sonner";

import { FileExplorer } from "./file-explorer";
import { DocumentCanvas } from "./document-canvas";
import { AgentInterface } from "./agent-interface";
import { SettingsDialog } from "./settings-dialog";
import { LogViewer } from "./log-viewer";

interface Settings {
  groq_api_key: string;
  root_folder: string;
  model_name: string;
  max_tokens: number;
  temperature: number;
  auto_save_interval: number;
  theme: string;
  language: string;
  enable_ai_suggestions: boolean;
  enable_auto_backup: boolean;
  backup_interval: number;
  max_backups: number;
}

interface SelectedFile {
  path: string;
  filename: string;
  type: string;
  size?: number;
  modified_at: string;
}

export function DocumentWriter() {
  const [selectedFile, setSelectedFile] = useState<SelectedFile | null>(null);
  const [documentContent, setDocumentContent] = useState("");
  const [isConnected, setIsConnected] = useState(false);
  const [websocket, setWebsocket] = useState<WebSocket | null>(null);
  const [currentFolder, setCurrentFolder] = useState("");
  const [showSettings, setShowSettings] = useState(false);
  const [rightPanelView, setRightPanelView] = useState<"agents" | "logs">(
    "agents"
  );
  const [settings, setSettings] = useState<Settings>({
    groq_api_key: "",
    root_folder: "",
    model_name: "deepseek-r1-distill-llama-70b",
    max_tokens: 8000,
    temperature: 0.7,
    auto_save_interval: 30,
    theme: "dark",
    language: "nl",
    enable_ai_suggestions: true,
    enable_auto_backup: true,
    backup_interval: 300,
    max_backups: 10,
  });
  const [connectionRetries, setConnectionRetries] = useState(0);

  useEffect(() => {
    loadSettings();
  }, []);

  useEffect(() => {
    if (settings.groq_api_key) {
      connectWebSocket();
    }
  }, [settings.groq_api_key]);

  const loadSettings = async () => {
    try {
      const response = await fetch("http://localhost:8001/settings");
      if (response.ok) {
        const data = await response.json();
        setSettings(data);
      }
    } catch (error) {
      console.error("Error loading settings:", error);
      toast.error("Kon instellingen niet laden");
    }
  };

  const connectWebSocket = () => {
    if (websocket) {
      websocket.close();
    }

    const ws = new WebSocket("ws://localhost:8001/ws");

    ws.onopen = () => {
      setIsConnected(true);
      setWebsocket(ws);
      setConnectionRetries(0);
      toast.success("Verbonden met server");
    };

    ws.onclose = () => {
      setIsConnected(false);
      setWebsocket(null);

      // Auto-reconnect with exponential backoff
      if (connectionRetries < 5) {
        const delay = Math.pow(2, connectionRetries) * 1000;
        setTimeout(() => {
          setConnectionRetries((prev) => prev + 1);
          connectWebSocket();
        }, delay);
      } else {
        toast.error("Verbinding verloren. Probeer de pagina te vernieuwen.");
      }
    };

    ws.onerror = (error) => {
      console.error("WebSocket error:", error);
      toast.error("Verbindingsfout");
    };

    return () => {
      if (ws) {
        ws.close();
      }
    };
  };

  const handleFileSelect = async (file: SelectedFile) => {
    setSelectedFile(file);

    if (file.type === "file") {
      try {
        const response = await fetch(
          `http://localhost:8001/files/${file.path}`
        );
        if (response.ok) {
          const data = await response.json();
          setDocumentContent(data.content);
          toast.success(`${file.filename} geopend`);
        }
      } catch (error) {
        console.error("Error loading file:", error);
        toast.error("Kon bestand niet laden");
      }
    }
  };

  const handleContentChange = (content: string) => {
    setDocumentContent(content);
  };

  const handleSaveFile = async () => {
    if (!selectedFile || selectedFile.type !== "file") return;

    try {
      const response = await fetch(
        `http://localhost:8001/files/${selectedFile.path}`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ content: documentContent }),
        }
      );

      if (response.ok) {
        toast.success("Bestand opgeslagen");
      } else {
        throw new Error("Failed to save file");
      }
    } catch (error) {
      console.error("Error saving file:", error);
      toast.error("Kon bestand niet opslaan");
    }
  };

  const handleSettingsUpdate = async (newSettings: Partial<Settings>) => {
    try {
      const response = await fetch("http://localhost:8001/settings", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(newSettings),
      });

      if (response.ok) {
        setSettings({ ...settings, ...newSettings });
        toast.success("Instellingen opgeslagen");

        // Reconnect if API key changed
        if (newSettings.groq_api_key) {
          connectWebSocket();
        }
      } else {
        throw new Error("Failed to update settings");
      }
    } catch (error) {
      console.error("Error updating settings:", error);
      toast.error("Kon instellingen niet opslaan");
    }
  };

  return (
    <div className="h-screen bg-background text-foreground flex flex-col overflow-hidden">
      <Toaster />

      {/* Header */}
      <motion.header
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="border-b bg-card/50 backdrop-blur-sm px-6 py-4 flex items-center justify-between"
      >
        <div className="flex items-center space-x-3">
          <FileText className="w-8 h-8 text-primary" />
          <h1 className="text-xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
            Document Schrijver
          </h1>
        </div>

        <div className="flex items-center space-x-4">
          {/* Connection Status */}
          <div className="flex items-center space-x-2">
            {isConnected ? (
              <Wifi className="w-5 h-5 text-green-500" />
            ) : (
              <WifiOff className="w-5 h-5 text-red-500" />
            )}
            <Badge variant={isConnected ? "default" : "destructive"}>
              {isConnected
                ? "Verbonden"
                : connectionRetries > 0
                ? "Verbinden..."
                : "Niet verbonden"}
            </Badge>
          </div>

          {/* Right Panel Toggle */}
          <div className="flex bg-muted rounded-lg p-1">
            <Button
              variant={rightPanelView === "agents" ? "default" : "ghost"}
              size="sm"
              onClick={() => setRightPanelView("agents")}
              className="h-8"
            >
              <Bot className="w-4 h-4 mr-1" />
              AI
            </Button>
            <Button
              variant={rightPanelView === "logs" ? "default" : "ghost"}
              size="sm"
              onClick={() => setRightPanelView("logs")}
              className="h-8"
            >
              <Terminal className="w-4 h-4 mr-1" />
              Logs
            </Button>
          </div>

          {/* Settings Button */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setShowSettings(true)}
          >
            <Settings className="w-5 h-5" />
          </Button>
        </div>
      </motion.header>

      {/* Main Content */}
      <main className="flex-1 overflow-hidden">
        <ResizablePanelGroup direction="horizontal" className="h-full">
          <ResizablePanel defaultSize={25} minSize={20}>
            <FileExplorer
              onFileSelect={handleFileSelect}
              selectedFile={selectedFile}
              currentFolder={currentFolder}
              onFolderChange={setCurrentFolder}
            />
          </ResizablePanel>

          <ResizableHandle />

          <ResizablePanel defaultSize={50} minSize={30}>
            <DocumentCanvas
              content={documentContent}
              onContentChange={handleContentChange}
              onSave={handleSaveFile}
              websocket={websocket}
              selectedFile={selectedFile}
            />
          </ResizablePanel>

          <ResizableHandle />

          <ResizablePanel defaultSize={25} minSize={20}>
            {rightPanelView === "agents" ? (
              <AgentInterface
                websocket={websocket}
                onDocumentGenerated={setDocumentContent}
                settings={settings}
              />
            ) : (
              <LogViewer />
            )}
          </ResizablePanel>
        </ResizablePanelGroup>
      </main>

      {/* Settings Dialog */}
      <SettingsDialog
        open={showSettings}
        onOpenChange={setShowSettings}
        settings={settings}
        onSave={handleSettingsUpdate}
      />

      {/* API Key Warning */}
      {!settings.groq_api_key && (
        <motion.div
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="fixed bottom-4 right-4 max-w-sm"
        >
          <Card className="p-4 border-yellow-500/50 bg-yellow-500/10">
            <div className="flex items-start space-x-3">
              <Bot className="w-6 h-6 text-yellow-500 flex-shrink-0 mt-0.5" />
              <div className="flex-1">
                <p className="font-medium text-yellow-500">
                  GROQ API Key vereist
                </p>
                <p className="text-sm text-muted-foreground mt-1">
                  Configureer je API key in de instellingen om AI functies te
                  gebruiken
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowSettings(true)}
                  className="mt-2 border-yellow-500/50 text-yellow-500 hover:bg-yellow-500/10"
                >
                  Instellen
                </Button>
              </div>
            </div>
          </Card>
        </motion.div>
      )}
    </div>
  );
}
