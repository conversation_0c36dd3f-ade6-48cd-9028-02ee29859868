.agent-interface {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #1e1e1e;
}

.agent-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #2a2a2a;
  border-bottom: 1px solid #444;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #ffffff;
}

.clear-btn {
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s;
}

.clear-btn:hover {
  background-color: #444;
  color: #ffffff;
}

.agent-selector {
  padding: 1rem;
  border-bottom: 1px solid #444;
}

.agent-selector label {
  display: block;
  margin-bottom: 0.5rem;
  color: #ffffff;
  font-size: 0.875rem;
  font-weight: 500;
}

.agent-selector select {
  width: 100%;
  padding: 0.5rem;
  background-color: #1a1a1a;
  border: 1px solid #444;
  border-radius: 4px;
  color: #ffffff;
  font-size: 0.875rem;
}

.agent-selector select:focus {
  outline: none;
  border-color: #0ea5e9;
}

.chat-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.message {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.message.user {
  align-items: flex-end;
}

.message.assistant {
  align-items: flex-start;
}

.message.error {
  align-items: center;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
}

.message.user .message-header {
  flex-direction: row-reverse;
}

.message-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #374151;
}

.message.user .message-icon {
  background-color: #1e40af;
}

.message-meta {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.message.user .message-meta {
  align-items: flex-end;
}

.message-sender {
  font-weight: 500;
  color: #ffffff;
}

.message-time {
  color: #888;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.message-content {
  max-width: 85%;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.message.user .message-content {
  background-color: #1e40af;
  color: white;
  border-bottom-right-radius: 4px;
}

.message.assistant .message-content {
  background-color: #374151;
  color: #ffffff;
  border-bottom-left-radius: 4px;
}

.message.error .message-content {
  background-color: #7f1d1d;
  color: #fecaca;
  border-radius: 8px;
  text-align: center;
  max-width: 100%;
}

.message.streaming .message-content {
  position: relative;
}

.cursor {
  animation: blink 1s infinite;
  color: #0ea5e9;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.input-form {
  padding: 1rem;
  border-top: 1px solid #444;
  background-color: #2a2a2a;
}

.input-container {
  display: flex;
  gap: 0.5rem;
  align-items: flex-end;
}

.input-container textarea {
  flex: 1;
  padding: 0.75rem;
  background-color: #1a1a1a;
  border: 1px solid #444;
  border-radius: 8px;
  color: #ffffff;
  resize: none;
  font-family: inherit;
  font-size: 0.875rem;
  line-height: 1.4;
}

.input-container textarea:focus {
  outline: none;
  border-color: #0ea5e9;
}

.input-container textarea:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.send-btn {
  background-color: #0ea5e9;
  color: white;
  border: none;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
  min-width: 44px;
  height: 44px;
}

.send-btn:hover:not(:disabled) {
  background-color: #0284c7;
}

.send-btn:disabled {
  background-color: #374151;
  cursor: not-allowed;
}

.input-hint {
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #888;
  text-align: center;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Scrollbar styling */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #1a1a1a;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #444;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #666;
}
