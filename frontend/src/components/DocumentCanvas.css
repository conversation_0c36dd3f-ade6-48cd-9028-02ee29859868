.document-canvas {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #1e1e1e;
}

.canvas-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #2a2a2a;
  border-bottom: 1px solid #444;
}

.canvas-title h3 {
  margin: 0;
  color: #ffffff;
  font-size: 1.1rem;
}

.canvas-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.view-mode-buttons {
  display: flex;
  background-color: #1e1e1e;
  border-radius: 6px;
  padding: 2px;
  border: 1px solid #444;
}

.view-mode-buttons button {
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-mode-buttons button:hover {
  color: #ffffff;
  background-color: #444;
}

.view-mode-buttons button.active {
  background-color: #0ea5e9;
  color: white;
}

.rewrite-btn {
  background-color: #f59e0b;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s;
}

.rewrite-btn:hover {
  background-color: #d97706;
}

.save-btn {
  background-color: #10b981;
  color: white;
  border: none;
  padding: 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.2s;
}

.save-btn:hover {
  background-color: #059669;
}

.canvas-content {
  flex: 1;
  overflow: hidden;
}

.editor-container {
  height: 100%;
  background-color: #1e1e1e;
}

.preview-container {
  height: 100%;
  overflow-y: auto;
  background-color: #1a1a1a;
  padding: 2rem;
}

.markdown-content {
  max-width: 800px;
  margin: 0 auto;
  color: #ffffff;
  line-height: 1.7;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  color: #ffffff;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.markdown-content h1 {
  font-size: 2.25rem;
  border-bottom: 2px solid #444;
  padding-bottom: 0.5rem;
}

.markdown-content h2 {
  font-size: 1.875rem;
  border-bottom: 1px solid #444;
  padding-bottom: 0.25rem;
}

.markdown-content h3 {
  font-size: 1.5rem;
}

.markdown-content p {
  margin-bottom: 1rem;
  color: #e5e5e5;
}

.markdown-content ul,
.markdown-content ol {
  margin-bottom: 1rem;
  padding-left: 2rem;
}

.markdown-content li {
  margin-bottom: 0.5rem;
  color: #e5e5e5;
}

.markdown-content blockquote {
  border-left: 4px solid #0ea5e9;
  padding-left: 1rem;
  margin: 1rem 0;
  color: #d1d5db;
  font-style: italic;
}

.markdown-content code {
  background-color: #374151;
  color: #f9fafb;
  padding: 0.125rem 0.25rem;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}

.markdown-content pre {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 1rem;
  border-radius: 6px;
  overflow-x: auto;
  margin: 1rem 0;
}

.markdown-content pre code {
  background: none;
  padding: 0;
}

.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.markdown-content th,
.markdown-content td {
  border: 1px solid #444;
  padding: 0.75rem;
  text-align: left;
}

.markdown-content th {
  background-color: #374151;
  font-weight: 600;
}

.markdown-content a {
  color: #60a5fa;
  text-decoration: none;
}

.markdown-content a:hover {
  text-decoration: underline;
}

.canvas-resize-handle {
  background-color: #444;
  width: 2px;
  cursor: col-resize;
  transition: background-color 0.2s;
}

.canvas-resize-handle:hover {
  background-color: #666;
}

.selection-indicator {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  background-color: #f59e0b;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

/* User selection styling */
.markdown-content ::selection {
  background-color: #3b82f6;
  color: white;
}

.markdown-content ::-moz-selection {
  background-color: #3b82f6;
  color: white;
}
