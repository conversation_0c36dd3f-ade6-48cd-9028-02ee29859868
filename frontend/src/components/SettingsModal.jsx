import { useState } from 'react'
import { motion } from 'framer-motion'
import { useForm } from 'react-hook-form'
import { 
  XMarkIcon, 
  KeyIcon, 
  FolderIcon, 
  CogIcon,
  EyeIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline'

const SettingsModal = ({ settings, onClose, onSave }) => {
  const [showApiKey, setShowApiKey] = useState(false)
  const [activeTab, setActiveTab] = useState('general')
  
  const { register, handleSubmit, formState: { errors } } = useForm({
    defaultValues: settings
  })

  const onSubmit = (data) => {
    onSave(data)
    onClose()
  }

  const tabs = [
    { id: 'general', name: '<PERSON><PERSON>mee<PERSON>', icon: CogIcon },
    { id: 'api', name: 'API', icon: KeyIcon },
    { id: 'folders', name: 'Mappen', icon: FolderIcon }
  ]

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.95, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.95, opacity: 0 }}
        className="card w-full max-w-2xl max-h-[80vh] overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-800">
          <h2 className="text-xl font-semibold text-gray-100">Instellingen</h2>
          <button
            onClick={onClose}
            className="btn-ghost p-2"
          >
            <XMarkIcon className="w-5 h-5" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-800">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-6 py-3 text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'text-primary-400 border-b-2 border-primary-400'
                  : 'text-gray-400 hover:text-gray-300'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span>{tab.name}</span>
            </button>
          ))}
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit(onSubmit)} className="flex-1 overflow-y-auto">
          <div className="p-6 space-y-6">
            
            {/* General Tab */}
            {activeTab === 'general' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Thema
                  </label>
                  <select
                    {...register('theme')}
                    className="input"
                  >
                    <option value="dark">Donker</option>
                    <option value="light">Licht</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Taal
                  </label>
                  <select
                    {...register('language')}
                    className="input"
                  >
                    <option value="nl">Nederlands</option>
                    <option value="en">English</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Auto-opslaan interval (seconden)
                  </label>
                  <input
                    type="number"
                    min="10"
                    max="300"
                    {...register('auto_save_interval', { 
                      valueAsNumber: true,
                      min: 10,
                      max: 300 
                    })}
                    className="input"
                  />
                  {errors.auto_save_interval && (
                    <p className="text-error-400 text-sm mt-1">
                      Interval moet tussen 10 en 300 seconden zijn
                    </p>
                  )}
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="enable_ai_suggestions"
                    {...register('enable_ai_suggestions')}
                    className="w-4 h-4 text-primary-600 bg-gray-800 border-gray-600 rounded focus:ring-primary-500"
                  />
                  <label htmlFor="enable_ai_suggestions" className="text-sm text-gray-300">
                    AI suggesties inschakelen
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="enable_auto_backup"
                    {...register('enable_auto_backup')}
                    className="w-4 h-4 text-primary-600 bg-gray-800 border-gray-600 rounded focus:ring-primary-500"
                  />
                  <label htmlFor="enable_auto_backup" className="text-sm text-gray-300">
                    Automatische back-ups
                  </label>
                </div>
              </div>
            )}

            {/* API Tab */}
            {activeTab === 'api' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    GROQ API Key
                  </label>
                  <div className="relative">
                    <input
                      type={showApiKey ? 'text' : 'password'}
                      {...register('groq_api_key', { 
                        required: 'API key is vereist' 
                      })}
                      className="input pr-10"
                      placeholder="Voer je GROQ API key in..."
                    />
                    <button
                      type="button"
                      onClick={() => setShowApiKey(!showApiKey)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
                    >
                      {showApiKey ? (
                        <EyeSlashIcon className="w-4 h-4" />
                      ) : (
                        <EyeIcon className="w-4 h-4" />
                      )}
                    </button>
                  </div>
                  {errors.groq_api_key && (
                    <p className="text-error-400 text-sm mt-1">
                      {errors.groq_api_key.message}
                    </p>
                  )}
                  <p className="text-gray-500 text-xs mt-1">
                    Krijg je API key van{' '}
                    <a 
                      href="https://console.groq.com" 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-primary-400 hover:text-primary-300"
                    >
                      console.groq.com
                    </a>
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Model
                  </label>
                  <select
                    {...register('model_name')}
                    className="input"
                  >
                    <option value="deepseek-r1-distill-llama-70b">DeepSeek-R1 70B</option>
                    <option value="llama-3.1-70b-versatile">Llama 3.1 70B</option>
                    <option value="mixtral-8x7b-32768">Mixtral 8x7B</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Max Tokens
                  </label>
                  <input
                    type="number"
                    min="1000"
                    max="32000"
                    {...register('max_tokens', { 
                      valueAsNumber: true,
                      min: 1000,
                      max: 32000 
                    })}
                    className="input"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Temperature ({register('temperature').value || 0.7})
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="2"
                    step="0.1"
                    {...register('temperature', { valueAsNumber: true })}
                    className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>Conservatief</span>
                    <span>Creatief</span>
                  </div>
                </div>
              </div>
            )}

            {/* Folders Tab */}
            {activeTab === 'folders' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Root Map
                  </label>
                  <input
                    type="text"
                    {...register('root_folder', { 
                      required: 'Root map is vereist' 
                    })}
                    className="input"
                    placeholder="/pad/naar/documenten"
                  />
                  {errors.root_folder && (
                    <p className="text-error-400 text-sm mt-1">
                      {errors.root_folder.message}
                    </p>
                  )}
                  <p className="text-gray-500 text-xs mt-1">
                    Alle documenten worden opgeslagen in deze map
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Max Back-ups
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="50"
                    {...register('max_backups', { 
                      valueAsNumber: true,
                      min: 1,
                      max: 50 
                    })}
                    className="input"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Back-up interval (seconden)
                  </label>
                  <input
                    type="number"
                    min="60"
                    max="3600"
                    {...register('backup_interval', { 
                      valueAsNumber: true,
                      min: 60,
                      max: 3600 
                    })}
                    className="input"
                  />
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex justify-end space-x-3 p-6 border-t border-gray-800">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary"
            >
              Annuleren
            </button>
            <button
              type="submit"
              className="btn-primary"
            >
              Opslaan
            </button>
          </div>
        </form>
      </motion.div>
    </motion.div>
  )
}

export default SettingsModal
