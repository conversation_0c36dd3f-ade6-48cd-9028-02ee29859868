"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  FolderOpen,
  File,
  Plus,
  Trash2,
  RefreshCw,
  Search,
  Home,
  ChevronRight,
  FileText,
  Folder,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";

interface FileItem {
  path: string;
  name: string;
  filename?: string;
  type: "file" | "folder";
  size?: number;
  modified_at: string;
  content_preview?: string;
}

interface SelectedFile {
  path: string;
  filename: string;
  type: string;
  size?: number;
  modified_at: string;
}

interface FileExplorerProps {
  onFileSelect: (file: SelectedFile) => void;
  selectedFile: SelectedFile | null;
  currentFolder: string;
  onFolderChange: (folder: string) => void;
  refreshTrigger?: number; // Add refresh trigger
}

export function FileExplorer({
  onFileSelect,
  selectedFile,
  currentFolder,
  onFolderChange,
  refreshTrigger,
}: FileExplorerProps) {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [folders, setFolders] = useState<FileItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<FileItem[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showNewFileDialog, setShowNewFileDialog] = useState(false);
  const [showNewFolderDialog, setShowNewFolderDialog] = useState(false);
  const [newFileName, setNewFileName] = useState("");
  const [newFolderName, setNewFolderName] = useState("");

  useEffect(() => {
    loadFilesAndFolders();
  }, [currentFolder, refreshTrigger]);

  useEffect(() => {
    if (searchQuery.length >= 2) {
      performSearch();
    } else {
      setSearchResults([]);
      setIsSearching(false);
    }
  }, [searchQuery]);

  const loadFilesAndFolders = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `http://localhost:8001/files?folder_path=${currentFolder}`
      );
      if (response.ok) {
        const data = await response.json();
        setFiles(data.files || []);
        setFolders(data.folders || []);
      }
    } catch (error) {
      console.error("Error loading files:", error);
      toast.error("Kon bestanden niet laden");
    } finally {
      setLoading(false);
    }
  };

  const performSearch = async () => {
    setIsSearching(true);
    try {
      const response = await fetch(
        `http://localhost:8001/search?query=${encodeURIComponent(
          searchQuery
        )}&folder_path=${currentFolder}`
      );
      if (response.ok) {
        const data = await response.json();
        setSearchResults(data);
      }
    } catch (error) {
      console.error("Error searching:", error);
      toast.error("Zoeken mislukt");
    } finally {
      setIsSearching(false);
    }
  };

  const handleCreateFile = async () => {
    if (!newFileName.trim()) return;

    try {
      const filename = newFileName.endsWith(".md")
        ? newFileName
        : `${newFileName}.md`;
      const filePath = currentFolder
        ? `${currentFolder}/${filename}`
        : filename;

      const response = await fetch(`http://localhost:8001/files/${filePath}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          content: `# ${newFileName}\n\nNieuw document...`,
        }),
      });

      if (response.ok) {
        setNewFileName("");
        setShowNewFileDialog(false);
        loadFilesAndFolders();
        toast.success("Bestand aangemaakt");
      }
    } catch (error) {
      console.error("Error creating file:", error);
      toast.error("Kon bestand niet aanmaken");
    }
  };

  const handleCreateFolder = async () => {
    if (!newFolderName.trim()) return;

    try {
      const response = await fetch("http://localhost:8001/folders", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          folder_path: currentFolder,
          folder_name: newFolderName,
        }),
      });

      if (response.ok) {
        setNewFolderName("");
        setShowNewFolderDialog(false);
        loadFilesAndFolders();
        toast.success("Map aangemaakt");
      }
    } catch (error) {
      console.error("Error creating folder:", error);
      toast.error("Kon map niet aanmaken");
    }
  };

  const handleDeleteItem = async (item: FileItem) => {
    if (!confirm(`Weet je zeker dat je ${item.name} wilt verwijderen?`)) return;

    try {
      const endpoint = item.type === "folder" ? "folders" : "files";
      const response = await fetch(
        `http://localhost:8001/${endpoint}/${item.path}`,
        {
          method: "DELETE",
        }
      );

      if (response.ok) {
        loadFilesAndFolders();
        toast.success(
          `${item.type === "folder" ? "Map" : "Bestand"} verwijderd`
        );

        if (selectedFile && selectedFile.path === item.path) {
          onFileSelect(null as any);
        }
      }
    } catch (error) {
      console.error("Error deleting item:", error);
      toast.error("Kon item niet verwijderen");
    }
  };

  const handleFolderClick = (folder: FileItem) => {
    onFolderChange(folder.path);
  };

  const handleFileClick = (file: FileItem) => {
    onFileSelect({
      path: file.path,
      filename: file.name || file.filename || "",
      type: file.type,
      size: file.size,
      modified_at: file.modified_at,
    });
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes || bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("nl-NL", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const displayItems =
    searchQuery.length >= 2 ? searchResults : [...folders, ...files];

  return (
    <div className="h-full bg-card/30 flex flex-col border-r">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <FolderOpen className="w-5 h-5 text-primary" />
            <h3 className="font-semibold">Bestanden</h3>
          </div>
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setShowNewFileDialog(true)}
              className="h-8 w-8"
            >
              <FileText className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setShowNewFolderDialog(true)}
              className="h-8 w-8"
            >
              <Folder className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={loadFilesAndFolders}
              className="h-8 w-8"
            >
              <RefreshCw className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <Input
            placeholder="Zoeken..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Breadcrumb */}
        {currentFolder && (
          <div className="flex items-center space-x-1 mt-2 text-sm text-muted-foreground">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onFolderChange("")}
              className="h-6 px-2"
            >
              <Home className="w-3 h-3" />
            </Button>
            {currentFolder.split("/").map((folder, index, array) => (
              <div key={index} className="flex items-center space-x-1">
                <ChevronRight className="w-3 h-3" />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() =>
                    onFolderChange(array.slice(0, index + 1).join("/"))
                  }
                  className="h-6 px-2 text-xs"
                >
                  {folder}
                </Button>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {loading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : displayItems.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-32 text-muted-foreground p-4">
            {searchQuery ? (
              <>
                <Search className="w-8 h-8 mb-2" />
                <p className="text-center">Geen resultaten gevonden</p>
              </>
            ) : (
              <>
                <Folder className="w-8 h-8 mb-2" />
                <p className="text-center mb-2">Geen bestanden gevonden</p>
                <Button
                  onClick={() => setShowNewFileDialog(true)}
                  size="sm"
                  className="text-xs"
                >
                  Maak je eerste document
                </Button>
              </>
            )}
          </div>
        ) : (
          <div className="p-2">
            <AnimatePresence>
              {displayItems.map((item, index) => (
                <motion.div
                  key={item.path}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ delay: index * 0.05 }}
                  className={`group flex items-center justify-between p-3 rounded-lg cursor-pointer transition-all hover:bg-accent ${
                    selectedFile?.path === item.path
                      ? "bg-primary/10 border border-primary/30"
                      : ""
                  }`}
                  onClick={() =>
                    item.type === "folder"
                      ? handleFolderClick(item)
                      : handleFileClick(item)
                  }
                >
                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    {item.type === "folder" ? (
                      <Folder className="w-5 h-5 text-yellow-500 flex-shrink-0" />
                    ) : (
                      <FileText className="w-5 h-5 text-blue-400 flex-shrink-0" />
                    )}

                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">
                        {item.name || item.filename}
                      </p>
                      <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                        {item.type === "file" && item.size && (
                          <>
                            <span>{formatFileSize(item.size)}</span>
                            <span>•</span>
                          </>
                        )}
                        <span>{formatDate(item.modified_at)}</span>
                      </div>
                      {item.content_preview && (
                        <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                          {item.content_preview}
                        </p>
                      )}
                    </div>
                  </div>

                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteItem(item);
                    }}
                    className="opacity-0 group-hover:opacity-100 h-8 w-8 text-destructive hover:text-destructive"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        )}
      </div>

      {/* New File Dialog */}
      {showNewFileDialog && (
        <div className="absolute inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <Card className="p-6 w-full max-w-sm">
            <h3 className="text-lg font-semibold mb-4">Nieuw bestand</h3>
            <Input
              placeholder="Bestandsnaam..."
              value={newFileName}
              onChange={(e) => setNewFileName(e.target.value)}
              onKeyPress={(e) => e.key === "Enter" && handleCreateFile()}
              className="mb-4"
              autoFocus
            />
            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowNewFileDialog(false)}
              >
                Annuleren
              </Button>
              <Button onClick={handleCreateFile}>Maken</Button>
            </div>
          </Card>
        </div>
      )}

      {/* New Folder Dialog */}
      {showNewFolderDialog && (
        <div className="absolute inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <Card className="p-6 w-full max-w-sm">
            <h3 className="text-lg font-semibold mb-4">Nieuwe map</h3>
            <Input
              placeholder="Mapnaam..."
              value={newFolderName}
              onChange={(e) => setNewFolderName(e.target.value)}
              onKeyPress={(e) => e.key === "Enter" && handleCreateFolder()}
              className="mb-4"
              autoFocus
            />
            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowNewFolderDialog(false)}
              >
                Annuleren
              </Button>
              <Button onClick={handleCreateFolder}>Maken</Button>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
}
