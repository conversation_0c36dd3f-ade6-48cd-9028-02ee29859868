"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { useTheme } from "@/components/theme-provider";
import { Settings, Key, Folder, Eye, EyeOff, ExternalLink } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";

interface Settings {
  groq_api_key: string;
  root_folder: string;
  model_name: string;
  max_tokens: number;
  temperature: number;
  auto_save_interval: number;
  theme: string;
  language: string;
  enable_ai_suggestions: boolean;
  enable_auto_backup: boolean;
  backup_interval: number;
  max_backups: number;
}

interface SettingsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  settings: Settings;
  onSave: (settings: Partial<Settings>) => void;
}

export function SettingsDialog({
  open,
  onOpenChange,
  settings,
  onSave,
}: SettingsDialogProps) {
  const [showApiKey, setShowApiKey] = useState(false);
  const [activeTab, setActiveTab] = useState("general");
  const { theme, setTheme } = useTheme();

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm({
    defaultValues: settings,
  });

  const onSubmit = (data: any) => {
    onSave(data);
    onOpenChange(false);
    toast.success("Instellingen opgeslagen");

    // Update theme if changed
    if (data.theme && data.theme !== theme) {
      setTheme(data.theme);
    }
  };

  const temperatureValue = watch("temperature", 0.7);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Settings className="w-5 h-5" />
            <span>Instellingen</span>
          </DialogTitle>
          <DialogDescription>
            Configureer je Document Schrijver applicatie
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger
              value="general"
              className="flex items-center space-x-2"
            >
              <Settings className="w-4 h-4" />
              <span>Algemeen</span>
            </TabsTrigger>
            <TabsTrigger value="api" className="flex items-center space-x-2">
              <Key className="w-4 h-4" />
              <span>API</span>
            </TabsTrigger>
            <TabsTrigger
              value="folders"
              className="flex items-center space-x-2"
            >
              <Folder className="w-4 h-4" />
              <span>Mappen</span>
            </TabsTrigger>
          </TabsList>

          <form
            onSubmit={handleSubmit(onSubmit)}
            className="flex-1 overflow-y-auto"
          >
            <div className="space-y-6 py-4">
              {/* General Tab */}
              <TabsContent value="general" className="space-y-4">
                <div className="grid gap-4">
                  <div>
                    <Label htmlFor="theme">Thema</Label>
                    <Select
                      value={watch("theme")}
                      onValueChange={(value) => setValue("theme", value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="dark">Donker</SelectItem>
                        <SelectItem value="light">Licht</SelectItem>
                        <SelectItem value="system">Systeem</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="language">Taal</Label>
                    <Select
                      value={watch("language")}
                      onValueChange={(value) => setValue("language", value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="nl">Nederlands</SelectItem>
                        <SelectItem value="en">English</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="auto_save_interval">
                      Auto-opslaan interval (seconden)
                    </Label>
                    <Input
                      type="number"
                      min="10"
                      max="300"
                      {...register("auto_save_interval", {
                        valueAsNumber: true,
                        min: 10,
                        max: 300,
                      })}
                    />
                    {errors.auto_save_interval && (
                      <p className="text-sm text-destructive mt-1">
                        Interval moet tussen 10 en 300 seconden zijn
                      </p>
                    )}
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>AI suggesties</Label>
                      <p className="text-sm text-muted-foreground">
                        Ontvang automatische suggesties tijdens het typen
                      </p>
                    </div>
                    <Switch
                      checked={watch("enable_ai_suggestions")}
                      onCheckedChange={(checked) =>
                        setValue("enable_ai_suggestions", checked)
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Automatische back-ups</Label>
                      <p className="text-sm text-muted-foreground">
                        Maak automatisch back-ups van je documenten
                      </p>
                    </div>
                    <Switch
                      checked={watch("enable_auto_backup")}
                      onCheckedChange={(checked) =>
                        setValue("enable_auto_backup", checked)
                      }
                    />
                  </div>
                </div>
              </TabsContent>

              {/* API Tab */}
              <TabsContent value="api" className="space-y-4">
                <div className="grid gap-4">
                  <div>
                    <Label htmlFor="groq_api_key">GROQ API Key</Label>
                    <div className="relative">
                      <Input
                        type={showApiKey ? "text" : "password"}
                        {...register("groq_api_key", {
                          required: "API key is vereist",
                        })}
                        placeholder="Voer je GROQ API key in..."
                        className="pr-20"
                      />
                      <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          onClick={() => setShowApiKey(!showApiKey)}
                          className="h-8 w-8"
                        >
                          {showApiKey ? (
                            <EyeOff className="w-4 h-4" />
                          ) : (
                            <Eye className="w-4 h-4" />
                          )}
                        </Button>
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          onClick={() =>
                            window.open("https://console.groq.com", "_blank")
                          }
                          className="h-8 w-8"
                        >
                          <ExternalLink className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    {errors.groq_api_key && (
                      <p className="text-sm text-destructive mt-1">
                        {errors.groq_api_key.message}
                      </p>
                    )}
                    <p className="text-xs text-muted-foreground mt-1">
                      Krijg je API key van{" "}
                      <a
                        href="https://console.groq.com"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary hover:underline"
                      >
                        console.groq.com
                      </a>
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="model_name">Model</Label>
                    <Select
                      value={watch("model_name")}
                      onValueChange={(value) => setValue("model_name", value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="deepseek-r1-distill-llama-70b">
                          DeepSeek-R1 70B
                        </SelectItem>
                        <SelectItem value="llama-3.1-70b-versatile">
                          Llama 3.1 70B
                        </SelectItem>
                        <SelectItem value="mixtral-8x7b-32768">
                          Mixtral 8x7B
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="max_tokens">Max Tokens</Label>
                    <Input
                      type="number"
                      min="1000"
                      max="32000"
                      {...register("max_tokens", {
                        valueAsNumber: true,
                        min: 1000,
                        max: 32000,
                      })}
                    />
                  </div>

                  <div>
                    <Label htmlFor="temperature">
                      Temperature ({temperatureValue.toFixed(1)})
                    </Label>
                    <div className="space-y-2">
                      <input
                        type="range"
                        min="0"
                        max="2"
                        step="0.1"
                        value={temperatureValue}
                        onChange={(e) =>
                          setValue("temperature", parseFloat(e.target.value))
                        }
                        className="w-full h-2 bg-muted rounded-lg appearance-none cursor-pointer"
                      />
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>Conservatief</span>
                        <span>Creatief</span>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>

              {/* Folders Tab */}
              <TabsContent value="folders" className="space-y-4">
                <div className="grid gap-4">
                  <div>
                    <Label htmlFor="root_folder">Root Map</Label>
                    <Input
                      {...register("root_folder", {
                        required: "Root map is vereist",
                      })}
                      placeholder="/pad/naar/documenten"
                    />
                    {errors.root_folder && (
                      <p className="text-sm text-destructive mt-1">
                        {errors.root_folder.message}
                      </p>
                    )}
                    <p className="text-xs text-muted-foreground mt-1">
                      Alle documenten worden opgeslagen in deze map
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="max_backups">Max Back-ups</Label>
                    <Input
                      type="number"
                      min="1"
                      max="50"
                      {...register("max_backups", {
                        valueAsNumber: true,
                        min: 1,
                        max: 50,
                      })}
                    />
                  </div>

                  <div>
                    <Label htmlFor="backup_interval">
                      Back-up interval (seconden)
                    </Label>
                    <Input
                      type="number"
                      min="60"
                      max="3600"
                      {...register("backup_interval", {
                        valueAsNumber: true,
                        min: 60,
                        max: 3600,
                      })}
                    />
                  </div>
                </div>
              </TabsContent>
            </div>

            {/* Footer */}
            <div className="flex justify-end space-x-3 pt-4 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                Annuleren
              </Button>
              <Button type="submit">Opslaan</Button>
            </div>
          </form>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
