.file-explorer {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #1e1e1e;
  border-right: 1px solid #444;
}

.file-explorer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #2a2a2a;
  border-bottom: 1px solid #444;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #ffffff;
}

.header-actions {
  display: flex;
  gap: 0.25rem;
}

.action-btn {
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s;
}

.action-btn:hover {
  background-color: #444;
  color: #ffffff;
}

.new-file-dialog {
  padding: 1rem;
  background-color: #2a2a2a;
  border-bottom: 1px solid #444;
}

.new-file-dialog input {
  width: 100%;
  padding: 0.5rem;
  background-color: #1e1e1e;
  border: 1px solid #444;
  border-radius: 4px;
  color: #ffffff;
  margin-bottom: 0.5rem;
}

.new-file-dialog input:focus {
  outline: none;
  border-color: #0ea5e9;
}

.dialog-actions {
  display: flex;
  gap: 0.5rem;
}

.dialog-actions button {
  padding: 0.25rem 0.75rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
}

.dialog-actions button:first-child {
  background-color: #0ea5e9;
  color: white;
}

.dialog-actions button:last-child {
  background-color: #444;
  color: #ffffff;
}

.file-list {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem;
}

.loading {
  text-align: center;
  padding: 2rem;
  color: #888;
}

.empty-state {
  text-align: center;
  padding: 2rem;
  color: #888;
}

.empty-state button {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: #0ea5e9;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0.75rem;
  margin-bottom: 0.25rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.file-item:hover {
  background-color: #2a2a2a;
}

.file-item.selected {
  background-color: #1e3a8a;
  border-color: #3b82f6;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: #ffffff;
  margin-bottom: 0.25rem;
}

.file-name span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-meta {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
  font-size: 0.75rem;
  color: #888;
}

.file-preview {
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #aaa;
  line-height: 1.3;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.delete-btn {
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  opacity: 0;
  transition: all 0.2s;
}

.file-item:hover .delete-btn {
  opacity: 1;
}

.delete-btn:hover {
  background-color: #dc2626;
  color: white;
}
