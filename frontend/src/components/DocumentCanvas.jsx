import { useState, useEffect, useRef } from 'react'
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels'
import Editor from '@monaco-editor/react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { Save, Eye, Edit3 } from 'lucide-react'
import './DocumentCanvas.css'

const DocumentCanvas = ({ content, onContentChange, onSave, websocket }) => {
  const [viewMode, setViewMode] = useState('split') // 'edit', 'preview', 'split'
  const [selectedText, setSelectedText] = useState('')
  const [selectionRange, setSelectionRange] = useState(null)
  const editorRef = useRef(null)
  const previewRef = useRef(null)

  useEffect(() => {
    // Listen for text selection in preview
    const handleSelection = () => {
      const selection = window.getSelection()
      if (selection.rangeCount > 0 && selection.toString().trim()) {
        const selectedText = selection.toString()
        setSelectedText(selectedText)
        
        // Store selection for rewriting
        const range = selection.getRangeAt(0)
        setSelectionRange({
          text: selectedText,
          startOffset: range.startOffset,
          endOffset: range.endOffset
        })
      }
    }

    document.addEventListener('mouseup', handleSelection)
    return () => document.removeEventListener('mouseup', handleSelection)
  }, [])

  const handleEditorDidMount = (editor, monaco) => {
    editorRef.current = editor
    
    // Configure editor
    editor.updateOptions({
      wordWrap: 'on',
      minimap: { enabled: false },
      fontSize: 14,
      lineHeight: 1.6,
      padding: { top: 16, bottom: 16 }
    })
  }

  const handleRewriteSelection = () => {
    if (!selectedText || !websocket) return
    
    const instruction = prompt('Hoe wil je deze tekst herschrijven?')
    if (!instruction) return
    
    const message = {
      type: 'generate_stream',
      agent_type: 'rewriter',
      instruction: instruction,
      context: selectedText,
      task_id: Date.now().toString()
    }
    
    websocket.send(JSON.stringify(message))
  }

  const insertTextAtCursor = (text) => {
    if (!editorRef.current) return
    
    const editor = editorRef.current
    const position = editor.getPosition()
    const range = {
      startLineNumber: position.lineNumber,
      startColumn: position.column,
      endLineNumber: position.lineNumber,
      endColumn: position.column
    }
    
    editor.executeEdits('insert-text', [{
      range: range,
      text: text
    }])
    
    // Update content
    onContentChange(editor.getValue())
  }

  const replaceSelectedText = (newText) => {
    if (!editorRef.current || !selectionRange) return
    
    const editor = editorRef.current
    const model = editor.getModel()
    const fullText = model.getValue()
    
    // Find the selected text in the editor content
    const beforeText = fullText.substring(0, fullText.indexOf(selectionRange.text))
    const afterText = fullText.substring(fullText.indexOf(selectionRange.text) + selectionRange.text.length)
    
    const newContent = beforeText + newText + afterText
    onContentChange(newContent)
    
    // Clear selection
    setSelectedText('')
    setSelectionRange(null)
  }

  // Listen for WebSocket messages
  useEffect(() => {
    if (!websocket) return
    
    const handleMessage = (event) => {
      const data = JSON.parse(event.data)
      
      if (data.type === 'stream_chunk') {
        // For rewriter, replace selected text
        if (selectedText) {
          replaceSelectedText(data.content)
        } else {
          // For new content, insert at cursor
          insertTextAtCursor(data.content)
        }
      }
    }
    
    websocket.addEventListener('message', handleMessage)
    return () => websocket.removeEventListener('message', handleMessage)
  }, [websocket, selectedText])

  const renderViewModeButtons = () => (
    <div className="view-mode-buttons">
      <button
        className={viewMode === 'edit' ? 'active' : ''}
        onClick={() => setViewMode('edit')}
        title="Alleen bewerken"
      >
        <Edit3 size={16} />
      </button>
      <button
        className={viewMode === 'split' ? 'active' : ''}
        onClick={() => setViewMode('split')}
        title="Gesplitste weergave"
      >
        <div style={{ display: 'flex', gap: '2px' }}>
          <div style={{ width: '6px', height: '12px', backgroundColor: 'currentColor' }}></div>
          <div style={{ width: '6px', height: '12px', backgroundColor: 'currentColor' }}></div>
        </div>
      </button>
      <button
        className={viewMode === 'preview' ? 'active' : ''}
        onClick={() => setViewMode('preview')}
        title="Alleen voorvertoning"
      >
        <Eye size={16} />
      </button>
    </div>
  )

  return (
    <div className="document-canvas">
      <div className="canvas-header">
        <div className="canvas-title">
          <h3>Document Canvas</h3>
        </div>
        <div className="canvas-actions">
          {renderViewModeButtons()}
          {selectedText && (
            <button
              className="rewrite-btn"
              onClick={handleRewriteSelection}
              title="Herschrijf geselecteerde tekst"
            >
              Herschrijven
            </button>
          )}
          <button
            className="save-btn"
            onClick={onSave}
            title="Opslaan (Ctrl+S)"
          >
            <Save size={16} />
          </button>
        </div>
      </div>

      <div className="canvas-content">
        {viewMode === 'edit' && (
          <div className="editor-container">
            <Editor
              height="100%"
              defaultLanguage="markdown"
              value={content}
              onChange={onContentChange}
              onMount={handleEditorDidMount}
              theme="vs-dark"
              options={{
                wordWrap: 'on',
                minimap: { enabled: false },
                fontSize: 14,
                lineHeight: 1.6,
                padding: { top: 16, bottom: 16 }
              }}
            />
          </div>
        )}

        {viewMode === 'preview' && (
          <div className="preview-container" ref={previewRef}>
            <div className="markdown-content">
              <ReactMarkdown remarkPlugins={[remarkGfm]}>
                {content || '# Leeg document\n\nBegin met typen...'}
              </ReactMarkdown>
            </div>
          </div>
        )}

        {viewMode === 'split' && (
          <PanelGroup direction="horizontal">
            <Panel defaultSize={50}>
              <div className="editor-container">
                <Editor
                  height="100%"
                  defaultLanguage="markdown"
                  value={content}
                  onChange={onContentChange}
                  onMount={handleEditorDidMount}
                  theme="vs-dark"
                  options={{
                    wordWrap: 'on',
                    minimap: { enabled: false },
                    fontSize: 14,
                    lineHeight: 1.6,
                    padding: { top: 16, bottom: 16 }
                  }}
                />
              </div>
            </Panel>
            
            <PanelResizeHandle className="canvas-resize-handle" />
            
            <Panel defaultSize={50}>
              <div className="preview-container" ref={previewRef}>
                <div className="markdown-content">
                  <ReactMarkdown remarkPlugins={[remarkGfm]}>
                    {content || '# Leeg document\n\nBegin met typen...'}
                  </ReactMarkdown>
                </div>
              </div>
            </Panel>
          </PanelGroup>
        )}
      </div>

      {selectedText && (
        <div className="selection-indicator">
          Geselecteerd: "{selectedText.substring(0, 50)}..."
        </div>
      )}
    </div>
  )
}

export default DocumentCanvas
