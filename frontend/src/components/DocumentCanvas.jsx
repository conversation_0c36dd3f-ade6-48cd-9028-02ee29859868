import { useState, useEffect, useRef } from "react";
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import Editor from "@monaco-editor/react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { Save, Eye, Edit3 } from "lucide-react";

const DocumentCanvas = ({ content, onContentChange, onSave, websocket }) => {
  const [viewMode, setViewMode] = useState("split"); // 'edit', 'preview', 'split'
  const [selectedText, setSelectedText] = useState("");
  const [selectionRange, setSelectionRange] = useState(null);
  const editorRef = useRef(null);
  const previewRef = useRef(null);

  useEffect(() => {
    // Listen for text selection in preview
    const handleSelection = () => {
      const selection = window.getSelection();
      if (selection.rangeCount > 0 && selection.toString().trim()) {
        const selectedText = selection.toString();
        setSelectedText(selectedText);

        // Store selection for rewriting
        const range = selection.getRangeAt(0);
        setSelectionRange({
          text: selectedText,
          startOffset: range.startOffset,
          endOffset: range.endOffset,
        });
      }
    };

    document.addEventListener("mouseup", handleSelection);
    return () => document.removeEventListener("mouseup", handleSelection);
  }, []);

  const handleEditorDidMount = (editor, monaco) => {
    editorRef.current = editor;

    // Configure editor
    editor.updateOptions({
      wordWrap: "on",
      minimap: { enabled: false },
      fontSize: 14,
      lineHeight: 1.6,
      padding: { top: 16, bottom: 16 },
    });
  };

  const handleRewriteSelection = () => {
    if (!selectedText || !websocket) return;

    const instruction = prompt("Hoe wil je deze tekst herschrijven?");
    if (!instruction) return;

    const message = {
      type: "generate_stream",
      agent_type: "rewriter",
      instruction: instruction,
      context: selectedText,
      task_id: Date.now().toString(),
    };

    websocket.send(JSON.stringify(message));
  };

  const insertTextAtCursor = (text) => {
    if (!editorRef.current) return;

    const editor = editorRef.current;
    const position = editor.getPosition();
    const range = {
      startLineNumber: position.lineNumber,
      startColumn: position.column,
      endLineNumber: position.lineNumber,
      endColumn: position.column,
    };

    editor.executeEdits("insert-text", [
      {
        range: range,
        text: text,
      },
    ]);

    // Update content
    onContentChange(editor.getValue());
  };

  const replaceSelectedText = (newText) => {
    if (!editorRef.current || !selectionRange) return;

    const editor = editorRef.current;
    const model = editor.getModel();
    const fullText = model.getValue();

    // Find the selected text in the editor content
    const beforeText = fullText.substring(
      0,
      fullText.indexOf(selectionRange.text)
    );
    const afterText = fullText.substring(
      fullText.indexOf(selectionRange.text) + selectionRange.text.length
    );

    const newContent = beforeText + newText + afterText;
    onContentChange(newContent);

    // Clear selection
    setSelectedText("");
    setSelectionRange(null);
  };

  // Listen for WebSocket messages
  useEffect(() => {
    if (!websocket) return;

    const handleMessage = (event) => {
      const data = JSON.parse(event.data);

      if (data.type === "stream_chunk") {
        // For rewriter, replace selected text
        if (selectedText) {
          replaceSelectedText(data.content);
        } else {
          // For new content, insert at cursor
          insertTextAtCursor(data.content);
        }
      }
    };

    websocket.addEventListener("message", handleMessage);
    return () => websocket.removeEventListener("message", handleMessage);
  }, [websocket, selectedText]);

  const renderViewModeButtons = () => (
    <div className="flex bg-gray-800 rounded-lg p-1">
      <button
        className={`px-3 py-1 rounded text-sm transition-all ${
          viewMode === "edit"
            ? "bg-blue-600 text-white"
            : "text-gray-400 hover:text-gray-300"
        }`}
        onClick={() => setViewMode("edit")}
        title="Alleen bewerken"
      >
        <Edit3 size={16} />
      </button>
      <button
        className={`px-3 py-1 rounded text-sm transition-all ${
          viewMode === "split"
            ? "bg-blue-600 text-white"
            : "text-gray-400 hover:text-gray-300"
        }`}
        onClick={() => setViewMode("split")}
        title="Gesplitste weergave"
      >
        <div className="flex gap-0.5">
          <div className="w-1.5 h-3 bg-current"></div>
          <div className="w-1.5 h-3 bg-current"></div>
        </div>
      </button>
      <button
        className={`px-3 py-1 rounded text-sm transition-all ${
          viewMode === "preview"
            ? "bg-blue-600 text-white"
            : "text-gray-400 hover:text-gray-300"
        }`}
        onClick={() => setViewMode("preview")}
        title="Alleen voorvertoning"
      >
        <Eye size={16} />
      </button>
    </div>
  );

  return (
    <div className="h-full bg-gray-900 flex flex-col">
      <div className="p-4 border-b border-gray-800 flex items-center justify-between">
        <div>
          <h3 className="font-semibold text-gray-100">Document Canvas</h3>
        </div>
        <div className="flex items-center space-x-3">
          {renderViewModeButtons()}
          {selectedText && (
            <button
              className="btn-secondary text-sm"
              onClick={handleRewriteSelection}
              title="Herschrijf geselecteerde tekst"
            >
              Herschrijven
            </button>
          )}
          <button
            className="btn-primary"
            onClick={onSave}
            title="Opslaan (Ctrl+S)"
          >
            <Save size={16} />
          </button>
        </div>
      </div>

      <div className="flex-1 overflow-hidden">
        {viewMode === "edit" && (
          <div className="h-full">
            <Editor
              height="100%"
              defaultLanguage="markdown"
              value={content}
              onChange={onContentChange}
              onMount={handleEditorDidMount}
              theme="vs-dark"
              options={{
                wordWrap: "on",
                minimap: { enabled: false },
                fontSize: 14,
                lineHeight: 1.6,
                padding: { top: 16, bottom: 16 },
                fontFamily: "'JetBrains Mono', 'Fira Code', monospace",
              }}
            />
          </div>
        )}

        {viewMode === "preview" && (
          <div
            className="h-full overflow-y-auto scrollbar-thin"
            ref={previewRef}
          >
            <div className="p-6 max-w-4xl mx-auto">
              <div className="prose prose-invert prose-lg max-w-none">
                <ReactMarkdown remarkPlugins={[remarkGfm]}>
                  {content || "# Leeg document\n\nBegin met typen..."}
                </ReactMarkdown>
              </div>
            </div>
          </div>
        )}

        {viewMode === "split" && (
          <PanelGroup direction="horizontal" className="h-full">
            <Panel defaultSize={50}>
              <div className="h-full">
                <Editor
                  height="100%"
                  defaultLanguage="markdown"
                  value={content}
                  onChange={onContentChange}
                  onMount={handleEditorDidMount}
                  theme="vs-dark"
                  options={{
                    wordWrap: "on",
                    minimap: { enabled: false },
                    fontSize: 14,
                    lineHeight: 1.6,
                    padding: { top: 16, bottom: 16 },
                    fontFamily: "'JetBrains Mono', 'Fira Code', monospace",
                  }}
                />
              </div>
            </Panel>

            <PanelResizeHandle className="w-1 bg-gray-800 hover:bg-gray-700 transition-colors" />

            <Panel defaultSize={50}>
              <div
                className="h-full overflow-y-auto scrollbar-thin"
                ref={previewRef}
              >
                <div className="p-6">
                  <div className="prose prose-invert prose-lg max-w-none">
                    <ReactMarkdown remarkPlugins={[remarkGfm]}>
                      {content || "# Leeg document\n\nBegin met typen..."}
                    </ReactMarkdown>
                  </div>
                </div>
              </div>
            </Panel>
          </PanelGroup>
        )}
      </div>

      {selectedText && (
        <div className="px-4 py-2 bg-blue-900/30 border-t border-blue-500/30 text-blue-300 text-sm">
          <span className="font-medium">Geselecteerd:</span> "
          {selectedText.substring(0, 50)}..."
        </div>
      )}
    </div>
  );
};

export default DocumentCanvas;
