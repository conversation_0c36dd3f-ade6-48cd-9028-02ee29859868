@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-gray-950 text-gray-100 font-sans antialiased;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  }

  html {
    @apply scroll-smooth;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-950 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply btn bg-gray-700 text-gray-100 hover:bg-gray-600 focus:ring-gray-500;
  }

  .btn-ghost {
    @apply btn bg-transparent text-gray-400 hover:bg-gray-800 hover:text-gray-100 focus:ring-gray-500;
  }

  .btn-danger {
    @apply btn bg-error-600 text-white hover:bg-error-700 focus:ring-error-500;
  }

  .input {
    @apply block w-full rounded-lg border border-gray-700 bg-gray-800 px-3 py-2 text-sm text-gray-100 placeholder-gray-400 transition-colors focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500;
  }

  .card {
    @apply rounded-xl border border-gray-800 bg-gray-900/50 backdrop-blur-sm;
  }

  .glass {
    @apply bg-gray-900/20 backdrop-blur-md border border-gray-800/50;
  }

  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(75 85 99) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(75 85 99);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(107 114 128);
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent;
  }

  .border-gradient {
    @apply border border-transparent bg-gradient-to-r from-primary-500 to-primary-700 bg-clip-border;
  }

  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Monaco Editor theme adjustments */
.monaco-editor {
  @apply rounded-lg;
}

.monaco-editor .margin {
  @apply bg-gray-900;
}

/* React Markdown styling */
.prose-dark {
  @apply prose prose-invert max-w-none;
}

.prose-dark h1,
.prose-dark h2,
.prose-dark h3,
.prose-dark h4,
.prose-dark h5,
.prose-dark h6 {
  @apply text-gray-100;
}

.prose-dark p {
  @apply text-gray-300;
}

.prose-dark code {
  @apply bg-gray-800 text-primary-400 px-1 py-0.5 rounded;
}

.prose-dark pre {
  @apply bg-gray-900 border border-gray-700;
}

.prose-dark blockquote {
  @apply border-l-primary-500 text-gray-300;
}

.prose-dark a {
  @apply text-primary-400 hover:text-primary-300;
}

/* Selection styling */
::selection {
  @apply bg-primary-600 text-white;
}

::-moz-selection {
  @apply bg-primary-600 text-white;
}
