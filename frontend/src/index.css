:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #1a1a1a;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background-color: #1a1a1a;
  color: #ffffff;
  overflow: hidden;
}

#root {
  height: 100vh;
  width: 100vw;
}

a {
  font-weight: 500;
  color: #60a5fa;
  text-decoration: inherit;
}

a:hover {
  color: #93c5fd;
}

button {
  border-radius: 6px;
  border: 1px solid transparent;
  padding: 0.5em 1em;
  font-size: 0.875em;
  font-weight: 500;
  font-family: inherit;
  background-color: #374151;
  color: inherit;
  cursor: pointer;
  transition: all 0.2s;
}

button:hover {
  background-color: #4b5563;
}

button:focus,
button:focus-visible {
  outline: 2px solid #0ea5e9;
  outline-offset: 2px;
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

input, textarea, select {
  font-family: inherit;
  font-size: inherit;
}

/* Scrollbar styling for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: #444;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #666;
}

/* Selection styling */
::selection {
  background-color: #3b82f6;
  color: white;
}

::-moz-selection {
  background-color: #3b82f6;
  color: white;
}
