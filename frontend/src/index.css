@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles */
body {
  font-family: "Inter", sans-serif;
}

/* Custom component styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s;
  cursor: pointer;
  border: none;
  outline: none;
}

.btn:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px rgb(59 130 246);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: rgb(37 99 235);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: rgb(29 78 216);
}

.btn-secondary {
  background-color: rgb(55 65 81);
  color: rgb(243 244 246);
}

.btn-secondary:hover:not(:disabled) {
  background-color: rgb(75 85 99);
}

.btn-ghost {
  background-color: transparent;
  color: rgb(156 163 175);
}

.btn-ghost:hover:not(:disabled) {
  background-color: rgb(31 41 55);
  color: rgb(243 244 246);
}

.input {
  display: block;
  width: 100%;
  border-radius: 0.5rem;
  border: 1px solid rgb(55 65 81);
  background-color: rgb(31 41 55);
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  color: rgb(243 244 246);
  transition: border-color 0.2s, box-shadow 0.2s;
}

.input::placeholder {
  color: rgb(156 163 175);
}

.input:focus {
  outline: none;
  border-color: rgb(59 130 246);
  box-shadow: 0 0 0 1px rgb(59 130 246);
}

.card {
  border-radius: 0.75rem;
  border: 1px solid rgb(31 41 55);
  background-color: rgba(17, 24, 39, 0.5);
  backdrop-filter: blur(4px);
}

.glass {
  background-color: rgba(17, 24, 39, 0.2);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(31, 41, 55, 0.5);
}

.text-gradient {
  background: linear-gradient(to right, rgb(96 165 250), rgb(37 99 235));
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgb(75 85 99) transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgb(75 85 99);
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgb(107 114 128);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

/* Selection styling */
::selection {
  background-color: rgb(37 99 235);
  color: white;
}

::-moz-selection {
  background-color: rgb(37 99 235);
  color: white;
}

/* Monaco Editor adjustments */
.monaco-editor {
  border-radius: 0.5rem;
}

.monaco-editor .margin {
  background-color: rgb(17 24 39);
}

/* Prose styling for markdown */
.prose-dark {
  max-width: none;
  color: rgb(209 213 219);
}

.prose-dark h1,
.prose-dark h2,
.prose-dark h3,
.prose-dark h4,
.prose-dark h5,
.prose-dark h6 {
  color: rgb(243 244 246);
}

.prose-dark p {
  color: rgb(209 213 219);
}

.prose-dark code {
  background-color: rgb(31 41 55);
  color: rgb(96 165 250);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

.prose-dark pre {
  background-color: rgb(17 24 39);
  border: 1px solid rgb(55 65 81);
}

.prose-dark blockquote {
  border-left: 4px solid rgb(37 99 235);
  color: rgb(209 213 219);
}

.prose-dark a {
  color: rgb(96 165 250);
}

.prose-dark a:hover {
  color: rgb(147 197 253);
}

/* Line clamp utility */
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
