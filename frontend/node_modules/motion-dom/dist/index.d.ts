import { Box, Easing, EasingFunction, BezierDefinition } from 'motion-utils';

interface SVGAttributes {
    accentHeight?: number | string | undefined;
    accumulate?: "none" | "sum" | undefined;
    additive?: "replace" | "sum" | undefined;
    alignmentBaseline?: "auto" | "baseline" | "before-edge" | "text-before-edge" | "middle" | "central" | "after-edge" | "text-after-edge" | "ideographic" | "alphabetic" | "hanging" | "mathematical" | "inherit" | undefined;
    allowReorder?: "no" | "yes" | undefined;
    alphabetic?: number | string | undefined;
    amplitude?: number | string | undefined;
    arabicForm?: "initial" | "medial" | "terminal" | "isolated" | undefined;
    ascent?: number | string | undefined;
    attributeName?: string | undefined;
    attributeType?: string | undefined;
    autoReverse?: boolean | undefined;
    azimuth?: number | string | undefined;
    baseFrequency?: number | string | undefined;
    baselineShift?: number | string | undefined;
    baseProfile?: number | string | undefined;
    bbox?: number | string | undefined;
    begin?: number | string | undefined;
    bias?: number | string | undefined;
    by?: number | string | undefined;
    calcMode?: number | string | undefined;
    capHeight?: number | string | undefined;
    clip?: number | string | undefined;
    clipPath?: string | undefined;
    clipPathUnits?: number | string | undefined;
    clipRule?: number | string | undefined;
    colorInterpolation?: number | string | undefined;
    colorInterpolationFilters?: "auto" | "sRGB" | "linearRGB" | "inherit" | undefined;
    colorProfile?: number | string | undefined;
    colorRendering?: number | string | undefined;
    contentScriptType?: number | string | undefined;
    contentStyleType?: number | string | undefined;
    cursor?: number | string | undefined;
    cx?: number | string | undefined;
    cy?: number | string | undefined;
    d?: string | undefined;
    decelerate?: number | string | undefined;
    descent?: number | string | undefined;
    diffuseConstant?: number | string | undefined;
    direction?: number | string | undefined;
    display?: number | string | undefined;
    divisor?: number | string | undefined;
    dominantBaseline?: number | string | undefined;
    dur?: number | string | undefined;
    dx?: number | string | undefined;
    dy?: number | string | undefined;
    edgeMode?: number | string | undefined;
    elevation?: number | string | undefined;
    enableBackground?: number | string | undefined;
    end?: number | string | undefined;
    exponent?: number | string | undefined;
    externalResourcesRequired?: boolean | undefined;
    fill?: string | undefined;
    fillOpacity?: number | string | undefined;
    fillRule?: "nonzero" | "evenodd" | "inherit" | undefined;
    filter?: string | undefined;
    filterRes?: number | string | undefined;
    filterUnits?: number | string | undefined;
    floodColor?: number | string | undefined;
    floodOpacity?: number | string | undefined;
    focusable?: boolean | "auto" | undefined;
    fontFamily?: string | undefined;
    fontSize?: number | string | undefined;
    fontSizeAdjust?: number | string | undefined;
    fontStretch?: number | string | undefined;
    fontStyle?: number | string | undefined;
    fontVariant?: number | string | undefined;
    fontWeight?: number | string | undefined;
    format?: number | string | undefined;
    fr?: number | string | undefined;
    from?: number | string | undefined;
    fx?: number | string | undefined;
    fy?: number | string | undefined;
    g1?: number | string | undefined;
    g2?: number | string | undefined;
    glyphName?: number | string | undefined;
    glyphOrientationHorizontal?: number | string | undefined;
    glyphOrientationVertical?: number | string | undefined;
    glyphRef?: number | string | undefined;
    gradientTransform?: string | undefined;
    gradientUnits?: string | undefined;
    hanging?: number | string | undefined;
    horizAdvX?: number | string | undefined;
    horizOriginX?: number | string | undefined;
    href?: string | undefined;
    ideographic?: number | string | undefined;
    imageRendering?: number | string | undefined;
    in2?: number | string | undefined;
    in?: string | undefined;
    intercept?: number | string | undefined;
    k1?: number | string | undefined;
    k2?: number | string | undefined;
    k3?: number | string | undefined;
    k4?: number | string | undefined;
    k?: number | string | undefined;
    kernelMatrix?: number | string | undefined;
    kernelUnitLength?: number | string | undefined;
    kerning?: number | string | undefined;
    keyPoints?: number | string | undefined;
    keySplines?: number | string | undefined;
    keyTimes?: number | string | undefined;
    lengthAdjust?: number | string | undefined;
    letterSpacing?: number | string | undefined;
    lightingColor?: number | string | undefined;
    limitingConeAngle?: number | string | undefined;
    local?: number | string | undefined;
    markerEnd?: string | undefined;
    markerHeight?: number | string | undefined;
    markerMid?: string | undefined;
    markerStart?: string | undefined;
    markerUnits?: number | string | undefined;
    markerWidth?: number | string | undefined;
    mask?: string | undefined;
    maskContentUnits?: number | string | undefined;
    maskUnits?: number | string | undefined;
    mathematical?: number | string | undefined;
    mode?: number | string | undefined;
    numOctaves?: number | string | undefined;
    offset?: number | string | undefined;
    opacity?: number | string | undefined;
    operator?: number | string | undefined;
    order?: number | string | undefined;
    orient?: number | string | undefined;
    orientation?: number | string | undefined;
    origin?: number | string | undefined;
    overflow?: number | string | undefined;
    overlinePosition?: number | string | undefined;
    overlineThickness?: number | string | undefined;
    paintOrder?: number | string | undefined;
    panose1?: number | string | undefined;
    path?: string | undefined;
    pathLength?: number | string | undefined;
    patternContentUnits?: string | undefined;
    patternTransform?: number | string | undefined;
    patternUnits?: string | undefined;
    pointerEvents?: number | string | undefined;
    points?: string | undefined;
    pointsAtX?: number | string | undefined;
    pointsAtY?: number | string | undefined;
    pointsAtZ?: number | string | undefined;
    preserveAlpha?: boolean | undefined;
    preserveAspectRatio?: string | undefined;
    primitiveUnits?: number | string | undefined;
    r?: number | string | undefined;
    radius?: number | string | undefined;
    refX?: number | string | undefined;
    refY?: number | string | undefined;
    renderingIntent?: number | string | undefined;
    repeatCount?: number | string | undefined;
    repeatDur?: number | string | undefined;
    requiredExtensions?: number | string | undefined;
    requiredFeatures?: number | string | undefined;
    restart?: number | string | undefined;
    result?: string | undefined;
    rotate?: number | string | undefined;
    rx?: number | string | undefined;
    ry?: number | string | undefined;
    scale?: number | string | undefined;
    seed?: number | string | undefined;
    shapeRendering?: number | string | undefined;
    slope?: number | string | undefined;
    spacing?: number | string | undefined;
    specularConstant?: number | string | undefined;
    specularExponent?: number | string | undefined;
    speed?: number | string | undefined;
    spreadMethod?: string | undefined;
    startOffset?: number | string | undefined;
    stdDeviation?: number | string | undefined;
    stemh?: number | string | undefined;
    stemv?: number | string | undefined;
    stitchTiles?: number | string | undefined;
    stopColor?: string | undefined;
    stopOpacity?: number | string | undefined;
    strikethroughPosition?: number | string | undefined;
    strikethroughThickness?: number | string | undefined;
    string?: number | string | undefined;
    stroke?: string | undefined;
    strokeDasharray?: string | number | undefined;
    strokeDashoffset?: string | number | undefined;
    strokeLinecap?: "butt" | "round" | "square" | "inherit" | undefined;
    strokeLinejoin?: "miter" | "round" | "bevel" | "inherit" | undefined;
    strokeMiterlimit?: number | string | undefined;
    strokeOpacity?: number | string | undefined;
    strokeWidth?: number | string | undefined;
    surfaceScale?: number | string | undefined;
    systemLanguage?: number | string | undefined;
    tableValues?: number | string | undefined;
    targetX?: number | string | undefined;
    targetY?: number | string | undefined;
    textAnchor?: string | undefined;
    textDecoration?: number | string | undefined;
    textLength?: number | string | undefined;
    textRendering?: number | string | undefined;
    to?: number | string | undefined;
    transform?: string | undefined;
    u1?: number | string | undefined;
    u2?: number | string | undefined;
    underlinePosition?: number | string | undefined;
    underlineThickness?: number | string | undefined;
    unicode?: number | string | undefined;
    unicodeBidi?: number | string | undefined;
    unicodeRange?: number | string | undefined;
    unitsPerEm?: number | string | undefined;
    vAlphabetic?: number | string | undefined;
    values?: string | undefined;
    vectorEffect?: number | string | undefined;
    version?: string | undefined;
    vertAdvY?: number | string | undefined;
    vertOriginX?: number | string | undefined;
    vertOriginY?: number | string | undefined;
    vHanging?: number | string | undefined;
    vIdeographic?: number | string | undefined;
    viewBox?: string | undefined;
    viewTarget?: number | string | undefined;
    visibility?: number | string | undefined;
    vMathematical?: number | string | undefined;
    widths?: number | string | undefined;
    wordSpacing?: number | string | undefined;
    writingMode?: number | string | undefined;
    x1?: number | string | undefined;
    x2?: number | string | undefined;
    x?: number | string | undefined;
    xChannelSelector?: string | undefined;
    xHeight?: number | string | undefined;
    xlinkActuate?: string | undefined;
    xlinkArcrole?: string | undefined;
    xlinkHref?: string | undefined;
    xlinkRole?: string | undefined;
    xlinkShow?: string | undefined;
    xlinkTitle?: string | undefined;
    xlinkType?: string | undefined;
    xmlBase?: string | undefined;
    xmlLang?: string | undefined;
    xmlns?: string | undefined;
    xmlnsXlink?: string | undefined;
    xmlSpace?: string | undefined;
    y1?: number | string | undefined;
    y2?: number | string | undefined;
    y?: number | string | undefined;
    yChannelSelector?: string | undefined;
    z?: number | string | undefined;
    zoomAndPan?: string | undefined;
}

/**
 * An update function. It accepts a timestamp used to advance the animation.
 */
type Update$1 = (timestamp: number) => void;
/**
 * Drivers accept a update function and call it at an interval. This interval
 * could be a synchronous loop, a setInterval, or tied to the device's framerate.
 */
interface DriverControls {
    start: (keepAlive?: boolean) => void;
    stop: () => void;
    now: () => number;
}
type Driver = (update: Update$1) => DriverControls;

/**
 * Temporary subset of VisualElement until VisualElement is
 * moved to motion-dom
 */
interface WithRender {
    render: () => void;
    readValue: (name: string, keyframe: any) => any;
    getValue: (name: string, defaultValue?: any) => any;
    current?: HTMLElement | SVGElement;
    measureViewportBox: () => Box;
}

interface ProgressTimeline {
    currentTime: null | {
        value: number;
    };
    cancel?: VoidFunction;
}
interface ValueAnimationOptionsWithRenderContext<V extends string | number = number> extends ValueAnimationOptions<V> {
    KeyframeResolver?: typeof KeyframeResolver;
    motionValue?: MotionValue<V>;
    element?: WithRender;
}
interface TimelineWithFallback {
    timeline?: ProgressTimeline;
    observe: (animation: AnimationPlaybackControls) => VoidFunction;
}
/**
 * Methods to control an animation.
 */
interface AnimationPlaybackControls {
    /**
     * The current time of the animation, in seconds.
     */
    time: number;
    /**
     * The playback speed of the animation.
     * 1 = normal speed, 2 = double speed, 0.5 = half speed.
     */
    speed: number;
    /**
     * The start time of the animation, in milliseconds.
     */
    startTime: number | null;
    /**
     * The state of the animation.
     *
     * This is currently for internal use only.
     */
    state: AnimationPlayState;
    duration: number;
    /**
     * Stops the animation at its current state, and prevents it from
     * resuming when the animation is played again.
     */
    stop: () => void;
    /**
     * Plays the animation.
     */
    play: () => void;
    /**
     * Pauses the animation.
     */
    pause: () => void;
    /**
     * Completes the animation and applies the final state.
     */
    complete: () => void;
    /**
     * Cancels the animation and applies the initial state.
     */
    cancel: () => void;
    /**
     * Attaches a timeline to the animation, for instance the `ScrollTimeline`.
     *
     * This is currently for internal use only.
     */
    attachTimeline: (timeline: TimelineWithFallback) => VoidFunction;
    finished: Promise<any>;
}
type AnimationPlaybackControlsWithThen = AnimationPlaybackControls & {
    then: (onResolve: VoidFunction, onReject?: VoidFunction) => Promise<void>;
};
interface AnimationState<V> {
    value: V;
    done: boolean;
}
interface KeyframeGenerator<V> {
    calculatedDuration: null | number;
    next: (t: number) => AnimationState<V>;
    toString: () => string;
}
interface DOMValueAnimationOptions<V extends string | number = number> extends ValueAnimationTransition<V> {
    element: HTMLElement | SVGElement;
    keyframes: ValueKeyframesDefinition;
    name: string;
    pseudoElement?: string;
    allowFlatten?: boolean;
}
interface ValueAnimationOptions<V extends string | number = number> extends ValueAnimationTransition {
    keyframes: V[];
    element?: any;
    name?: string;
    motionValue?: MotionValue<V>;
    from?: V;
    isHandoff?: boolean;
    allowFlatten?: boolean;
    finalKeyframe?: V;
}
type GeneratorFactoryFunction = (options: ValueAnimationOptions<any>) => KeyframeGenerator<any>;
interface GeneratorFactory extends GeneratorFactoryFunction {
    applyToOptions?: (options: Transition) => Transition;
}
type AnimationGeneratorType = GeneratorFactory | "decay" | "spring" | "keyframes" | "tween" | "inertia";
interface AnimationPlaybackLifecycles<V> {
    onUpdate?: (latest: V) => void;
    onPlay?: () => void;
    onComplete?: () => void;
    onRepeat?: () => void;
    onStop?: () => void;
}
interface ValueAnimationTransition<V = any> extends Transition, AnimationPlaybackLifecycles<V> {
    isSync?: boolean;
}
type RepeatType = "loop" | "reverse" | "mirror";
interface AnimationPlaybackOptions {
    repeat?: number;
    repeatType?: RepeatType;
    repeatDelay?: number;
}
interface VelocityOptions {
    velocity?: number;
    restSpeed?: number;
    restDelta?: number;
}
interface DurationSpringOptions {
    duration?: number;
    visualDuration?: number;
    bounce?: number;
}
interface SpringOptions extends DurationSpringOptions, VelocityOptions {
    stiffness?: number;
    damping?: number;
    mass?: number;
}
interface DecayOptions extends VelocityOptions {
    keyframes?: number[];
    power?: number;
    timeConstant?: number;
    modifyTarget?: (v: number) => number;
}
interface InertiaOptions extends DecayOptions {
    bounceStiffness?: number;
    bounceDamping?: number;
    min?: number;
    max?: number;
}
interface KeyframeOptions {
    ease?: Easing | Easing[];
    times?: number[];
}
interface Transition extends AnimationPlaybackOptions, Omit<SpringOptions, "keyframes">, Omit<InertiaOptions, "keyframes">, KeyframeOptions {
    delay?: number;
    elapsed?: number;
    driver?: Driver;
    type?: AnimationGeneratorType;
    duration?: number;
    autoplay?: boolean;
    startTime?: number;
}
type SVGPathTransitions = {
    [K in keyof SVGPathProperties]: Transition;
};
type SVGTransitions = {
    [K in keyof SVGAttributes]: Transition;
};
type VariableTransitions = {
    [key: `--${string}`]: Transition;
};
type StyleTransitions = {
    [K in keyof CSSStyleDeclarationWithTransform]?: Transition;
};
type ValueKeyframe = string | number;
type UnresolvedValueKeyframe = ValueKeyframe | null;
type ResolvedValueKeyframe = ValueKeyframe | ValueKeyframe[];
type ValueKeyframesDefinition = ValueKeyframe | ValueKeyframe[] | UnresolvedValueKeyframe[];
type StyleKeyframesDefinition = {
    [K in keyof CSSStyleDeclarationWithTransform]?: ValueKeyframesDefinition;
};
type SVGKeyframesDefinition = {
    [K in keyof SVGAttributes]?: ValueKeyframesDefinition;
};
type VariableKeyframesDefinition = {
    [key: `--${string}`]: ValueKeyframesDefinition;
};
type SVGPathKeyframesDefinition = {
    [K in keyof SVGPathProperties]?: ValueKeyframesDefinition;
};
type DOMKeyframesDefinition = StyleKeyframesDefinition & SVGKeyframesDefinition & SVGPathKeyframesDefinition & VariableKeyframesDefinition;
interface CSSStyleDeclarationWithTransform extends Omit<CSSStyleDeclaration, "direction" | "transition" | "x" | "y" | "z"> {
    x: number | string;
    y: number | string;
    z: number | string;
    rotateX: number | string;
    rotateY: number | string;
    rotateZ: number | string;
    scaleX: number;
    scaleY: number;
    scaleZ: number;
    skewX: number | string;
    skewY: number | string;
}
type AnimationOptionsWithValueOverrides<V = any> = StyleTransitions & SVGPathTransitions & SVGTransitions & VariableTransitions & ValueAnimationTransition<V>;
type DynamicOption<T> = (i: number, total: number) => T;
interface AnimationOptions extends Omit<AnimationOptionsWithValueOverrides, "delay"> {
    delay?: number | DynamicOption<number>;
}
interface TransformProperties {
    x?: string | number;
    y?: string | number;
    z?: string | number;
    translateX?: string | number;
    translateY?: string | number;
    translateZ?: string | number;
    rotate?: string | number;
    rotateX?: string | number;
    rotateY?: string | number;
    rotateZ?: string | number;
    scale?: string | number;
    scaleX?: string | number;
    scaleY?: string | number;
    scaleZ?: string | number;
    skew?: string | number;
    skewX?: string | number;
    skewY?: string | number;
    originX?: string | number;
    originY?: string | number;
    originZ?: string | number;
    perspective?: string | number;
    transformPerspective?: string | number;
}
interface SVGPathProperties {
    pathLength?: number;
    pathOffset?: number;
    pathSpacing?: number;
}

/**
 * @public
 */
type Subscriber<T> = (v: T) => void;
/**
 * @public
 */
type PassiveEffect<T> = (v: T, safeSetter: (v: T) => void) => void;
type StartAnimation = (complete: () => void) => AnimationPlaybackControlsWithThen | undefined;
interface MotionValueEventCallbacks<V> {
    animationStart: () => void;
    animationComplete: () => void;
    animationCancel: () => void;
    change: (latestValue: V) => void;
    renderRequest: () => void;
    destroy: () => void;
}
interface ResolvedValues {
    [key: string]: string | number;
}
interface Owner {
    current: HTMLElement | unknown;
    getProps: () => {
        onUpdate?: (latest: ResolvedValues) => void;
        transformTemplate?: (transform: TransformProperties, generatedTransform: string) => string;
    };
}
interface MotionValueOptions {
    owner?: Owner;
}
declare const collectMotionValues: {
    current: MotionValue[] | undefined;
};
/**
 * `MotionValue` is used to track the state and velocity of motion values.
 *
 * @public
 */
declare class MotionValue<V = any> {
    /**
     * If a MotionValue has an owner, it was created internally within Motion
     * and therefore has no external listeners. It is therefore safe to animate via WAAPI.
     */
    owner?: Owner;
    /**
     * The current state of the `MotionValue`.
     */
    private current;
    /**
     * The previous state of the `MotionValue`.
     */
    private prev;
    /**
     * The previous state of the `MotionValue` at the end of the previous frame.
     */
    private prevFrameValue;
    /**
     * The last time the `MotionValue` was updated.
     */
    updatedAt: number;
    /**
     * The time `prevFrameValue` was updated.
     */
    prevUpdatedAt: number | undefined;
    private stopPassiveEffect?;
    /**
     * Whether the passive effect is active.
     */
    isEffectActive?: boolean;
    /**
     * A reference to the currently-controlling animation.
     */
    animation?: AnimationPlaybackControlsWithThen;
    /**
     * A list of MotionValues whose values are computed from this one.
     * This is a rough start to a proper signal-like dirtying system.
     */
    private dependents;
    /**
     * Tracks whether this value should be removed
     */
    liveStyle?: boolean;
    /**
     * @param init - The initiating value
     * @param config - Optional configuration options
     *
     * -  `transformer`: A function to transform incoming values with.
     */
    constructor(init: V, options?: MotionValueOptions);
    setCurrent(current: V): void;
    setPrevFrameValue(prevFrameValue?: V | undefined): void;
    /**
     * Adds a function that will be notified when the `MotionValue` is updated.
     *
     * It returns a function that, when called, will cancel the subscription.
     *
     * When calling `onChange` inside a React component, it should be wrapped with the
     * `useEffect` hook. As it returns an unsubscribe function, this should be returned
     * from the `useEffect` function to ensure you don't add duplicate subscribers..
     *
     * ```jsx
     * export const MyComponent = () => {
     *   const x = useMotionValue(0)
     *   const y = useMotionValue(0)
     *   const opacity = useMotionValue(1)
     *
     *   useEffect(() => {
     *     function updateOpacity() {
     *       const maxXY = Math.max(x.get(), y.get())
     *       const newOpacity = transform(maxXY, [0, 100], [1, 0])
     *       opacity.set(newOpacity)
     *     }
     *
     *     const unsubscribeX = x.on("change", updateOpacity)
     *     const unsubscribeY = y.on("change", updateOpacity)
     *
     *     return () => {
     *       unsubscribeX()
     *       unsubscribeY()
     *     }
     *   }, [])
     *
     *   return <motion.div style={{ x }} />
     * }
     * ```
     *
     * @param subscriber - A function that receives the latest value.
     * @returns A function that, when called, will cancel this subscription.
     *
     * @deprecated
     */
    onChange(subscription: Subscriber<V>): () => void;
    /**
     * An object containing a SubscriptionManager for each active event.
     */
    private events;
    on<EventName extends keyof MotionValueEventCallbacks<V>>(eventName: EventName, callback: MotionValueEventCallbacks<V>[EventName]): VoidFunction;
    clearListeners(): void;
    /**
     * Attaches a passive effect to the `MotionValue`.
     */
    attach(passiveEffect: PassiveEffect<V>, stopPassiveEffect: VoidFunction): void;
    /**
     * Sets the state of the `MotionValue`.
     *
     * @remarks
     *
     * ```jsx
     * const x = useMotionValue(0)
     * x.set(10)
     * ```
     *
     * @param latest - Latest value to set.
     * @param render - Whether to notify render subscribers. Defaults to `true`
     *
     * @public
     */
    set(v: V, render?: boolean): void;
    setWithVelocity(prev: V, current: V, delta: number): void;
    /**
     * Set the state of the `MotionValue`, stopping any active animations,
     * effects, and resets velocity to `0`.
     */
    jump(v: V, endAnimation?: boolean): void;
    dirty(): void;
    addDependent(dependent: MotionValue): void;
    removeDependent(dependent: MotionValue): void;
    updateAndNotify: (v: V, render?: boolean) => void;
    /**
     * Returns the latest state of `MotionValue`
     *
     * @returns - The latest state of `MotionValue`
     *
     * @public
     */
    get(): NonNullable<V>;
    /**
     * @public
     */
    getPrevious(): V | undefined;
    /**
     * Returns the latest velocity of `MotionValue`
     *
     * @returns - The latest velocity of `MotionValue`. Returns `0` if the state is non-numerical.
     *
     * @public
     */
    getVelocity(): number;
    hasAnimated: boolean;
    /**
     * Registers a new animation to control this `MotionValue`. Only one
     * animation can drive a `MotionValue` at one time.
     *
     * ```jsx
     * value.start()
     * ```
     *
     * @param animation - A function that starts the provided animation
     */
    start(startAnimation: StartAnimation): Promise<void>;
    /**
     * Stop the currently active animation.
     *
     * @public
     */
    stop(): void;
    /**
     * Returns `true` if this value is currently animating.
     *
     * @public
     */
    isAnimating(): boolean;
    private clearAnimation;
    /**
     * Destroy and clean up subscribers to this `MotionValue`.
     *
     * The `MotionValue` hooks like `useMotionValue` and `useTransform` automatically
     * handle the lifecycle of the returned `MotionValue`, so this method is only necessary if you've manually
     * created a `MotionValue` via the `motionValue` function.
     *
     * @public
     */
    destroy(): void;
}
declare function motionValue<V>(init: V, options?: MotionValueOptions): MotionValue<V>;

type UnresolvedKeyframes<T extends string | number> = Array<T | null>;
type ResolvedKeyframes<T extends string | number> = Array<T>;
declare function flushKeyframeResolvers(): void;
type OnKeyframesResolved<T extends string | number> = (resolvedKeyframes: ResolvedKeyframes<T>, finalKeyframe: T, forced: boolean) => void;
declare class KeyframeResolver<T extends string | number = any> {
    name?: string;
    element?: WithRender;
    finalKeyframe?: T;
    suspendedScrollY?: number;
    protected unresolvedKeyframes: UnresolvedKeyframes<string | number>;
    private motionValue?;
    private onComplete;
    state: "pending" | "scheduled" | "complete";
    /**
     * Track whether this resolver is async. If it is, it'll be added to the
     * resolver queue and flushed in the next frame. Resolvers that aren't going
     * to trigger read/write thrashing don't need to be async.
     */
    private isAsync;
    /**
     * Track whether this resolver needs to perform a measurement
     * to resolve its keyframes.
     */
    needsMeasurement: boolean;
    constructor(unresolvedKeyframes: UnresolvedKeyframes<string | number>, onComplete: OnKeyframesResolved<T>, name?: string, motionValue?: MotionValue<T>, element?: WithRender, isAsync?: boolean);
    scheduleResolve(): void;
    readKeyframes(): void;
    setFinalKeyframe(): void;
    measureInitialState(): void;
    renderEndStyles(): void;
    measureEndState(): void;
    complete(isForcedComplete?: boolean): void;
    cancel(): void;
    resume(): void;
}

declare class WithPromise {
    protected _finished: Promise<void>;
    resolve: VoidFunction;
    constructor();
    get finished(): Promise<void>;
    protected updateFinished(): void;
    protected notifyFinished(): void;
    /**
     * Allows the animation to be awaited.
     *
     * @deprecated Use `finished` instead.
     */
    then(onResolve: VoidFunction, onReject?: VoidFunction): Promise<void>;
}

type OptionsWithoutKeyframes<T extends string | number> = Omit<ValueAnimationOptions<T>, "keyframes">;
declare class AsyncMotionValueAnimation<T extends string | number> extends WithPromise implements AnimationPlaybackControls {
    private createdAt;
    private resolvedAt;
    private _animation;
    private pendingTimeline;
    private keyframeResolver;
    private stopTimeline;
    constructor({ autoplay, delay, type, repeat, repeatDelay, repeatType, keyframes, name, motionValue, element, ...options }: ValueAnimationOptions<T>);
    onKeyframesResolved(keyframes: ResolvedKeyframes<T>, finalKeyframe: T, options: OptionsWithoutKeyframes<T>, sync: boolean): void;
    get finished(): Promise<any>;
    then(onResolve: VoidFunction, _onReject?: VoidFunction): Promise<void>;
    get animation(): AnimationPlaybackControls;
    get duration(): number;
    get time(): number;
    set time(newTime: number);
    get speed(): number;
    get state(): AnimationPlayState;
    set speed(newSpeed: number);
    get startTime(): number | null;
    attachTimeline(timeline: TimelineWithFallback): () => void;
    play(): void;
    pause(): void;
    complete(): void;
    cancel(): void;
    /**
     * Bound to support return animation.stop pattern
     */
    stop: () => void;
}

type AcceptedAnimations = AnimationPlaybackControls;
type GroupedAnimations = AcceptedAnimations[];
declare class GroupAnimation implements AnimationPlaybackControls {
    animations: GroupedAnimations;
    constructor(animations: Array<AcceptedAnimations | undefined>);
    get finished(): Promise<any[]>;
    /**
     * TODO: Filter out cancelled or stopped animations before returning
     */
    private getAll;
    private setAll;
    attachTimeline(timeline: TimelineWithFallback): () => void;
    get time(): number;
    set time(time: number);
    get speed(): number;
    set speed(speed: number);
    get state(): any;
    get startTime(): any;
    get duration(): number;
    private runAll;
    play(): void;
    pause(): void;
    stop: () => void;
    cancel(): void;
    complete(): void;
}

declare class GroupAnimationWithThen extends GroupAnimation implements AnimationPlaybackControlsWithThen {
    then(onResolve: VoidFunction, _onReject?: VoidFunction): Promise<void>;
}

declare class JSAnimation<T extends number | string> extends WithPromise implements AnimationPlaybackControlsWithThen {
    state: AnimationPlayState;
    startTime: number | null;
    /**
     * The driver that's controlling the animation loop. Normally this is a requestAnimationFrame loop
     * but in tests we can pass in a synchronous loop.
     */
    private driver?;
    private isStopped;
    private generator;
    private calculatedDuration;
    private resolvedDuration;
    private totalDuration;
    private options;
    /**
     * The current time of the animation.
     */
    private currentTime;
    /**
     * The time at which the animation was paused.
     */
    private holdTime;
    /**
     * Playback speed as a factor. 0 would be stopped, -1 reverse and 2 double speed.
     */
    private playbackSpeed;
    private mixKeyframes;
    private mirroredGenerator;
    constructor(options: ValueAnimationOptions<T>);
    initAnimation(): void;
    updateTime(timestamp: number): void;
    tick(timestamp: number, sample?: boolean): AnimationState<T>;
    /**
     * Allows the returned animation to be awaited or promise-chained. Currently
     * resolves when the animation finishes at all but in a future update could/should
     * reject if its cancels.
     */
    then(resolve: VoidFunction, reject?: VoidFunction): Promise<void>;
    get duration(): number;
    get time(): number;
    set time(newTime: number);
    get speed(): number;
    set speed(newSpeed: number);
    play(): void;
    pause(): void;
    /**
     * This method is bound to the instance to fix a pattern where
     * animation.stop is returned as a reference from a useEffect.
     */
    stop: () => void;
    complete(): void;
    finish(): void;
    cancel(): void;
    private teardown;
    private stopDriver;
    sample(sampleTime: number): AnimationState<T>;
    attachTimeline(timeline: TimelineWithFallback): VoidFunction;
}
declare function animateValue<T extends number | string>(options: ValueAnimationOptions<T>): JSAnimation<T>;

interface NativeAnimationOptions<V extends string | number = number> extends DOMValueAnimationOptions<V> {
    pseudoElement?: string;
    startTime?: number;
}
/**
 * NativeAnimation implements AnimationPlaybackControls for the browser's Web Animations API.
 */
declare class NativeAnimation<T extends string | number> extends WithPromise implements AnimationPlaybackControlsWithThen {
    /**
     * The interfaced Web Animation API animation
     */
    protected animation: Animation;
    protected finishedTime: number | null;
    protected options: NativeAnimationOptions;
    private allowFlatten;
    private isStopped;
    private isPseudoElement;
    constructor(options?: NativeAnimationOptions);
    updateMotionValue?(value?: T): void;
    play(): void;
    pause(): void;
    complete(): void;
    cancel(): void;
    stop(): void;
    /**
     * WAAPI doesn't natively have any interruption capabilities.
     *
     * In this method, we commit styles back to the DOM before cancelling
     * the animation.
     *
     * This is designed to be overridden by NativeAnimationExtended, which
     * will create a renderless JS animation and sample it twice to calculate
     * its current value, "previous" value, and therefore allow
     * Motion to also correctly calculate velocity for any subsequent animation
     * while deferring the commit until the next animation frame.
     */
    protected commitStyles(): void;
    get duration(): number;
    get time(): number;
    set time(newTime: number);
    /**
     * The playback speed of the animation.
     * 1 = normal speed, 2 = double speed, 0.5 = half speed.
     */
    get speed(): number;
    set speed(newSpeed: number);
    get state(): AnimationPlayState;
    get startTime(): number;
    set startTime(newStartTime: number);
    /**
     * Attaches a timeline to the animation, for instance the `ScrollTimeline`.
     */
    attachTimeline({ timeline, observe }: TimelineWithFallback): VoidFunction;
}

type NativeAnimationOptionsExtended<T extends string | number> = NativeAnimationOptions & ValueAnimationOptions<T> & NativeAnimationOptions;
declare class NativeAnimationExtended<T extends string | number> extends NativeAnimation<T> {
    options: NativeAnimationOptionsExtended<T>;
    constructor(options: NativeAnimationOptionsExtended<T>);
    /**
     * WAAPI doesn't natively have any interruption capabilities.
     *
     * Rather than read commited styles back out of the DOM, we can
     * create a renderless JS animation and sample it twice to calculate
     * its current value, "previous" value, and therefore allow
     * Motion to calculate velocity for any subsequent animation.
     */
    updateMotionValue(value?: T): void;
}

declare class NativeAnimationWrapper<T extends string | number> extends NativeAnimation<T> {
    constructor(animation: Animation);
}

declare const animationMapKey: (name: string, pseudoElement?: string) => string;
declare function getAnimationMap(element: Element): Map<any, any>;

type CSSVariableName = `--${string}`;
type CSSVariableToken = `var(${CSSVariableName})`;
declare const isCSSVariableName: (key?: string | number | null) => key is `--${string}`;
declare const isCSSVariableToken: (value?: string) => value is `var(--${string})`;

declare function parseCSSVariable(current: string): string[] | undefined[];
declare function getVariableValue(current: CSSVariableToken, element: Element, depth?: number): string | number | undefined;

declare function getValueTransition(transition: any, key: string): any;

declare function inertia({ keyframes, velocity, power, timeConstant, bounceDamping, bounceStiffness, modifyTarget, min, max, restDelta, restSpeed, }: ValueAnimationOptions<number>): KeyframeGenerator<number>;

declare function defaultEasing(values: any[], easing?: EasingFunction): EasingFunction[];
declare function keyframes<T extends string | number>({ duration, keyframes: keyframeValues, times, ease, }: ValueAnimationOptions<T>): KeyframeGenerator<T>;

declare function spring(optionsOrVisualDuration?: ValueAnimationOptions<number> | number, bounce?: number): KeyframeGenerator<number>;
declare namespace spring {
    var applyToOptions: (options: Transition) => Transition;
}

/**
 * Implement a practical max duration for keyframe generation
 * to prevent infinite loops
 */
declare const maxGeneratorDuration = 20000;
declare function calcGeneratorDuration(generator: KeyframeGenerator<unknown>): number;

/**
 * Create a progress => progress easing function from a generator.
 */
declare function createGeneratorEasing(options: Transition, scale: number | undefined, createGenerator: GeneratorFactory): {
    type: string;
    ease: (progress: number) => number;
    duration: number;
};

declare function isGenerator(type?: AnimationGeneratorType): type is GeneratorFactory;

declare class DOMKeyframesResolver<T extends string | number> extends KeyframeResolver<T> {
    name: string;
    element?: WithRender;
    private removedTransforms?;
    private measuredOrigin?;
    constructor(unresolvedKeyframes: UnresolvedKeyframes<string | number>, onComplete: OnKeyframesResolved<T>, name?: string, motionValue?: MotionValue<T>, element?: WithRender);
    readKeyframes(): void;
    resolveNoneKeyframes(): void;
    measureInitialState(): void;
    measureEndState(): void;
}

declare function defaultOffset(arr: any[]): number[];

declare function fillOffset(offset: number[], remaining: number): void;

declare function convertOffsetToTimes(offset: number[], duration: number): number[];

declare function applyPxDefaults(keyframes: ValueKeyframe[] | UnresolvedValueKeyframe[], name: string): void;

declare function fillWildcards(keyframes: ValueKeyframe[] | UnresolvedValueKeyframe[]): void;

declare const cubicBezierAsString: ([a, b, c, d]: BezierDefinition) => string;

declare function isWaapiSupportedEasing(easing?: Easing | Easing[]): boolean;

declare function mapEasingToNativeEasing(easing: Easing | Easing[] | undefined, duration: number): undefined | string | string[];

declare const supportedWaapiEasing: {
    linear: string;
    ease: string;
    easeIn: string;
    easeOut: string;
    easeInOut: string;
    circIn: string;
    circOut: string;
    backIn: string;
    backOut: string;
};

declare function startWaapiAnimation(element: Element, valueName: string, keyframes: ValueKeyframesDefinition, { delay, duration, repeat, repeatType, ease, times, }?: Transition, pseudoElement?: string | undefined): Animation;

declare const supportsPartialKeyframes: () => boolean;

declare function supportsBrowserAnimation<T extends string | number>(options: ValueAnimationOptionsWithRenderContext<T>): any;

/**
 * A list of values that can be hardware-accelerated.
 */
declare const acceleratedValues: Set<string>;

declare const generateLinearEasing: (easing: EasingFunction, duration: number, resolution?: number) => string;

declare class MotionValueState {
    latest: {
        [name: string]: string | number;
    };
    private values;
    set(name: string, value: MotionValue, render?: VoidFunction, computed?: MotionValue, useDefaultValueType?: boolean): () => void;
    get(name: string): MotionValue | undefined;
    destroy(): void;
}

declare const addAttrValue: (element: HTMLElement | SVGElement, state: MotionValueState, key: string, value: MotionValue) => () => void;
declare const attrEffect: (subject: ElementOrSelector, values: Record<string, MotionValue<any>>) => () => void;

declare const propEffect: (subject: {
    [key: string]: any;
}, values: Record<string, MotionValue<any>>) => VoidFunction;

declare const addStyleValue: (element: HTMLElement | SVGElement, state: MotionValueState, key: string, value: MotionValue) => () => void;
declare const styleEffect: (subject: ElementOrSelector, values: Record<string, MotionValue<any>>) => () => void;

declare const svgEffect: (subject: ElementOrSelector, values: Record<string, MotionValue<any>>) => () => void;

declare const frame: Batcher;
declare const cancelFrame: (process: Process) => void;
declare const frameData: FrameData;
declare const frameSteps: Steps;

type Process = (data: FrameData) => void;
type Schedule = (process: Process, keepAlive?: boolean, immediate?: boolean) => Process;
interface Step {
    schedule: Schedule;
    cancel: (process: Process) => void;
    process: (data: FrameData) => void;
}
type StepId = "setup" | "read" | "resolveKeyframes" | "preUpdate" | "update" | "preRender" | "render" | "postRender";
type CancelProcess = (process: Process) => void;
type Batcher = {
    [key in StepId]: Schedule;
};
type Steps = {
    [key in StepId]: Step;
};
interface FrameData {
    delta: number;
    timestamp: number;
    isProcessing: boolean;
}

declare function createRenderBatcher(scheduleNextBatch: (callback: Function) => void, allowKeepAlive: boolean): {
    schedule: Batcher;
    cancel: (process: Process) => void;
    state: FrameData;
    steps: Steps;
};

declare const microtask: Batcher;
declare const cancelMicrotask: (process: Process) => void;

/**
 * An eventloop-synchronous alternative to performance.now().
 *
 * Ensures that time measurements remain consistent within a synchronous context.
 * Usually calling performance.now() twice within the same synchronous context
 * will return different values which isn't useful for animations when we're usually
 * trying to sync animations to the same frame.
 */
declare const time: {
    now: () => number;
    set: (newTime: number) => void;
};

declare const isDragging: {
    x: boolean;
    y: boolean;
};
declare function isDragActive(): boolean;

declare function setDragLock(axis: boolean | "x" | "y" | "lockDirection"): (() => void) | null;

type ElementOrSelector = Element | Element[] | NodeListOf<Element> | string;
interface WithQuerySelectorAll {
    querySelectorAll: Element["querySelectorAll"];
}
interface AnimationScope<T = any> {
    readonly current: T;
    animations: any[];
}
interface SelectorCache {
    [key: string]: NodeListOf<Element>;
}
declare function resolveElements(elementOrSelector: ElementOrSelector, scope?: AnimationScope, selectorCache?: SelectorCache): Element[];

/**
 * Options for event listeners.
 *
 * @public
 */
interface EventOptions {
    /**
     * Use passive event listeners. Doing so allows the browser to optimize
     * scrolling performance by not allowing the use of `preventDefault()`.
     *
     * @default true
     */
    passive?: boolean;
    /**
     * Remove the event listener after the first event.
     *
     * @default false
     */
    once?: boolean;
}

/**
 * A function to be called when a hover gesture starts.
 *
 * This function can optionally return a function that will be called
 * when the hover gesture ends.
 *
 * @public
 */
type OnHoverStartEvent = (element: Element, event: PointerEvent) => void | OnHoverEndEvent;
/**
 * A function to be called when a hover gesture ends.
 *
 * @public
 */
type OnHoverEndEvent = (event: PointerEvent) => void;
/**
 * Create a hover gesture. hover() is different to .addEventListener("pointerenter")
 * in that it has an easier syntax, filters out polyfilled touch events, interoperates
 * with drag gestures, and automatically removes the "pointerennd" event listener when the hover ends.
 *
 * @public
 */
declare function hover(elementOrSelector: ElementOrSelector, onHoverStart: OnHoverStartEvent, options?: EventOptions): VoidFunction;

interface PressGestureInfo {
    success: boolean;
}
type OnPressEndEvent = (event: PointerEvent, info: PressGestureInfo) => void;
type OnPressStartEvent = (element: Element, event: PointerEvent) => OnPressEndEvent | void;

interface PointerEventOptions extends EventOptions {
    useGlobalTarget?: boolean;
}
/**
 * Create a press gesture.
 *
 * Press is different to `"pointerdown"`, `"pointerup"` in that it
 * automatically filters out secondary pointer events like right
 * click and multitouch.
 *
 * It also adds accessibility support for keyboards, where
 * an element with a press gesture will receive focus and
 *  trigger on Enter `"keydown"` and `"keyup"` events.
 *
 * This is different to a browser's `"click"` event, which does
 * respond to keyboards but only for the `"click"` itself, rather
 * than the press start and end/cancel. The element also needs
 * to be focusable for this to work, whereas a press gesture will
 * make an element focusable by default.
 *
 * @public
 */
declare function press(targetOrSelector: ElementOrSelector, onPressStart: OnPressStartEvent, options?: PointerEventOptions): VoidFunction;

/**
 * Recursively traverse up the tree to check whether the provided child node
 * is the parent or a descendant of it.
 *
 * @param parent - Element to find
 * @param child - Element to test against parent
 */
declare const isNodeOrChild: (parent: Element | null, child?: Element | null) => boolean;

declare const isPrimaryPointer: (event: PointerEvent) => boolean;

declare function defaultTransformValue(name: string): number;
declare function parseValueFromTransform(transform: string | undefined, name: string): number;
declare const readTransformValue: (instance: HTMLElement, name: string) => number;

declare function getComputedStyle(element: HTMLElement | SVGElement, name: string): string;

declare function setStyle(element: HTMLElement | SVGElement, name: string, value: string | number): void;

declare const positionalKeys: Set<string>;

/**
 * Generate a list of every possible transform key.
 */
declare const transformPropOrder: string[];
/**
 * A quick lookup for transform props.
 */
declare const transformProps: Set<string>;

type Update = (progress: number) => void;
declare function observeTimeline(update: Update, timeline: ProgressTimeline): () => void;

declare const stepsOrder: StepId[];
type StepNames = (typeof stepsOrder)[number];

interface Summary {
    min: number;
    max: number;
    avg: number;
}
type FrameloopStatNames = "rate" | StepNames;
interface Stats<T> {
    frameloop: {
        [key in FrameloopStatNames]: T;
    };
    animations: {
        mainThread: T;
        waapi: T;
        layout: T;
    };
    layoutProjection: {
        nodes: T;
        calculatedTargetDeltas: T;
        calculatedProjections: T;
    };
}
type StatsBuffer = number[];
type FrameStats = Stats<number>;
type StatsRecording = Stats<StatsBuffer>;
type StatsSummary = Stats<Summary>;

declare function reportStats(): StatsSummary;
declare function recordStats(): typeof reportStats;

declare const activeAnimations: {
    layout: number;
    mainThread: number;
    waapi: number;
};

type InactiveStatsBuffer = {
    value: null;
    addProjectionMetrics: null;
};
type ActiveStatsBuffer = {
    value: StatsRecording;
    addProjectionMetrics: (metrics: {
        nodes: number;
        calculatedTargetDeltas: number;
        calculatedProjections: number;
    }) => void;
};
declare const statsBuffer: InactiveStatsBuffer | ActiveStatsBuffer;

type Mixer<T> = (p: number) => T;
type MixerFactory<T> = (a: T, b: T) => Mixer<T>;

interface InterpolateOptions<T> {
    clamp?: boolean;
    ease?: EasingFunction | EasingFunction[];
    mixer?: MixerFactory<T>;
}
/**
 * Create a function that maps from a numerical input array to a generic output array.
 *
 * Accepts:
 *   - Numbers
 *   - Colors (hex, hsl, hsla, rgb, rgba)
 *   - Complex (combinations of one or more numbers or strings)
 *
 * ```jsx
 * const mixColor = interpolate([0, 1], ['#fff', '#000'])
 *
 * mixColor(0.5) // 'rgba(128, 128, 128, 1)'
 * ```
 *
 * TODO Revisit this approach once we've moved to data models for values,
 * probably not needed to pregenerate mixer functions.
 *
 * @public
 */
declare function interpolate<T>(input: number[], output: T[], { clamp: isClamp, ease, mixer }?: InterpolateOptions<T>): (v: number) => T;

/**
 * Checks if an element is an HTML element in a way
 * that works across iframes
 */
declare function isHTMLElement(element: unknown): element is HTMLElement;

/**
 * Checks if an element is an SVG element in a way
 * that works across iframes
 */
declare function isSVGElement(element: unknown): element is SVGElement;

/**
 * Checks if an element is specifically an SVGSVGElement (the root SVG element)
 * in a way that works across iframes
 */
declare function isSVGSVGElement(element: unknown): element is SVGSVGElement;

declare function mix<T>(from: T, to: T): Mixer<T>;
declare function mix(from: number, to: number, p: number): number;

type Transformer = (v: any) => any;
type ValueType = {
    test: (v: any) => boolean;
    parse: (v: any) => any;
    transform?: Transformer;
    createTransformer?: (template: string) => Transformer;
    default?: any;
    getAnimatableNone?: (v: any) => any;
};
type NumberMap = {
    [key: string]: number;
};
type RGBA = {
    red: number;
    green: number;
    blue: number;
    alpha: number;
};
type HSLA = {
    hue: number;
    saturation: number;
    lightness: number;
    alpha: number;
};
type Color = HSLA | RGBA;

declare const mixLinearColor: (from: number, to: number, v: number) => number;
declare const mixColor: (from: Color | string, to: Color | string) => (p: number) => string | Color;

type MixableArray = Array<number | RGBA | HSLA | string>;
type MixableObject = {
    [key: string]: string | number | RGBA | HSLA;
};
declare function getMixer<T>(a: T): ((from: string | Color, to: string | Color) => (p: number) => string | Color) | ((origin: string | number, target: string | number) => Function) | typeof mixArray | typeof mixObject;
declare function mixArray(a: MixableArray, b: MixableArray): (p: number) => (string | number | RGBA | HSLA)[];
declare function mixObject(a: MixableObject, b: MixableObject): (v: number) => {
    [x: string]: string | number | RGBA | HSLA;
};
declare const mixComplex: (origin: string | number, target: string | number) => Function;

declare function mixImmediate<T>(a: T, b: T): (p: number) => T;

declare const mixNumber: (from: number, to: number, progress: number) => number;

declare const invisibleValues: Set<string>;
/**
 * Returns a function that, when provided a progress value between 0 and 1,
 * will return the "none" or "hidden" string only when the progress is that of
 * the origin or target.
 */
declare function mixVisibility(origin: string, target: string): (p: number) => string;

/**
 * Add the ability for test suites to manually set support flags
 * to better test more environments.
 */
declare const supportsFlags: Record<string, boolean | undefined>;

declare const supportsLinearEasing: () => boolean;

declare global {
    interface Window {
        ScrollTimeline: ScrollTimeline;
    }
}
declare class ScrollTimeline implements ProgressTimeline {
    constructor(options: ScrollOptions);
    currentTime: null | {
        value: number;
    };
    cancel?: VoidFunction;
}
declare const supportsScrollTimeline: () => boolean;

/**
 * @public
 */
interface TransformOptions<T> {
    /**
     * Clamp values to within the given range. Defaults to `true`
     *
     * @public
     */
    clamp?: boolean;
    /**
     * Easing functions to use on the interpolations between each value in the input and output ranges.
     *
     * If provided as an array, the array must be one item shorter than the input and output ranges, as the easings apply to the transition **between** each.
     *
     * @public
     */
    ease?: EasingFunction | EasingFunction[];
    /**
     * Provide a function that can interpolate between any two values in the provided range.
     *
     * @public
     */
    mixer?: (from: T, to: T) => (v: number) => any;
}
/**
 * Transforms numbers into other values by mapping them from an input range to an output range.
 * Returns the type of the input provided.
 *
 * @remarks
 *
 * Given an input range of `[0, 200]` and an output range of
 * `[0, 1]`, this function will return a value between `0` and `1`.
 * The input range must be a linear series of numbers. The output range
 * can be any supported value type, such as numbers, colors, shadows, arrays, objects and more.
 * Every value in the output range must be of the same type and in the same format.
 *
 * ```jsx
 * import * as React from "react"
 * import { transform } from "framer-motion"
 *
 * export function MyComponent() {
 *    const inputRange = [0, 200]
 *    const outputRange = [0, 1]
 *    const output = transform(100, inputRange, outputRange)
 *
 *    // Returns 0.5
 *    return <div>{output}</div>
 * }
 * ```
 *
 * @param inputValue - A number to transform between the input and output ranges.
 * @param inputRange - A linear series of numbers (either all increasing or decreasing).
 * @param outputRange - A series of numbers, colors, strings, or arrays/objects of those. Must be the same length as `inputRange`.
 * @param options - Clamp: Clamp values to within the given range. Defaults to `true`.
 *
 * @public
 */
declare function transform<T>(inputValue: number, inputRange: number[], outputRange: T[], options?: TransformOptions<T>): T;
/**
 *
 * Transforms numbers into other values by mapping them from an input range to an output range.
 *
 * Given an input range of `[0, 200]` and an output range of
 * `[0, 1]`, this function will return a value between `0` and `1`.
 * The input range must be a linear series of numbers. The output range
 * can be any supported value type, such as numbers, colors, shadows, arrays, objects and more.
 * Every value in the output range must be of the same type and in the same format.
 *
 * ```jsx
 * import * as React from "react"
 * import { Frame, transform } from "framer"
 *
 * export function MyComponent() {
 *     const inputRange = [-200, -100, 100, 200]
 *     const outputRange = [0, 1, 1, 0]
 *     const convertRange = transform(inputRange, outputRange)
 *     const output = convertRange(-150)
 *
 *     // Returns 0.5
 *     return <div>{output}</div>
 * }
 *
 * ```
 *
 * @param inputRange - A linear series of numbers (either all increasing or decreasing).
 * @param outputRange - A series of numbers, colors or strings. Must be the same length as `inputRange`.
 * @param options - Clamp: clamp values to within the given range. Defaults to `true`.
 *
 * @public
 */
declare function transform<T>(inputRange: number[], outputRange: T[], options?: TransformOptions<T>): (inputValue: number) => T;

type MapInputRange = number[];
/**
 * Create a `MotionValue` that maps the output of another `MotionValue` by
 * mapping it from one range of values into another.
 *
 * @remarks
 *
 * Given an input range of `[-200, -100, 100, 200]` and an output range of
 * `[0, 1, 1, 0]`, the returned `MotionValue` will:
 *
 * - When provided a value between `-200` and `-100`, will return a value between `0` and  `1`.
 * - When provided a value between `-100` and `100`, will return `1`.
 * - When provided a value between `100` and `200`, will return a value between `1` and  `0`
 *
 * The input range must be a linear series of numbers. The output range
 * can be any value type supported by Motion: numbers, colors, shadows, etc.
 *
 * Every value in the output range must be of the same type and in the same format.
 *
 * ```jsx
 * const x = motionValue(0)
 * const xRange = [-200, -100, 100, 200]
 * const opacityRange = [0, 1, 1, 0]
 * const opacity = mapValue(x, xRange, opacityRange)
 * ```
 *
 * @param inputValue - `MotionValue`
 * @param inputRange - A linear series of numbers (either all increasing or decreasing)
 * @param outputRange - A series of numbers, colors or strings. Must be the same length as `inputRange`.
 * @param options -
 *
 *  - clamp: boolean. Clamp values to within the given range. Defaults to `true`
 *  - ease: EasingFunction[]. Easing functions to use on the interpolations between each value in the input and output ranges. If provided as an array, the array must be one item shorter than the input and output ranges, as the easings apply to the transition between each.
 *
 * @returns `MotionValue`
 *
 * @public
 */
declare function mapValue<O>(inputValue: MotionValue<number>, inputRange: MapInputRange, outputRange: O[], options?: TransformOptions<O>): MotionValue<O>;

/**
 * Create a `MotionValue` that animates to its latest value using a spring.
 * Can either be a value or track another `MotionValue`.
 *
 * ```jsx
 * const x = motionValue(0)
 * const y = transformValue(() => x.get() * 2) // double x
 * ```
 *
 * @param transformer - A transform function. This function must be pure with no side-effects or conditional statements.
 * @returns `MotionValue`
 *
 * @public
 */
declare function springValue<T extends string | number>(source: T | MotionValue<T>, options?: SpringOptions): MotionValue<T>;
declare function attachSpring<T extends string | number>(value: MotionValue<T>, source: T | MotionValue<T>, options?: SpringOptions): VoidFunction | undefined;

type TransformInputRange = number[];
type SingleTransformer<I, O> = (input: I) => O;
type MultiTransformer<I, O> = (input: I[]) => O;
type ValueTransformer<I, O> = SingleTransformer<I, O> | MultiTransformer<I, O>;
/**
 * Create a `MotionValue` that transforms the output of other `MotionValue`s by
 * passing their latest values through a transform function.
 *
 * Whenever a `MotionValue` referred to in the provided function is updated,
 * it will be re-evaluated.
 *
 * ```jsx
 * const x = motionValue(0)
 * const y = transformValue(() => x.get() * 2) // double x
 * ```
 *
 * @param transformer - A transform function. This function must be pure with no side-effects or conditional statements.
 * @returns `MotionValue`
 *
 * @public
 */
declare function transformValue<O>(transform: () => O): MotionValue<O>;

declare const color: {
    test: (v: any) => boolean;
    parse: (v: any) => RGBA | HSLA;
    transform: (v: HSLA | RGBA | string) => string;
};

declare function parseHex(v: string): RGBA;
declare const hex: {
    test: (v: any) => boolean;
    parse: typeof parseHex;
    transform: ({ red, green, blue, alpha }: RGBA) => string;
};

declare const hsla: {
    test: (v: any) => boolean;
    parse: (v: string | Color) => HSLA;
    transform: ({ hue, saturation, lightness, alpha }: HSLA) => string;
};

declare function hslaToRgba({ hue, saturation, lightness, alpha }: HSLA): RGBA;

declare const rgbUnit: {
    transform: (v: number) => number;
    test: (v: number) => boolean;
    parse: typeof parseFloat;
};
declare const rgba: {
    test: (v: any) => boolean;
    parse: (v: string | Color) => RGBA;
    transform: ({ red, green, blue, alpha }: RGBA) => string;
};

declare function test(v: any): boolean;
type ComplexValues = Array<CSSVariableToken | string | number | Color>;
type ValueIndexes = {
    color: number[];
    number: number[];
    var: number[];
};
interface ComplexValueInfo {
    values: ComplexValues;
    split: string[];
    indexes: ValueIndexes;
    types: Array<keyof ValueIndexes>;
}
declare function analyseComplexValue(value: string | number): ComplexValueInfo;
declare function parseComplexValue(v: string | number): ComplexValues;
declare function createTransformer(source: string | number): (v: Array<CSSVariableToken | Color | number | string>) => string;
declare function getAnimatableNone$1(v: string | number): string;
declare const complex: {
    test: typeof test;
    parse: typeof parseComplexValue;
    createTransformer: typeof createTransformer;
    getAnimatableNone: typeof getAnimatableNone$1;
};

/**
 * A list of value types commonly used for dimensions
 */
declare const dimensionValueTypes: (ValueType | {
    test: (v: number) => boolean;
    parse: typeof parseFloat;
    transform: (v: number) => number;
} | {
    test: (v: string | number) => boolean;
    parse: typeof parseFloat;
    transform: (v: string | number) => string;
})[];
/**
 * Tests a dimensional value against the list of dimension ValueTypes
 */
declare const findDimensionValueType: (v: any) => ValueType | {
    test: (v: number) => boolean;
    parse: typeof parseFloat;
    transform: (v: number) => number;
} | {
    test: (v: string | number) => boolean;
    parse: typeof parseFloat;
    transform: (v: string | number) => string;
} | undefined;

interface ValueTypeMap {
    [key: string]: ValueType;
}

/**
 * A map of default value types for common values
 */
declare const defaultValueTypes: ValueTypeMap;
/**
 * Gets the default ValueType for the provided value key
 */
declare const getDefaultValueType: (key: string) => ValueType;

declare const numberValueTypes: ValueTypeMap;

declare const transformValueTypes: ValueTypeMap;

declare const number: {
    test: (v: number) => boolean;
    parse: typeof parseFloat;
    transform: (v: number) => number;
};
declare const alpha: {
    transform: (v: number) => number;
    test: (v: number) => boolean;
    parse: typeof parseFloat;
};
declare const scale: {
    default: number;
    test: (v: number) => boolean;
    parse: typeof parseFloat;
    transform: (v: number) => number;
};

declare const degrees: {
    test: (v: string | number) => boolean;
    parse: typeof parseFloat;
    transform: (v: number | string) => string;
};
declare const percent: {
    test: (v: string | number) => boolean;
    parse: typeof parseFloat;
    transform: (v: number | string) => string;
};
declare const px: {
    test: (v: string | number) => boolean;
    parse: typeof parseFloat;
    transform: (v: number | string) => string;
};
declare const vh: {
    test: (v: string | number) => boolean;
    parse: typeof parseFloat;
    transform: (v: number | string) => string;
};
declare const vw: {
    test: (v: string | number) => boolean;
    parse: typeof parseFloat;
    transform: (v: number | string) => string;
};
declare const progressPercentage: {
    parse: (v: string) => number;
    transform: (v: number) => string;
    test: (v: string | number) => boolean;
};

/**
 * Tests a provided value against a ValueType
 */
declare const testValueType: (v: any) => (type: ValueType) => boolean;

declare function getAnimatableNone(key: string, value: string): any;

/**
 * Tests a value against the list of ValueTypes
 */
declare const findValueType: (v: any) => ValueType | {
    test: (v: number) => boolean;
    parse: typeof parseFloat;
    transform: (v: number) => number;
} | {
    test: (v: string | number) => boolean;
    parse: typeof parseFloat;
    transform: (v: string | number) => string;
} | {
    test: (v: any) => boolean;
    parse: (v: any) => RGBA | HSLA;
    transform: (v: string | RGBA | HSLA) => string;
} | {
    test: (v: any) => boolean;
    parse: (v: string | number) => ComplexValues;
    createTransformer: (source: string | number) => (v: (string | number | Color)[]) => string;
    getAnimatableNone: (v: string | number) => string;
} | undefined;

/**
 * Provided a value and a ValueType, returns the value as that value type.
 */
declare const getValueAsType: (value: any, type?: ValueType) => any;

declare const isMotionValue: (value: any) => value is MotionValue<any>;

type ViewTransitionAnimationDefinition = {
    keyframes: DOMKeyframesDefinition;
    options: AnimationOptions;
};
type ViewTransitionTarget = {
    layout?: ViewTransitionAnimationDefinition;
    enter?: ViewTransitionAnimationDefinition;
    exit?: ViewTransitionAnimationDefinition;
    new?: ViewTransitionAnimationDefinition;
    old?: ViewTransitionAnimationDefinition;
};
interface ViewTransitionOptions extends AnimationOptions {
    interrupt?: "wait" | "immediate";
}
type Target = string | Element;

declare class ViewTransitionBuilder {
    private currentTarget;
    targets: Map<Target, ViewTransitionTarget>;
    update: () => void | Promise<void>;
    options: ViewTransitionOptions;
    notifyReady: (value: GroupAnimation) => void;
    private readyPromise;
    constructor(update: () => void | Promise<void>, options?: ViewTransitionOptions);
    get(selector: Target): this;
    layout(keyframes: DOMKeyframesDefinition, options?: AnimationOptions): this;
    new(keyframes: DOMKeyframesDefinition, options?: AnimationOptions): this;
    old(keyframes: DOMKeyframesDefinition, options?: AnimationOptions): this;
    enter(keyframes: DOMKeyframesDefinition, options?: AnimationOptions): this;
    exit(keyframes: DOMKeyframesDefinition, options?: AnimationOptions): this;
    crossfade(options?: AnimationOptions): this;
    updateTarget(target: "enter" | "exit" | "layout" | "new" | "old", keyframes: DOMKeyframesDefinition, options?: AnimationOptions): void;
    then(resolve: () => void, reject?: () => void): Promise<void>;
}
declare function animateView(update: () => void | Promise<void>, defaultOptions?: ViewTransitionOptions): ViewTransitionBuilder;

/**
 * @deprecated
 *
 * Import as `frame` instead.
 */
declare const sync: Batcher;
/**
 * @deprecated
 *
 * Use cancelFrame(callback) instead.
 */
declare const cancelSync: Record<string, (process: Process) => void>;

export { type AcceptedAnimations, type ActiveStatsBuffer, type AnimationGeneratorType, type AnimationOptions, type AnimationOptionsWithValueOverrides, type AnimationPlaybackControls, type AnimationPlaybackControlsWithThen, type AnimationPlaybackLifecycles, type AnimationPlaybackOptions, type AnimationScope, type AnimationState, AsyncMotionValueAnimation, type Batcher, type CSSStyleDeclarationWithTransform, type CSSVariableName, type CSSVariableToken, type CancelProcess, type Color, type ComplexValueInfo, type ComplexValues, type DOMKeyframesDefinition, DOMKeyframesResolver, type DOMValueAnimationOptions, type DecayOptions, type DurationSpringOptions, type DynamicOption, type ElementOrSelector, type EventOptions, type FrameData, type FrameStats, type GeneratorFactory, type GeneratorFactoryFunction, GroupAnimation, GroupAnimationWithThen, type GroupedAnimations, type HSLA, type InactiveStatsBuffer, type InertiaOptions, type InterpolateOptions, JSAnimation, type KeyframeGenerator, type KeyframeOptions, KeyframeResolver, type MapInputRange, type Mixer, type MixerFactory, MotionValue, type MotionValueEventCallbacks, type MotionValueOptions, type MultiTransformer, NativeAnimation, NativeAnimationExtended, type NativeAnimationOptions, type NativeAnimationOptionsExtended, NativeAnimationWrapper, type NumberMap, type OnHoverEndEvent, type OnHoverStartEvent, type OnKeyframesResolved, type OnPressEndEvent, type OnPressStartEvent, type Owner, type PassiveEffect, type PointerEventOptions, type PressGestureInfo, type Process, type ProgressTimeline, type RGBA, type RepeatType, type ResolvedKeyframes, type ResolvedValueKeyframe, type SVGAttributes, type SVGKeyframesDefinition, type SVGPathKeyframesDefinition, type SVGPathProperties, type SVGPathTransitions, type SVGTransitions, type Schedule, type SelectorCache, type SingleTransformer, type SpringOptions, type StartAnimation, type Stats, type StatsBuffer, type StatsRecording, type StatsSummary, type Step, type StepId, type Steps, type StyleKeyframesDefinition, type StyleTransitions, type Subscriber, type Summary, type Target, type TimelineWithFallback, type TransformInputRange, type TransformOptions, type TransformProperties, type Transformer, type Transition, type UnresolvedKeyframes, type UnresolvedValueKeyframe, type ValueAnimationOptions, type ValueAnimationOptionsWithRenderContext, type ValueAnimationTransition, type ValueIndexes, type ValueKeyframe, type ValueKeyframesDefinition, type ValueTransformer, type ValueType, type ValueTypeMap, type VariableKeyframesDefinition, type VariableTransitions, type VelocityOptions, type ViewTransitionAnimationDefinition, ViewTransitionBuilder, type ViewTransitionOptions, type ViewTransitionTarget, type WithQuerySelectorAll, acceleratedValues, activeAnimations, addAttrValue, addStyleValue, alpha, analyseComplexValue, animateValue, animateView, animationMapKey, applyPxDefaults, attachSpring, attrEffect, calcGeneratorDuration, cancelFrame, cancelMicrotask, cancelSync, collectMotionValues, color, complex, convertOffsetToTimes, createGeneratorEasing, createRenderBatcher, cubicBezierAsString, defaultEasing, defaultOffset, defaultTransformValue, defaultValueTypes, degrees, dimensionValueTypes, fillOffset, fillWildcards, findDimensionValueType, findValueType, flushKeyframeResolvers, frame, frameData, frameSteps, generateLinearEasing, getAnimatableNone, getAnimationMap, getComputedStyle, getDefaultValueType, getMixer, getValueAsType, getValueTransition, getVariableValue, hex, hover, hsla, hslaToRgba, inertia, interpolate, invisibleValues, isCSSVariableName, isCSSVariableToken, isDragActive, isDragging, isGenerator, isHTMLElement, isMotionValue, isNodeOrChild, isPrimaryPointer, isSVGElement, isSVGSVGElement, isWaapiSupportedEasing, keyframes, mapEasingToNativeEasing, mapValue, maxGeneratorDuration, microtask, mix, mixArray, mixColor, mixComplex, mixImmediate, mixLinearColor, mixNumber, mixObject, mixVisibility, motionValue, number, numberValueTypes, observeTimeline, parseCSSVariable, parseValueFromTransform, percent, positionalKeys, press, progressPercentage, propEffect, px, readTransformValue, recordStats, resolveElements, rgbUnit, rgba, scale, setDragLock, setStyle, spring, springValue, startWaapiAnimation, statsBuffer, styleEffect, supportedWaapiEasing, supportsBrowserAnimation, supportsFlags, supportsLinearEasing, supportsPartialKeyframes, supportsScrollTimeline, svgEffect, sync, testValueType, time, transform, transformPropOrder, transformProps, transformValue, transformValueTypes, vh, vw };
