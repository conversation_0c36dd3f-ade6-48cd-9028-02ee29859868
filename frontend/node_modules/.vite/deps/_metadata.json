{"hash": "46f7bcf7", "configHash": "cefc89f1", "lockfileHash": "d5168334", "browserHash": "b68cf903", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "322f6785", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "34b1b7b2", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "3bd0f922", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "4bba9a97", "needsInterop": true}, "@heroicons/react/24/outline": {"src": "../../@heroicons/react/24/outline/esm/index.js", "file": "@heroicons_react_24_outline.js", "fileHash": "f8e733fc", "needsInterop": false}, "@monaco-editor/react": {"src": "../../@monaco-editor/react/dist/index.mjs", "file": "@monaco-editor_react.js", "fileHash": "ea82becd", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "6c27c630", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "645b086e", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "75089139", "needsInterop": true}, "react-hot-toast": {"src": "../../react-hot-toast/dist/index.mjs", "file": "react-hot-toast.js", "fileHash": "94c1d777", "needsInterop": false}, "react-markdown": {"src": "../../react-markdown/index.js", "file": "react-markdown.js", "fileHash": "08b9f515", "needsInterop": false}, "react-resizable-panels": {"src": "../../react-resizable-panels/dist/react-resizable-panels.browser.development.js", "file": "react-resizable-panels.js", "fileHash": "3f08da82", "needsInterop": false}, "remark-gfm": {"src": "../../remark-gfm/index.js", "file": "remark-gfm.js", "fileHash": "661707cb", "needsInterop": false}}, "chunks": {"chunk-UIS6UNVN": {"file": "chunk-UIS6UNVN.js"}, "chunk-JNNNAK6O": {"file": "chunk-JNNNAK6O.js"}, "chunk-A7ECLLTJ": {"file": "chunk-A7ECLLTJ.js"}, "chunk-HSUUC2QV": {"file": "chunk-HSUUC2QV.js"}, "chunk-DC5AMYBS": {"file": "chunk-DC5AMYBS.js"}}}