/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

/* Use custom CSS vars to expose padding into parent select for padding calculation */
.monaco-select-box-dropdown-padding {
	--dropdown-padding-top: 1px;
	--dropdown-padding-bottom: 1px;
}

.hc-black .monaco-select-box-dropdown-padding,
.hc-light .monaco-select-box-dropdown-padding {
	--dropdown-padding-top: 3px;
	--dropdown-padding-bottom: 4px;
}

.monaco-select-box-dropdown-container {
	display: none;
	box-sizing:	border-box;
}

.monaco-select-box-dropdown-container > .select-box-details-pane > .select-box-description-markdown * {
	margin: 0;
}

.monaco-select-box-dropdown-container > .select-box-details-pane > .select-box-description-markdown a:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

.monaco-select-box-dropdown-container > .select-box-details-pane > .select-box-description-markdown code {
	line-height: 15px; /** For some reason, this is needed, otherwise <code> will take up 20px height */
	font-family: var(--monaco-monospace-font);
}


.monaco-select-box-dropdown-container.visible {
	display: flex;
	flex-direction: column;
	text-align: left;
	width: 1px;
	overflow: hidden;
	border-bottom-left-radius: 3px;
	border-bottom-right-radius: 3px;
}

.monaco-select-box-dropdown-container > .select-box-dropdown-list-container {
	flex: 0 0 auto;
	align-self: flex-start;
	padding-top: var(--dropdown-padding-top);
	padding-bottom: var(--dropdown-padding-bottom);
	padding-left: 1px;
	padding-right: 1px;
	width: 100%;
	overflow: hidden;
	box-sizing:	border-box;
}

.monaco-select-box-dropdown-container > .select-box-details-pane {
	padding: 5px;
}

.hc-black .monaco-select-box-dropdown-container > .select-box-dropdown-list-container {
	padding-top: var(--dropdown-padding-top);
	padding-bottom: var(--dropdown-padding-bottom);
}

.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row {
	cursor: pointer;
}

.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row > .option-text {
	text-overflow: ellipsis;
	overflow: hidden;
	padding-left: 3.5px;
	white-space: nowrap;
	float: left;
}

.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row > .option-detail {
	text-overflow: ellipsis;
	overflow: hidden;
	padding-left: 3.5px;
	white-space: nowrap;
	float: left;
	opacity: 0.7;
}

.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row > .option-decorator-right {
	text-overflow: ellipsis;
	overflow: hidden;
	padding-right: 10px;
	white-space: nowrap;
	float: right;
}


/* Accepted CSS hiding technique for accessibility reader text  */
/* https://webaim.org/techniques/css/invisiblecontent/ */

.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row > .visually-hidden {
		position: absolute;
		left: -10000px;
		top: auto;
		width: 1px;
		height: 1px;
		overflow: hidden;
}

.monaco-select-box-dropdown-container > .select-box-dropdown-container-width-control {
	flex: 1 1 auto;
	align-self: flex-start;
	opacity: 0;
}

.monaco-select-box-dropdown-container > .select-box-dropdown-container-width-control > .width-control-div {
	overflow: hidden;
	max-height: 0px;
}

.monaco-select-box-dropdown-container > .select-box-dropdown-container-width-control > .width-control-div > .option-text-width-control {
	padding-left: 4px;
	padding-right: 8px;
	white-space: nowrap;
}
