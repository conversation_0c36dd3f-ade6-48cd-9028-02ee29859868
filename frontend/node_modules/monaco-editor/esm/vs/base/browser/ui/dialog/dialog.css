/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

/** Dialog: Modal Block */
.monaco-dialog-modal-block {
	position: fixed;
	height: 100%;
	width: 100%;
	left:0;
	top:0;
	z-index: 2600;
	display: flex;
	justify-content: center;
	align-items: center;
}

.monaco-dialog-modal-block.dimmed {
	background: rgba(0, 0, 0, 0.3);
}

/** Dialog: Container */
.monaco-dialog-box {
	display: flex;
	flex-direction: column-reverse;
	width: min-content;
	min-width: 500px;
	max-width: 90vw;
	min-height: 75px;
	padding: 10px;
	transform: translate3d(0px, 0px, 0px);
	border-radius: 3px;
}

/** Dialog: Title Actions Row */
.monaco-dialog-box .dialog-toolbar-row {
	height: 22px;
	padding-bottom: 4px;
}

.monaco-dialog-box .dialog-toolbar-row .actions-container {
	justify-content: flex-end;
}

/** Dialog: Message Row */
.monaco-dialog-box .dialog-message-row {
	display: flex;
	flex-grow: 1;
	align-items: center;
	padding: 0 10px;
}

.monaco-dialog-box .dialog-message-row > .dialog-icon.codicon {
	flex: 0 0 48px;
	height: 48px;
	align-self: baseline;
	font-size: 48px;
}

/** Dialog: Message Container */
.monaco-dialog-box .dialog-message-row .dialog-message-container {
	display: flex;
	flex-direction: column;
	overflow: hidden;
	text-overflow: ellipsis;
	padding-left: 24px;
	user-select: text;
	-webkit-user-select: text;
	word-wrap: break-word; /* never overflow long words, but break to next line */
	white-space: normal;
}

/** Dialog: Message */
.monaco-dialog-box .dialog-message-row .dialog-message-container .dialog-message {
	line-height: 22px;
	font-size: 18px;
	flex: 1; /* let the message always grow */
	white-space: normal;
	word-wrap: break-word; /* never overflow long words, but break to next line */
	min-height: 48px; /* matches icon height */
	margin-bottom: 8px;
	display: flex;
	align-items: center;
}

/** Dialog: Details */
.monaco-dialog-box .dialog-message-row .dialog-message-container .dialog-message-detail {
	line-height: 22px;
	flex: 1; /* let the message always grow */
}

.monaco-dialog-box .dialog-message-row .dialog-message-container .dialog-message a:focus {
	outline-width: 1px;
	outline-style: solid;
}

/** Dialog: Checkbox */
.monaco-dialog-box .dialog-message-row .dialog-message-container .dialog-checkbox-row {
	padding: 15px 0px 0px;
	display: flex;
}

.monaco-dialog-box .dialog-message-row .dialog-message-container .dialog-checkbox-row .dialog-checkbox-message {
	cursor: pointer;
	user-select: none;
	-webkit-user-select: none;
}

/** Dialog: Input */
.monaco-dialog-box .dialog-message-row .dialog-message-container .dialog-message-input {
	padding: 15px 0px 0px;
	display: flex;
}

.monaco-dialog-box .dialog-message-row .dialog-message-container .dialog-message-input .monaco-inputbox {
	flex: 1;
}

/** Dialog: File Path */
.monaco-dialog-box code {
	font-family: var(--monaco-monospace-font);
}

/** Dialog: Buttons Row */
.monaco-dialog-box > .dialog-buttons-row {
	display: flex;
	align-items: center;
	padding-right: 1px;
	overflow: hidden; /* buttons row should never overflow */
}

.monaco-dialog-box > .dialog-buttons-row {
	display: flex;
	white-space: nowrap;
	padding: 20px 10px 10px;
}

/** Dialog: Buttons */
.monaco-dialog-box > .dialog-buttons-row > .dialog-buttons {
	display: flex;
	width: 100%;
	justify-content: flex-end;
	overflow: hidden;
	margin-left: 67px; /* for long buttons, force align with text */
}

.monaco-dialog-box > .dialog-buttons-row > .dialog-buttons > .monaco-button {
	width: fit-content;
	padding: 5px 10px;
	overflow: hidden;
	text-overflow: ellipsis;
	margin: 4px 5px; /* allows button focus outline to be visible */
	outline-offset: 2px !important;
}
