{"version": 3, "sources": ["out-editor/nls.messages.zh-tw.js"], "sourcesContent": ["/*!-----------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/vscode/blob/main/LICENSE.txt\n *-----------------------------------------------------------*/\n\nglobalThis._VSCODE_NLS_MESSAGES=[\"{0} ({1})\",\"輸入\",\"大小寫須相符\",\"全字拼寫須相符\",\"使用規則運算式\",\"輸入\",\"保留大小寫\",\"使用 {0} 在可存取檢視中檢查此項目。\",\"透過目前無法透過按鍵繫結關係觸發的開啟可存取檢視命令，在可存取檢視中檢查此項目。\",\"錯誤: {0}\",\"警告: {0}\",\"資訊: {0}\",\" 或 {0} 以取得歷程記錄\",\" ({0} 以取得歷程記錄)\",\"已清除輸入\",\"未繫結\",\"選取方塊\",\"更多操作\",\"篩選\",\"模糊比對\",\"要篩選的類型\",\"要搜尋的類型\",\"要搜尋的類型\",\"關閉\",\"沒有結果\",\"找不到結果。\",null,\"(空的)\",\"{0}: {1}\",\"發生系統錯誤 ({0})\",\"發生未知的錯誤。如需詳細資訊，請參閱記錄檔。\",\"發生未知的錯誤。如需詳細資訊，請參閱記錄檔。\",\"{0} (總計 {1} 個錯誤)\",\"發生未知的錯誤。如需詳細資訊，請參閱記錄檔。\",\"Ctrl\",\"Shift\",\"Alt\",\"Windows\",\"Ctrl\",\"Shift\",\"Alt\",\"超級鍵\",\"Control\",\"Shift\",\"選項\",\"命令\",\"Control\",\"Shift\",\"Alt\",\"Windows\",\"Control\",\"Shift\",\"Alt\",\"超級鍵\",null,null,null,null,null,\"即使行的長度過長，仍要堅持至結尾\",\"即使行的長度過長，仍要堅持至結尾\",\"已移除次要資料指標\",\"復原(&&U)\",\"復原\",\"取消復原(&&R)\",\"重做\",\"全選(&&S)\",\"全選\",\"按住 {0} 鍵將滑鼠懸停在上面\",\"正在載入...\",\"游標數目已限制為 {0}。請考慮使用 [尋找和取代](https://code.visualstudio.com/docs/editor/codebasics#_find-and-replace) 進行較大型的變更，或增加編輯器的多重游標限制設定。\",\"增加多重游標限制\",\"切換摺疊未變更的區域\",\"切換顯示移動的程式碼區塊\",\"當空間有限時切換使用內嵌檢視\",\"Diff 編輯器\",\"切換側邊\",\"結束比較移動\",\"摺疊所有未變更的區域\",\"顯示所有未變更的區域\",\"還原\",\"可存取的 Diff 檢視器\",\"移至下一個差異\",\"移至上一個差異\",\"易存取差異檢視器中的 [插入] 圖示。\",\"易存取差異檢視器中的 [移除] 圖示。\",\"易存取差異檢視器中的 [關閉] 圖示。\",\"關閉\",\"可存取的 Diff 檢視器。使用向上和向下箭頭來瀏覽。\",\"未變更任一行\",\"已變更 1 行\",\"已變更 {0} 行\",\"{1} 項差異中的第 {0} 項: 原始行 {2}、{3}，修改行 {4}、{5}\",\"空白\",\"{0} 未變更行 {1}\",\"{0} 原始行 {1} 修改的行 {2}\",\"+ {0} 修改行 {1}\",\"- {0} 原始行 {1}\",\" 使用 {0} 以開啟協助工具說明。\",\"複製已刪除的行\",\"複製已刪除的行\",\"複製變更的行\",\"複製變更的行\",\"複製已刪除的行 （{0}）\",\"複製變更的行 ({0})\",\"還原此變更\",\"空間有限時使用內嵌檢視\",\"顯示移動的程式碼區塊\",\"還原區塊\",\"還原選取範圍\",\"開啟易存取差異檢視器\",\"摺疊未變更的區域\",\"{0} 條隱藏行\",\"按一下或拖曳以在上方顯示更多內容\",\"顯示未變更的區域\",\"按一下或拖曳以在下方顯示更多內容\",\"{0} 條隱藏行\",\"按兩下以展開\",\"行 {0}-{1} 的程式碼已移動，且有所變更\",\"行 {0}-{1} 的程式碼已移動，且有所變更\",\"程式碼已移至行 {0}-{1}\",\"行 {0}-{1} 的程式碼已移動\",\"還原選取的變更\",\"還原變更\",\"在 Diff 編輯器中移動的文字的框線色彩。\",\"在 Diff 編輯器中移動的文字的作用中框線色彩。\",\"未變更的區域小工具周圍的陰影色彩。\",\"Diff 編輯器中用於插入的線條裝飾。\",\"Diff 編輯器中用於移除的線條裝飾。\",\"Diff 編輯器標頭的背景色彩\",\"多重檔案 Diff 編輯器的背景色彩\",\"多重檔案 Diff 編輯器的框線色彩\",\"沒有變更的檔案\",\"編輯器\",\"與 Tab 相等的空格數量。當 {0} 已開啟時，會根據檔案內容覆寫此設定。\",\"用於縮排或 'tabSize' 使用 `\\\"editor.tabSize\\\"` 值的空格數目。當 '#editor.detectIndentation#' 開啟時，會根據檔案內容覆寫這個設定。\",\"在按 `Tab` 時插入空格。當 {0} 開啟時，會根據檔案內容覆寫此設定。\",\"根據檔案內容，控制當檔案開啟時，是否自動偵測 {0} 和 {1}。\",\"移除尾端自動插入的空白字元。\",\"針對大型檔案停用部分高記憶體需求功能的特殊處理方式。\",\"關閉 Word 型建議。\",\"僅建議來自使用中文件中的字組。\",\"建議來自所有已開啟文件中，語言相同的字組。\",\"建議來自所有已開啟文件中的字組。\",\"控制是否應該根據文件中的文字來計算完成，以及從哪些文件計算。\",\"所有彩色主題皆已啟用語意醒目提示。\",\"所有彩色主題皆已停用語意醒目提示。\",\"語意醒目提示由目前之彩色佈景主題的 'semanticHighlighting' 設定所設定。\",\"控制 semanticHighlighting 是否會為支援的語言顯示。\",\"即使按兩下內容或按 `Escape`，仍保持瞄孔編輯器開啟。\",\"因效能的緣故，不會將超過此高度的行 Token 化\",\"控制權杖化是否應該在 Web 工作者上非同步進行。\",\"控制是否應該記錄非同步權杖化。僅適用偵錯。\",\"控制是否應使用舊版背景 Token 化來驗證非同步 Token 化。可能會減慢 Token 化的速度。僅用於偵錯。\",\"控制是否應該開啟樹狀座標剖析並收集遙測。設定特定語言的 'editor.experimental.preferTreeSitter' 會優先。\",\"定義增加或減少縮排的括弧符號。\",\"左括弧字元或字串順序。\",\"右括弧字元或字串順序。\",\"定義當括弧配對著色已啟用時，由其巢狀層級著色的括弧配對。\",\"左括弧字元或字串順序。\",\"右括弧字元或字串順序。\",\"取消 Diff 計算前的逾時限制 (毫秒)。若無逾時，請使用 0。\",\"要計算差異的檔案大小上限 (MB)。使用 0 表示無限制。\",\"控制 Diff 編輯器要並排或內嵌顯示 Diff。\",\"如果差異編輯器寬度小於此值，則使用內嵌檢視。\",\"如果啟用且編輯器寬度太小，則會使用內嵌檢視。\",\"啟用時，Diff 編輯器會在其字元邊緣顯示箭頭，以還原變更。\",\"啟用時，Diff 編輯器會顯示還原和暫存動作的特殊裝訂邊。\",\"啟用時，Diff 編輯器會忽略前置或後置空格的變更。\",\"控制 Diff 編輯器是否要為新增/移除的變更顯示 +/- 標記。\",\"控制編輯器是否顯示 codelens。\",\"一律不換行。\",\"依檢視區寬度換行。\",\"將依據 {0} 設定自動換行。\",\"使用舊版差異演算法。\",\"使用進階版差異演算法。\",\"控制差異編輯器是否顯示未變更的區域。\",\"控制未變更區域的使用行數。\",\"控制未變更區域的最小使用行數。\",\"控制比較未變更的區域時，要使用多少行作為內容。\",\"控制 Diff 編輯器是否應該顯示偵測到的程式碼移動。\",\"控制差異編輯器是否顯示空白裝飾項目，以查看插入或刪除字元的位置。\",\"如果已啟用且編輯器使用內嵌檢視，則文字變更會以內嵌方式呈現。\",\"使用平台 API 以偵測螢幕助讀程式附加。\",\"使用螢幕助讀程式將使用方式最佳化。\",\"假設螢幕助讀程式未連結。\",\"控制 UI 是否應於已為螢幕助讀程式最佳化的模式中執行。\",\"控制是否要在註解時插入空白字元。\",\"控制是否應以行註解的切換、新增或移除動作，忽略空白的行。\",\"控制複製時不選取任何項目是否會複製目前程式行。\",\"控制在輸入期間是否要跳過游標來尋找相符的項目。\",\"永不從編輯器選取範圍中植入搜尋字串。\",\"一律從編輯器選取範圍中植入搜尋字串，包括游標位置的字。\",\"只有來自編輯器選取範圍中的植入搜尋字串。\",\"控制 [尋找小工具] 中的搜尋字串是否來自編輯器選取項目。\",\"永不自動開啟 [在選取範圍中尋找] (預設)。\",\"一律自動開啟 [在選取範圍中尋找]。\",\"選取多行內容時，自動開啟 [在選取範圍中尋找]。\",\"控制自動開啟 [在選取範圍中尋找] 的條件。\",\"控制尋找小工具是否在 macOS 上讀取或修改共用尋找剪貼簿。\",\"控制尋找小工具是否應在編輯器頂端額外新增行。若為 true，當您可看到尋找小工具時，您的捲動範圍會超過第一行。\",\"當再也找不到其他相符項目時，控制是否自動從開頭 (或結尾) 重新開始搜尋。\",\"啟用/停用連字字型 ('calt' 和 'liga' 字型功能)。將此項變更為字串，以精確控制 'font-feature-settings' CSS 屬性。\",\"明確的 'font-feature-settings' CSS 屬性。如果只需要開啟/關閉連字，可以改為傳遞布林值。\",\"設定連字字型或字型功能。可以是布林值以啟用/停用連字，或代表 CSS 'font-feature-settings' 屬性的字串。\",\"啟用/停用從 font-weight 到 font-variation-settings 的轉換。將此設定變更為字串，以更精細地控制 'font-variation-settings' CSS 屬性。\",\"明確的 'font-variation-settings' CSS 屬性。如果只需要將 font-weight 轉換為 font-variation-settings，可以改為傳遞布林值。\",\"設定字型變化。可以是布林值，以啟用/停用從 font-weight 到 font-variation-settings 的轉換，或是字串，做為 CSS 'font-variation-settings' 屬性的值。\",\"控制字型大小 (像素)。\",\"只允許「一般」及「粗體」關鍵字，或介於 1 到 1000 之間的數值。\",\"控制字型粗細。接受「一般」及「粗體」關鍵字，或介於 1 到 1000 之間的數值。\",\"顯示結果的預覽檢視 (預設)\",\"移至主要結果並顯示預覽檢視\",\"前往主要結果，並對其他人啟用無預覽瀏覽\",\"此設定已淘汰，請改用 'editor.editor.gotoLocation.multipleDefinitions' 或 'editor.editor.gotoLocation.multipleImplementations' 等單獨設定。\",\"控制 'Go to Definition' 命令在有多個目標位置存在時的行為。\",\"控制 'Go to Type Definition' 命令在有多個目標位置存在時的行為。\",\"控制 'Go to Declaration' 命令在有多個目標位置存在時的行為。\",\"控制 'Go to Implementations' 命令在有多個目標位置存在時的行為。\",\"控制 'Go to References' 命令在有多個目標位置存在時的行為。\",\"當 'Go to Definition' 的結果為目前位置時，正在執行的替代命令識別碼。\",\"當 'Go to Type Definition' 的結果為目前位置時，正在執行的替代命令識別碼。\",\"當 'Go to Declaration' 的結果為目前位置時，正在執行的替代命令識別碼。\",\"當 'Go to Implementation' 的結果為目前位置時，正在執行的替代命令識別碼。\",\"當 'Go to Reference' 的結果為目前位置時，正在執行的替代命令識別碼。\",\"控制是否顯示暫留。\",\"控制暫留顯示的延遲時間 (以毫秒為單位)。\",\"控制當滑鼠移過時，是否應保持顯示暫留。\",\"控制暫留隱藏的延遲時間 (以毫秒為單位)。需要啟用 `editor.hover.sticky`。\",\"如果有空間，則偏好在行上方顯示游標。\",\"假設所有字元的寬度均相同。這是一種快速的演算法，適用於等寬字型，以及字符寬度相同的部分指令碼 (例如拉丁文字元)。\",\"將外圍點計算委派給瀏覽器。這是緩慢的演算法，如果檔案較大可能會導致凍結，但在所有情況下都正常運作。\",\"控制計算外圍點的演算法。請注意，在協助工具模式中，會使用進階來獲得最佳體驗。\",\"停用程式代碼動作功能表。\",\"當游標位於含有程式代碼的行時，顯示程式碼動作功能表。\",\"當游標位於含有程式代碼的行或空行時，顯示程式碼動作功能表。\",\"在編輯器中啟用程式碼動作燈泡。\",\"在編輯器頂端捲動期間顯示巢狀的目前範圍。\",\"定義要顯示的自黏線數目上限。\",\"定義要用於判斷要黏住的線條的模型。如果大綱模型不存在，則會回到摺疊提供者模型，其會回到縮排模型。這三種情況中會遵守此順序。\",\"使用編輯器的水平捲軸，啟用自黏捲動的捲動。\",\"啟用編輯器中的內嵌提示。\",\"已啟用內嵌提示\",\"預設會顯示內嵌提示，並在按住 {0} 時隱藏\",\"預設會隱藏內嵌提示，並在按住 {0} 時顯示\",\"已停用內嵌提示\",\"控制編輯器中內嵌提示的字型大小。當設定的值小於 {1} 或大於編輯器字型大小時，則會使用{0} 預設值。\",\"控制編輯器中，內嵌提示的字型家族。設定為空白時，則會使用 {0}。\",\"在編輯器中啟用的內嵌提示周圍的填補。\",\"控制行高。\\r\\n - 使用 0 從字型大小自動計算行高。\\r\\n - 使用介於 0 和 8 之間的值作為字型大小的乘數。\\r\\n - 大於或等於 8 的值將用來作為有效值。\",\"控制是否會顯示縮圖\",\"控制是否會自動隱藏縮圖。\",\"縮圖大小與編輯器內容相同 (且可能會捲動)。\",\"縮圖會視需要伸縮，以填滿該編輯器的高度 (無捲動)。\",\"縮圖將視需要縮小，一律不會大於該編輯器 (無捲動)。\",\"控制縮圖的大小。\",\"控制要在哪端呈現縮圖。\",\"控制何時顯示迷你地圖滑桿。\",\"縮圖內所繪製的內容大小: 1、2 或 3。\",\"顯示行中的實際字元，而不是色彩區塊。\",\"限制縮圖的寬度，最多顯示某個數目的列。\",\"控制指定的區域是否在縮圖中顯示為區段標頭。\",\"控制是否將 MARK: 註解顯示為縮圖中的區段標頭。\",\"控制縮圖中區段標頭的字型大小。\",\"控制區段標頭字元之間 (像素) 的空間量。這有助於小字型的標頭可讀性。\",\"控制編輯器上邊緣與第一行之間的空格數。\",\"控制編輯器下邊緣與最後一行之間的空格數。\",\"啟用快顯，在您鍵入的同時顯示參數文件和類型資訊。\",\"控制提示功能表是否在清單結尾時循環或關閉。\",\"快速建議會顯示在建議小工具內\",\"快速建議會顯示為浮水印文字\",\"已停用快速建議\",\"允許在字串內顯示即時建議。\",\"允許在註解中顯示即時建議。\",\"允許在字串與註解以外之處顯示即時建議。\",\"控制輸入時是否應自動顯示建議。這可控制在註解、字串及其他程式碼中的輸入。可設定快速建議以隱形浮出文字或建議小工具顯示。另外也請注意 {0} 設定，它會控制建議是否由特殊字元所觸發。\",\"不顯示行號。\",\"行號以絕對值顯示。\",\"行號以目前游標的相對值顯示。\",\"每 10 行顯示行號。\",\"控制行號的顯示。\",\"這個編輯器尺規會轉譯的等寬字元數。\",\"此編輯器尺規的色彩。\",\"在某個數目的等寬字元之後顯示垂直尺規。如有多個尺規，就會使用多個值。若陣列空白，就不會繪製任何尺規。\",\"垂直捲軸只有在必要時才可見。\",\"垂直捲軸永遠可見。\",\"垂直捲軸永遠隱藏。\",\"控制項垂直捲軸的可見度。\",\"水平捲軸只有在必要時才可見。\",\"水平捲軸永遠可見。\",\"水平捲軸永遠隱藏。\",\"控制項水平捲軸的可見度。\",\"垂直捲軸的寬度。\",\"水平捲軸的高度。\",\"控制項按一下是否按頁面滾動或跳到按一下位置。\",\"設定時，水平捲軸不會增加編輯器內容的大小。\",\"控制是否醒目提示所有非基本的 ASCII 字元。只有介於 U+0020和 U+007E、tab、換行和歸位字元之間的字元會視為基本 ASCII。\",\"控制是否只保留空格或完全沒有寬度之字元的醒目提示。\",\"控制是否醒目提示與基本 ASCII 字元混淆的字元，但目前使用者地區設定中通用的字元除外。\",\"控制註解中的字元是否也應受到 Unicode 醒目提示。\",\"控制字串中的字元是否也應受到 Unicode 醒目提示。\",\"定義未醒目提示的允許字元。\",\"不會將允許地區設置中常見的 Unicode 字元強調顯示。\",\"控制是否要在編輯器中自動顯示內嵌建議。\",\"每當顯示內嵌建議時，顯示內嵌建議工具列。\",\"每當游標停留在內嵌建議上方時，顯示內嵌建議工具列。\",\"永不顯示內嵌建議工具列。\",\"控制何時顯示內嵌建議工具列。\",\"控制內嵌建議如何與建議小工具互動。如果啟用，有可用的內嵌建議時，不會自動顯示建議小工具。\",\"控制內嵌建議的字體系列。\",null,null,null,null,null,null,\"控制是否啟用成對方括弧著色。使用 {0} 覆寫括弧亮顯顏色。\",\"控制每個括弧類型是否有自己的獨立色彩集區。\",\"啟用括弧配對輔助線。\",\"只啟用使用中括弧組的括弧配對輔助線。\",\"停用括弧配對輔助線。\",\"控制是否啟用成對方括弧指南。\",\"啟用水平輔助線作為垂直括弧配對輔助線的新增功能。\",\"只啟用使用中括弧配對的水平輔助線。\",\"停用水平括弧配對輔助線。\",\"控制是否啟用水平成對方括弧輔助線。\",\"控制編輯器是否應醒目提示使用中的成對括弧。\",\"控制編輯器是否應顯示縮排輔助線。\",\"醒目提示使用中的縮排輔助線。\",\"即使醒目提示括弧輔助線，仍醒目提示使用中的縮排輔助線。\",\"不要醒目提示使用中的縮排輔助線。\",\"控制編輯器是否應醒目提示使用中的縮排輔助線。\",\"插入建議而不覆寫游標旁的文字。\",\"插入建議並覆寫游標旁的文字。\",\"控制是否要在接受完成時覆寫字組。請注意，這取決於加入此功能的延伸模組。\",\"控制對於拚錯字是否進行篩選和排序其建議\",\"控制排序是否偏好游標附近的字組。\",\"控制記錄的建議選取項目是否在多個工作區和視窗間共用 (需要 `#editor.suggestSelection#`)。\",\"自動觸發 IntelliSense 時一律選取建議。\",\"自動觸發 IntelliSense 時永不選取建議。\",\"只有在從觸發字元觸發 IntelliSense 時，才選取建議。\",\"只有在您輸入時觸發 IntelliSense 時，才選取建議。\",\"控制小工具顯示時是否選取建議。請注意，這只適用於({0} 和 {1}) 自動觸發的建議，而且一律會在明確叫用時選取建議，例如透過 'Ctrl+Space'。\",\"控制正在使用的程式碼片段是否會避免快速建議。\",\"控制要在建議中顯示或隱藏圖示。\",\"控制建議小工具底下的狀態列可見度。\",\"控制是否要在編輯器中預覽建議結果。\",\"控制建議詳細資料是以內嵌於標籤的方式顯示，還是只在詳細資料小工具中顯示。\",\"此設定已淘汰。建議小工具現可調整大小。\",\"此設定已淘汰，請改用 'editor.suggest.showKeywords' 或 'editor.suggest.showSnippets' 等單獨設定。\",\"啟用時，IntelliSense 顯示「方法」建議。\",\"啟用時，IntelliSense 顯示「函式」建議。\",\"啟用時，IntelliSense 顯示「建構函式」建議。\",\"啟用時，IntelliSense 顯示「已取代」建議。\",\"啟用時，IntelliSense 篩選會要求第一個字元符合文字開頭，例如 `Console` 或 `WebCoNtext` 上的 `c`，但不是 `description` 上的 _not_。停用時，IntelliSense 會顯示更多結果，但仍會依相符品質排序結果。\",\"啟用時，IntelliSense 顯示「欄位」建議。\",\"啟用時，IntelliSense 顯示「變數」建議。\",\"啟用時，IntelliSense 顯示「類別」建議。\",\"啟用時，IntelliSense 顯示「結構」建議。\",\"啟用時，IntelliSense 顯示「介面」建議。\",\"啟用時，IntelliSense 顯示「模組」建議。\",\"啟用時，IntelliSense 顯示「屬性」建議。\",\"啟用時，IntelliSense 顯示「事件」建議。\",\"啟用時，IntelliSense 顯示「運算子」建議。\",\"啟用時，IntelliSense 顯示「單位」建議。\",\"啟用時，IntelliSense 顯示「值」建議。\",\"啟用時，IntelliSense 顯示「常數」建議。\",\"啟用時，IntelliSense 顯示「列舉」建議。\",\"啟用時，IntelliSense 顯示「enumMember」建議。\",\"啟用時，IntelliSense 顯示「關鍵字」建議。\",\"啟用時，IntelliSense 顯示「文字」建議。\",\"啟用時，IntelliSense 顯示「色彩」建議。\",\"啟用時，IntelliSense 顯示「檔案」建議。\",\"啟用時，IntelliSense 顯示「參考」建議。\",\"啟用時，IntelliSense 顯示「customcolor」建議。\",\"啟用時，IntelliSense 顯示「資料夾」建議。\",\"啟用時，IntelliSense 顯示「typeParameter」建議。\",\"啟用時，IntelliSense 顯示「程式碼片段」建議。\",\"啟用之後，IntelliSense 會顯示 `user`-suggestions。\",\"啟用時，IntelliSense 會顯示 `issues`-suggestions。\",\"是否應一律選取前置和後置的空白字元。\",\"是否應該選取子詞 (例如 'fooBar' 或 'foo_bar' 中的 'foo')。\",\"執行與文字相關的瀏覽或作業時，用於文字分割的地區設定。指定您要辨識的文字的 BCP 47 語言標記 (例如 ja、zh-CN、zh-Hant-TW 等)。\",\"執行與文字相關的瀏覽或作業時，用於文字分割的地區設定。指定您要辨識的文字的 BCP 47 語言標記 (例如 ja、zh-CN、zh-Hant-TW 等)。\",\"無縮排。換行從第 1 列開始。\",\"換行的縮排會與父行相同。\",\"換行的縮排為父行 +1。\",\"換行縮排為父行 +2。\",\"控制換行的縮排。\",\"控制您是否可以按住 `Shift` 鍵，將檔案拖放到文字編輯器中 (而非在編輯器中開啟檔案)。\",\"控制將檔案放入編輯器時是否顯示小工具。此小工具可讓您控制檔案的置放方式。\",\"將檔案放入編輯器後顯示置放選取器小工具。\",\"永不顯示置放選取器小工具。改為一律使用預設置放提供者。\",\"控制是否可以以不同方式貼上內容。\",\"控制將內容貼上至編輯器時是否顯示小工具。此小工具可讓您控制檔案的貼上方式。\",\"將內容貼上編輯器後顯示貼上選取器小工具。\",\"永不顯示貼上選取器小工具。而是一律使用預設的貼上行為。\",\"控制是否透過提交字元接受建議。例如在 JavaScript 中，分號 (';') 可以是接受建議並鍵入該字元的提交字元。\",\"在建議進行文字變更時，僅透過 `Enter` 接受建議。\",\"控制除了 'Tab' 外，是否也透過 'Enter' 接受建議。這有助於避免混淆要插入新行或接受建議。\",\"控制編輯器中可一次由螢幕助讀程式讀出的行數。偵測到螢幕助讀程式時會自動預設為 500。警告: 若數字超過預設，可能會對效能有所影響。\",\"編輯器內容\",\"控制螢幕助讀程式是否宣告內嵌建議。\",\"使用語言配置確定何時自動關閉括號。\",\"僅當游標位於空白的左側時自動關閉括號。\",\"控制編輯器是否應在使用者新增左括弧後，自動加上右括弧。\",\"使用語言配置確定何時自動關閉註解。\",\"僅當游標位於空白的左側時自動關閉註解。\",\"控制使用者新增開啟的註解之後，編輯器是否應該自動關閉註解。\",\"僅在自動插入相鄰的右引號或括弧時，才將其移除。\",\"控制編輯器是否應在刪除時移除相鄰的右引號或括弧。\",\"僅在自動插入右引號或括號時，才在其上方鍵入。\",\"控制編輯器是否應在右引號或括號上鍵入。\",\"使用語言配置確定何時自動關閉引號。\",\"僅當游標位於空白的左側時自動關閉引號。\",\"控制編輯器是否應在使用者新增開始引號後，自動加上關閉引號。\",\"編輯器不會自動插入縮排。\",\"編輯器會保留目前行的縮排。\",\"編輯器會保留目前行的縮排並接受語言定義的括號。\",\"編輯器會目前行的縮排、接受語言定義的括號並叫用語言定義的特殊 onEnterRules。\",\"編輯器會保留目前行的縮排、接受語言定義的括號並叫用語言定義的特殊 onEnterRules 並接受語言定義的 indentationRules。\",\"控制編輯器是否應在使用者鍵入、貼上、移動或縮排行時自動調整縮排。\",\"使用語言組態來決定何時自動環繞選取項目。\",\"用引號括住，而非使用括弧。\",\"用括弧括住，而非使用引號。 \",\"控制編輯器是否應在鍵入引號或括弧時自動包圍選取範圍。\",\"當使用空格進行縮排時，會模擬定位字元的選取表現方式。選取範圍會依循定位停駐點。\",\"控制編輯器是否顯示 codelens。\",\"控制 CodeLens 的字型家族。\",\"控制 CodeLens 的字型大小 (像素)。設定為 0 時，會使用 90% 的 `#editor.fontSize#`。\",\"控制編輯器是否應轉譯內嵌色彩裝飾項目與色彩選擇器。\",\"讓色彩選擇器在按一下和停駐色彩在裝飾項目上時出現\",\"讓色彩選擇器在停駐色彩裝飾項目時出現\",\"讓色彩選擇器在按一下色彩裝飾項目時出現\",\"控制條件，讓色彩選擇器從色彩裝飾項目出現\",\"控制一次可在編輯器中呈現的色彩裝飾項目最大數目。\",\"啟用即可以滑鼠與按鍵選取進行資料行選取。\",\"控制語法醒目提示是否應複製到剪貼簿。\",\"控制資料指標動畫樣式。\",\"平滑插入號動畫已停用。\",\"只有當使用者使用明確手勢移動游標時，才會啟用平滑插入號動畫。\",\"永遠啟用平滑插入號動畫。\",\"控制是否應啟用平滑插入點動畫。 \",\"控制插入輸入模式中的游標樣式。\",\"控制游標上下周圍可顯示的前置線 (最小為 0) 和後置線 (最小為 1) 的最小數目。在某些編輯器中稱為 'scrollOff' 或 'scrollOffset'。\",\"只有通過鍵盤或 API 觸發時，才會施行 `cursorSurroundingLines`。\",\"一律強制執行 `cursorSurroundingLines`\",\"控制應強制執行 `#editor.cursorSurroundingLines#` 的時間。\",\"控制游標寬度，當 `#editor.cursorStyle#` 設定為 `line` 時。\",\"控制編輯器是否允許透過拖放來移動選取項目。\",\"使用新的 svg 轉譯方法。\",\"使用具有字型字元的新轉譯方法。\",\"使用穩定轉譯方法。\",\"控制是否使用新的實驗性方法來呈現空白字元。\",\"按下 `Alt` 時的捲動速度乘數。\",\"控制編輯器是否啟用程式碼摺疊功能。\",\"使用語言特定摺疊策略 (如果可用)，否則使用縮排式策略。\",\"使用縮排式摺疊策略。\",\"控制計算資料夾範圍的策略。\",\"控制編輯器是否應將折疊的範圍醒目提示。\",\"控制編輯器是否會自動摺疊匯入範圍。\",\"可摺疊區域的數目上限。增加此值可能會造成當目前的來源有大量可摺疊區域時，編輯器的回應速度變慢。\",\"控制按一下已折疊行後方的空白內容是否會展開行。\",\"控制字型家族。\",\"控制編輯器是否應自動為貼上的內容設定格式。必須有可用的格式器，而且格式器應能夠為文件中的一個範圍設定格式。\",\"控制編輯器是否應自動在鍵入後設定行的格式。\",\"控制編輯器是否應轉譯垂直字符邊界。字符邊界最常用來進行偵錯。\",\"控制游標是否應隱藏在概觀尺規中。\",\"控制字母間距 (像素)。\",\"控制編輯器是否已啟用連結編輯。相關符號 (例如 HTML 標籤) 會根據語言在編輯時更新。\",\"控制編輯器是否應偵測連結並使其可供點選。\",\"將符合的括號醒目提示。\",\"要用於滑鼠滾輪捲動事件 `deltaX` 和 `deltaY` 的乘數。\",\"使用滑鼠滾輪並按住 `Cmd` 時，縮放編輯器的字型\",\"使用滑鼠滾輪並按住 `Ctrl` 時，縮放編輯器的字型\",\"在多個游標重疊時將其合併。\",\"對應Windows和Linux的'Control'與對應 macOS 的'Command'。\",\"對應Windows和Linux的'Alt'與對應macOS的'Option'。\",\"用於在滑鼠新增多個游標的修飾元。[移至定義] 和 [開啟連結] 滑鼠手勢會加以適應，以避免與 [多個游標的修飾元](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier) 相衝突。\",\"每個游標都會貼上一行文字。\",\"每個游標都會貼上全文。\",\"當已貼上文字的行數與游標數相符時控制貼上功能。\",\"控制一次可在作用中編輯器中的游標數目上限。\",\"不強調顯示出現項目。\",\"僅強調顯示目前檔案中的出現項目。\",\"實驗: 跨所有有效的開啟檔案強調顯示出現項目。\",\"控制是否應跨開啟的檔案強調顯示出現項目。\",\"控制是否應在概觀尺規周圍繪製框線。\",\"開啟預覽時焦點樹狀\",\"開啟時聚焦編輯器\",\"控制要聚焦內嵌編輯器或預覽小工具中的樹系。\",\"控制「前往定義」滑鼠手勢，是否一律開啟瞄核小工具。\",\"控制在快速建議顯示後的延遲 (以毫秒為單位)。\",\"控制編輯器是否會自動依類型重新命名。\",\"已淘汰，請改用 `editor.linkedEditing`。\",\"控制編輯器是否應顯示控制字元。\",\"在檔案結尾為新行時，呈現最後一行的號碼。\",\"醒目提示裝訂邊和目前的行。\",\"控制編輯器如何顯示目前行的醒目提示。\",\"控制當聚焦於編輯器時，編輯器是否應僅轉譯目前行的醒目提示。\",\"轉譯空白字元，但文字之間的單一空格除外。\",\"只轉譯所選文字的空白字元。\",\"只轉譯結尾空白字元。\",\"控制編輯器應如何轉譯空白字元。\",\"控制選取範圍是否有圓角\",\"控制編輯器水平捲動的額外字元數。\",\"控制編輯器是否捲動到最後一行之外。\",\"同時進行垂直與水平捲動時，僅沿主軸捲動。避免在軌跡板上進行垂直捲動時發生水平漂移。\",\"控制是否支援 Linux 主要剪貼簿。\",\"控制編輯器是否應醒目提示與選取項目類似的相符項目。\",\"一律顯示摺疊控制項。\",\"永不顯示摺疊控制項與減少裝訂邊大小。\",\"僅當滑鼠懸停在活動列上時，才顯示折疊功能。\",\"控制摺疊控制項在裝訂邊上的顯示時機。\",\"控制未使用程式碼的淡出。\",\"控制已刪除的淘汰變數。\",\"將程式碼片段建議顯示於其他建議的頂端。\",\"將程式碼片段建議顯示於其他建議的下方。\",\"將程式碼片段建議與其他建議一同顯示。\",\"不顯示程式碼片段建議。\",\"控制程式碼片段是否隨其他建議顯示，以及其排序方式。\",\"控制編輯器是否會使用動畫捲動\",\"控制當顯示內嵌完成時，是否應提供協助工具提示給螢幕助讀程式使用者。\",\"建議小工具的字型大小。當設定為 {0} 時，則會使用 {1} 的值。\",\"建議小工具的行高。當設定為 {0} 時，則會使用 {1} 的值。最小值為 8。\",\"控制建議是否應在鍵入觸發字元時自動顯示。\",\"一律選取第一個建議。\",\"除非進一步鍵入選取了建議，否則選取最近的建議，例如 `console.| -> console.log`，原因是最近完成了 `log`。\",\"根據先前已完成該建議的前置詞選取建議，例如 `co -> console` 和 `con -> const`。\",\"控制在顯示建議清單時如何預先選取建議。\",\"按 Tab 時，Tab 完成會插入最符合的建議。\",\"停用 tab 鍵自動完成。\",\"在程式碼片段的首碼相符時使用 Tab 完成。未啟用 'quickSuggestions' 時效果最佳。\",\"啟用 tab 鍵自動完成。\",\"自動移除異常的行結束字元。\",\"忽略異常的行結束字元。\",\"要移除之異常的行結束字元提示。\",\"移除可能導致問題的異常行結束字元。\",\"空格和索引標籤的插入和刪除會與索引標籤的停頓對齊。\",\"使用預設的分行符號規則。\",\"中文/日文/韓文 (CJK) 文字不應該使用斷字。非中日韓的文字行為與一般文字相同。\",\"控制用於中文/日文/韓文 (CJK) 文字的斷字規則。\",\"在執行文字相關導覽或作業時要用作文字分隔符號的字元\",\"一律不換行。\",\"依檢視區寬度換行。\",\"於 '#editor.wordWrapColumn#' 換行。\",\"當檢視區縮至最小並設定 '#editor.wordWrapColumn#' 時換行。\",\"控制如何換行。\",\"當 `#editor.wordWrap#` 為 `wordWrapColumn` 或 `bounded` 時，控制編輯器中的資料行換行。\",\"控制是否應使用預設的文件色彩提供者顯示內嵌色彩裝飾\",\"控制編輯器是否接收索引標籤，或將其延遲至工作台進行流覽。\",\"目前游標位置行的反白顯示背景色彩。\",\"目前游標位置行之周圍框線的背景色彩。\",\"醒目提示範圍的背景色彩，例如快速開啟並尋找功能。其不得為不透明色彩，以免隱藏底層裝飾。\",\"反白顯示範圍周圍邊框的背景顏色。\",\"醒目提示符號的背景色彩，相似於前往下一個定義或前往下一個/上一個符號。色彩必須透明，以免隱藏底層裝飾。\",\"醒目提示周圍的邊界背景色彩。\",\"編輯器游標的色彩。\",\"編輯器游標的背景色彩。允許自訂區塊游標重疊的字元色彩。\",\"有多個游標存在時，主要編輯器游標的色彩。\",\"有多個游標存在時，主要編輯器游標的背景色彩。允許自訂區塊游標重疊的字元色彩。\",\"有多個游標存在時，次要編輯器游標的色彩。\",\"有多個游標存在時，次要編輯器游標的背景色彩。允許自訂區塊游標重疊的字元色彩。\",\"編輯器中空白字元的色彩。\",\"編輯器行號的色彩。\",\"編輯器縮排輔助線的色彩。\",\"'editorIndentGuide.background' 已被取代。請改用 'editorIndentGuide.background1'。\",\"使用中編輯器縮排輔助線的色彩。\",\"'editorIndentGuide.activeBackground' 已被取代。請改用 'editorIndentGuide.activeBackground1'。\",\"編輯器縮排輔助線的色彩 (1)。\",\"編輯器縮排輔助線的色彩 (2)。\",\"編輯器縮排輔助線的色彩 (3)。\",\"編輯器縮排輔助線的色彩 (4)。\",\"編輯器縮排輔助線的色彩 (5)。\",\"編輯器縮排輔助線的色彩 (6)。\",\"使用中編輯器縮排輔助線的色彩 (1)。\",\"使用中編輯器縮排輔助線的色彩 (2)。\",\"使用中編輯器縮排輔助線的色彩 (3)。\",\"使用中編輯器縮排輔助線的色彩 (4)。\",\"使用中編輯器縮排輔助線的色彩 (5)。\",\"使用中編輯器縮排輔助線的色彩 (6)。\",\"編輯器使用中行號的色彩\",\"Id 已取代。請改用 'editorLineNumber.activeForeground' 。\",\"編輯器使用中行號的色彩\",\"editor.renderFinalNewline 設定為暗灰色時，最終編輯器線條的色彩。\",\"編輯器尺規的色彩\",\"編輯器程式碼濾鏡的前景色彩\",\"成對括號背景色彩\",\"成對括號邊框色彩\",\"預覽檢視編輯器尺規的邊框色彩.\",\"編輯器概觀尺規的背景色彩。\",\"編輯器邊框的背景顏色,包含行號與字形圖示的邊框.\",\"編輯器中不必要 (未使用) 原始程式碼的框線色彩。\",\"編輯器中不必要 (未使用) 原始程式碼的不透明度。例如 \\\"#000000c0” 會以 75% 的不透明度轉譯程式碼。針對高對比主題，使用 'editorUnnecessaryCode.border' 主題色彩可為不必要的程式碼加上底線，而不是將其變淡。\",\"編輯器中浮水印文字的邊框色彩。\",\"編輯器中浮水印文字的前景色彩。\",\"編輯器中浮水印文字的背景色彩。\",\"範圍醒目提示的概觀尺規標記色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\"錯誤的概觀尺規標記色彩。\",\"警示的概觀尺規標記色彩。\",\"資訊的概觀尺規標記色彩。\",\"括弧 (1) 的前景色彩。需要啟用成對方括弧著色。\",\"括弧 (2) 的前景色彩。需要啟用成對方括弧著色。\",\"括弧 (3) 的前景色彩。需要啟用成對方括弧著色。\",\"括弧 (4) 的前景色彩。需要啟用成對方括弧著色。\",\"括弧 (5) 的前景色彩。需要啟用成對方括弧著色。\",\"括弧 (6) 的前景色彩。需要啟用成對方括弧著色。\",\"未預期括弧的前景色彩。\",\"非使用中括弧配對輔助線 (1) 的背景色彩。需要啟用括弧配對輔助線。\",\"非使用中括弧配對輔助線 (2) 的背景色彩。需要啟用括弧配對輔助線。\",\"非使用中括弧配對輔助線 (3) 的背景色彩。需要啟用括弧配對輔助線。\",\"非使用中括弧配對輔助線 (4) 的背景色彩。需要啟用括弧配對輔助線。\",\"非使用中括弧配對輔助線 (5) 的背景色彩。需要啟用括弧配對輔助線。\",\"非使用中括弧配對輔助線 (6) 的背景色彩。需要啟用括弧配對輔助線。\",\"使用中括弧配對輔助線 (1) 的背景色彩。需要啟用括弧配對輔助線。\",\"使用中括弧配對輔助線 (2) 的背景色彩。需要啟用括弧配對輔助線。\",\"使用中括弧配對輔助線 (3) 的背景色彩。需要啟用括弧配對輔助線。\",\"使用中括弧配對輔助線 (4) 的背景色彩。需要啟用括弧配對輔助線。\",\"使用中括弧配對輔助線 (5) 的背景色彩。需要啟用括弧配對輔助線。\",\"使用中括弧配對輔助線 (6) 的背景色彩。需要啟用括弧配對輔助線。\",\"用來醒目提示 Unicode 字元的框線色彩。\",\"用來醒目提示 Unicode 字元的背景色彩。\",\"編輯器文字是否有焦點 (游標閃爍)\",\"編輯器或編輯器小工具是否有焦點 (例如焦點位於 [尋找] 小工具中)\",\"編輯器或 RTF 輸入是否有焦點 (游標閃爍)\",\"編輯器是否為唯讀\",\"內容是否為 Diff 編輯器\",\"內容是否為內嵌 Diff 編輯器\",null,\"是否摺疊多重 Diff 編輯器中的所有檔案\",\"Diff 編輯器是否有變更\",\"是否選取移動的程式碼區塊進行比較\",\"是否顯示易存取差異檢視器\",\"是否已達到差異編輯器並排呈現內嵌中斷點\",\"內嵌模式是否作用中\",\"修改是否可以在 Diff 編輯器中寫入\",\"修改是否可以在 Diff 編輯器中寫入\",\"原始文件的 URI\",\"已修改文件的 URI\",\"'editor.columnSelection' 是否已啟用\",\"編輯器是否有選取文字\",\"編輯器是否有多個選取項目\",\"'Tab' 是否會將焦點移出編輯器\",\"編輯器暫留是否顯示\",\"編輯器暫留是否聚焦\",\"自黏捲動是否聚焦\",\"自黏捲動是否顯示\",\"是否顯示獨立的顏色選擇器\",\"獨立的顏色選擇器是否聚焦\",\"編輯器是否為較大編輯器的一部分 (例如筆記本)\",\"編輯器的語言識別碼\",\"編輯器是否有完成項目提供者\",\"編輯器是否有程式碼動作提供者\",\"編輯器是否有 CodeLens 提供者\",\"編輯器是否有定義提供者\",\"編輯器是否有宣告提供者\",\"編輯器是否有實作提供者\",\"編輯器是否有型別定義提供者\",\"編輯器是否有暫留提供者\",\"編輯器是否有文件醒目提示提供者\",\"編輯器是否有文件符號提供者\",\"編輯器是否有參考提供者\",\"編輯器是否有重新命名提供者\",\"編輯器是否有簽章說明提供者\",\"編輯器是否有內嵌提示提供者\",\"編輯器是否有文件格式化提供者\",\"編輯器是否有文件選取項目格式化提供者\",\"編輯器是否有多個文件格式化提供者\",\"編輯器是否有多個文件選取項目格式化提供者\",\"陣列\",\"布林值\",\"類別\",\"常數\",\"建構函式\",\"列舉\",\"列舉成員\",\"事件\",\"欄位\",\"檔案\",\"函式\",\"介面\",\"索引鍵\",\"方法\",\"模組\",\"命名空間\",\"null\",\"數字\",\"物件\",\"運算子\",\"套件\",\"屬性\",\"字串\",\"結構\",\"型別參數\",\"變數\",\"{0} ({1})\",\"純文字\",\"正在鍵入\",\"開發人員: 檢查權杖\",\"前往行/欄...\",\"顯示所有快速存取提供者\",\"命令選擇區\",\"顯示並執行命令\",\"移至符號...\",\"前往符號 (依類別)...\",\"編輯器內容\",\"切換高對比佈景主題\",\"已在 {1} 檔案中進行 {0} 項編輯\",\"顯示更多 ({0})\",\"{0} chars\",\"選取範圍錨點\",\"設定錨點為 {0}:{1}\",\"設定選取範圍錨點\",\"前往選取範圍錨點\",\"選取從錨點到游標之間的範圍\",\"取消選取範圍錨點\",\"成對括弧的概觀尺規標記色彩。\",\"移至方括弧\",\"選取至括弧\",\"移除括弧\",\"前往括弧(&&B)\",\"選取內部文字，並包含括弧或大括弧\",\"將所選文字向左移動\",\"將所選文字向右移動\",\"調換字母\",\"剪下(&&T)\",\"剪下\",\"剪下\",\"剪下\",\"複製(&&C)\",\"複製\",\"複製\",\"複製\",\"貼上(&&P)\",\"貼上\",\"貼上\",\"貼上\",\"隨語法醒目提示複製\",\"複製為\",\"複製為\",\"共用\",\"共用\",\"套用程式碼動作時發生未知的錯誤\",\"要執行程式碼動作的種類。\",\"控制要套用傳回動作的時機。\",\"一律套用第一個傳回的程式碼動作。\",\"如果傳回的程式碼動作是唯一動作，則加以套用。\",\"不要套用傳回的程式碼動作。\",\"控制是否僅應傳回偏好的程式碼動作。\",\"快速修復...\",\"沒有可用的程式碼操作\",\"沒有 \\\"{0}\\\" 的偏好程式碼動作\",\"沒有 \\\"{0}\\\" 可用的程式碼動作\",\"沒有可用的偏好程式碼動作\",\"沒有可用的程式碼操作\",\"重構...\",\"沒有適用於 '{0}' 的偏好重構。\",\"沒有可用的 \\\"{0}\\\" 重構\",\"沒有可用的偏好重構\",\"沒有可用的重構\",\"來源動作...\",\"沒有適用於 '{0}' 的偏好來源動作\",\"沒有 \\\"{0}\\\" 可用的來源動作\",\"沒有可用的偏好來源動作\",\"沒有可用的來源動作\",\"組織匯入\",\"沒有任何可用的組織匯入動作\",\"全部修正\",\"沒有全部修正動作可用\",\"自動修正...\",\"沒有可用的自動修正\",\"啟用/停用在 [程式碼動作] 功能表中顯示群組標頭。\",\"目前不在診斷時，啟用/停用顯示行內最近的 [快速修正]。\",\"當 {1} 設為 {2} 時，啟用觸發 {0}。程式碼動作必須設定為要針對視窗和焦點變更觸發 {3}。\",\"內容: {0} 在行 {1} 和欄 {2}。\",\"隱藏已停用項目\",\"顯示已停用項目\",\"更多動作...\",\"快速修正\",\"擷取\",\"內嵌\",\"重寫\",\"移動\",\"範圍陳述式\",\"來源動作\",\"當編輯器中沒有空間時，從裝訂邊衍生程式碼動作功能表的圖示。\",\"當編輯器中沒有空間，且有快速修正可用時，從裝訂邊衍生程式碼動作功能表的圖示。\",\"當編輯器中沒有空間，且有 AI 修正程式可用時，從裝訂邊衍生程式碼動作功能表的圖示。\",\"當編輯器中沒有空間，且有 AI 修正程式及快速修正可用時，從裝訂邊衍生程式碼動作功能表的圖示。\",\"當編輯器中沒有空間，且有 AI 修正程式及快速修正可用時，從裝訂邊衍生程式碼動作功能表的圖示。\",\"執行: {0}\",\"顯示程式碼動作。偏好的快速修正可用 ({0})\",\"顯示程式碼動作 ({0})\",\"顯示程式碼動作\",\"顯示目前行的 Code Lens 命令\",\"選取命令\",null,null,null,null,null,null,null,null,null,\"切換行註解\",\"切換行註解(&&T)\",\"加入行註解\",\"移除行註解\",\"切換區塊註解\",\"切換區塊註解(&&B)\",\"縮圖\",\"轉譯字元\",\"垂直大小\",\"按比例\",\"填滿\",\"最適大小\",\"滑桿\",\"滑鼠移至上方\",\"一律\",\"顯示編輯器內容功能表\",\"游標復原\",\"游標重做\",\"要嘗試貼上的貼上編輯的種類。\\r\\n如果有此種類的多個編輯，編輯器會顯示選擇器。如果沒有此種類的編輯，編輯器會顯示錯誤訊息。\",\"貼上為...\",\"貼上為文字\",\"是否顯示貼上小工具\",\"顯示貼上選項...\",\"找不到 '{0}' 的貼上編輯\",\"正在解析貼上編輯。按一下以取消\",\"執行貼上處理常式。按一下以取消並執行基本貼上\",\"選取貼上動作\",\"執行貼上處理常式\",\"插入純文字\",\"插入 URI\",\"插入 URI\",\"插入路徑\",\"插入路徑\",\"插入相對路徑\",\"插入相對路徑\",\"插入 HTML\",null,\"是否顯示卸除小工具\",\"顯示卸除選項...\",\"正在執行置放處理常式。按一下以取消\",\"解析編輯 '{0}' 時發生錯誤:\\r\\n{1}\",\"套用編輯 '{0}' 時發生錯誤:\\r\\n{1}\",\"編輯器是否執行可取消的作業，例如「預覽參考」\",\"檔案太大，無法執行取代所有作業。\",\"尋找\",\"尋找(&&F)\",\"使用引數尋找\",\"尋找選取項目\",\"尋找下一個\",\"尋找上一個\",\"移至相符項目...\",\"沒有相符項目。嘗試搜尋其他項目。\",\"輸入數字以前往特定相符項目 (介於 1 到 {0})\",\"請輸入介於 1 和 {0} 之間的數字。\",\"請輸入介於 1 和 {0} 之間的數字。\",\"尋找下一個選取項目\",\"尋找上一個選取項目\",\"取代\",\"取代(&&R)\",\"表示編輯器尋找小工具已摺疊的圖示。\",\"表示編輯器尋找小工具已展開的圖示。\",\"編輯器尋找小工具中 [在選取範圍中尋找] 的圖示。\",\"編輯器尋找小工具中 [取代] 的圖示。\",\"編輯器尋找小工具中 [全部取代] 的圖示。\",\"編輯器尋找小工具中 [尋找上一個] 的圖示。\",\"編輯器尋找小工具中 [尋找下一個] 的圖示。\",\"尋找/取代\",\"尋找\",\"尋找\",\"上一個相符項目\",\"下一個相符項目\",\"在選取範圍中尋找\",\"關閉\",\"取代\",\"取代\",\"取代\",\"全部取代\",\"切換取代\",\"僅反白顯示前 {0} 筆結果，但所有尋找作業會在完整文字上執行。\",\"{1} 的 {0}\",\"查無結果\",\"找到 {0}\",\"以 '{1}' 找到 {0}\",\"以 '{1}' 找到 {0}，位於 {2}\",\"已以 '{1}' 找到 {0}\",\"Ctrl+Enter 現在會插入分行符號，而不會全部取代。您可以修改 editor.action.replaceAll 的按鍵繫結關係，以覆寫此行為。\",\"展開\",\"以遞迴方式展開\",\"摺疊\",\"切換摺疊\",\"以遞迴方式摺疊\",\"切換以遞迴方式摺疊\",\"摺疊全部區塊註解\",\"摺疊所有區域\",\"展開所有區域\",\"摺疊所選區域以外的所有區域\",\"展開所選區域以外的所有區域\",\"全部摺疊\",\"全部展開\",\"移至父代摺疊\",\"移至上一個摺疊範圍\",\"移至下一個摺疊範圍\",\"從選取範圍建立摺疊範圍\",\"移除手動折疊範圍\",\"摺疊層級 {0}\",\"已摺疊範圍後的背景色彩。色彩不得處於不透明狀態，以免隱藏底層裝飾。\",\"摺疊範圍第一行之後的摺疊文字色彩。\",\"編輯器裝訂邊的摺疊控制項色彩。\",\"編輯器字符邊界中 [展開的範圍] 的圖示。\",\"編輯器字符邊界中 [摺疊的範圍] 的圖示。\",\"編輯器字符邊界中手動摺疊範圍的圖示。\",\"編輯器字符邊界中手動展開範圍的圖示。\",\"按一下以展開範圍。\",\"按一下以摺疊範圍。\",\"增加編輯器字型大小\",\"減少編輯器字型大小\",\"重設編輯器字型大小\",\"格式化文件\",\"格式化選取範圍\",\"移至下一個問題 (錯誤, 警告, 資訊)\",\"[前往下一個標記] 的圖示。\",\"移至上一個問題 (錯誤, 警告, 資訊)\",\"[前往上一個標記] 的圖示。\",\"移至檔案裡面的下一個問題 (錯誤, 警告, 資訊)\",\"下一個問題(&&P)\",\"移至檔案裡面的上一個問題 (錯誤, 警告, 資訊)\",\"前一個問題(&&P)\",\"錯誤\",\"警告\",\"資訊\",\"提示\",\"{0} 於 {1}。\",\"{0} 個問題 (共 {1} 個)\",\"{0} 個問題 (共 {1} 個)\",\"編輯器標記導覽小工具錯誤的色彩。\",\"編輯器標記導覽小工具錯誤標題背景。\",\"編輯器標記導覽小工具警告的色彩。\",\"編輯器標記導覽小工具警告標題背景。\",\"編輯器標記導覽小工具資訊的色彩\",\"編輯器標記導覽小工具資訊標題背景。\",\"編輯器標記導覽小工具的背景。\",\"查看\",\"定義\",\"找不到 '{0}' 的定義\",\"找不到任何定義\",\"移至定義(&&D)\",\"宣告\",\"找不到 '{0}' 的宣告 \",\"找不到任何宣告\",\"前往宣告(&&D)\",\"找不到 '{0}' 的宣告 \",\"找不到任何宣告\",\"類型定義\",\"找不到 '{0}' 的任何類型定義\",\"找不到任何類型定義\",\"前往類型定義(&&T)\",\"實作\",\"找不到 '{0}' 的任何實作\",\"找不到任何實作\",\"前往實作(&&I)\",\"未找到 \\\"{0}\\\" 的參考\",\"未找到參考\",\"前往參考(&&R)\",\"參考\",\"參考\",\"位置\",\"'{0}' 沒有結果\",\"參考\",\"移至定義\",\"在一側開啟定義\",\"瞄核定義\",\"移至宣告\",\"預覽宣告\",\"移至類型定義\",\"預覽類型定義\",\"前往實作\",\"查看實作\",\"前往參考\",\"預覽參考\",\"前往任何符號\",\"按一下以顯示 {0} 項定義。\",\"是否顯示參考瞄核，例如「瞄核參考」或「瞄核定義」\",\"正在載入...\",\"{0} ({1})\",\"{0} 個參考\",\"{0} 個參考\",\"參考\",\"無法預覽\",\"查無結果\",\"參考\",\"在資料行 {2} 行 {1} 的 {0} 中\",\"在資料行 {3} 行 {2} 的 {1} 的 {0} 中\",\"1 個符號位於 {0}, 完整路徑 {1}\",\"{0} 個符號位於 {1}, 完整路徑 {2}\",\"找不到結果\",\"在 {0} 中找到 1 個符號\",\"在 {1} 中找到 {0} 個符號\",\"在 {1} 個檔案中找到 {0} 個符號\",\"是否有只能透過鍵盤瀏覽的符號位置。\",\"{1} 的符號 {0}，{2} 為下一個\",\"{1} 的符號 {0}\",\"提高暫留詳細程度層級\",\"降低暫留詳細程度層級\",\"顯示或聚焦暫留\",\"游標暫留將不會自動聚焦。\",\"只有在游標暫留顯示時才會聚焦。\",\"游標暫留出現時將自動聚焦。\",\"顯示定義預覽懸停\",\"向上捲動暫留\",\"向下捲動暫留\",\"向左捲動暫留\",\"向右捲動暫留\",\"上一頁暫留\",\"下一頁暫留\",\"移至上方暫留\",\"移至下方暫留\",\"顯示或聚焦編輯器暫留，這會顯示目前游標位置符號的文件、參考及其他內容。\",\"在編輯器中顯示定義預覽暫留。\",\"向上捲動編輯器暫留。\",\"向下捲動編輯器暫留。\",\"向左捲動編輯器暫留。\",\"向右捲動編輯器暫留。\",\"將編輯器暫留向上一頁。\",\"將編輯器暫留向下一頁。\",\"移至編輯器暫留的頂端。\",\"移至編輯器暫留的底部。\",\"提高暫留詳細程度的圖示。\",\"降低暫留詳細程度的圖示。\",\"正在載入...\",\"由於效能原因，已暫停轉譯。這可透過 `editor.stopRenderingLineAfter` 進行設定。\",\"因效能的緣故，已跳過將長的行 Token 化。您可透過 `editor.maxTokenizationLineLength` 設定。\",\"提高暫留詳細程度 ({0})\",\"提高暫留詳細程度\",\"降低暫留詳細程度 ({0})\",\"降低暫留詳細程度\",\"檢視問題\",\"沒有可用的快速修正\",\"正在檢查快速修正...\",\"沒有可用的快速修正\",\"快速修復...\",\"將縮排轉換成空格\",\"將縮排轉換成定位點\",\"已設定的定位點大小\",\"預設索引標籤大小\",\"目前的索引標籤大小\",\"選取目前檔案的定位點大小\",\"使用 Tab 進行縮排\",\"使用空格鍵進行縮排\",\"變更索引標籤顯示大小\",\"偵測內容中的縮排\",\"重新將行縮排\",\"重新將選取的行縮排\",\"將定位字元縮排轉換為空格。\",\"將空格縮排轉換為定位字元。\",\"使用定位字元縮排。\",\"使用空格縮排。\",\"變更與定位字元相等的空格大小。\",\"偵測內容中的縮排。\",\"重新縮排編輯器的行。\",\"重新縮排編輯器的選取行。\",\"按兩下以插入\",\"cmd + 按一下\",\"ctrl + 按一下\",\"選項 + 按一下\",\"alt + 按一下\",\"前往 [定義] ({0})，按一下滑鼠右鍵以了解更多\",\"移至定義 ({0})\",\"執行命令\",\"顯示下一個內嵌建議\",\"顯示上一個內嵌建議\",\"觸發內嵌建議\",\"接受下一個內嵌建議字組\",\"接受字組\",\"接受下一個內嵌建議行\",\"接受行\",\"接受內嵌建議\",\"接受\",\"隱藏內嵌建議\",\"永遠顯示工具列\",\"是否顯示內嵌建議\",\"內嵌建議是否以空白字元開頭\",\"內嵌建議的開頭是否為空白，且比 Tab 能插入的字元要小\",\"是否應隱藏目前建議的其他建議\",\"在可存取檢視中檢查此項目 ({0})\",\"建議:\",\"[顯示下一個參數提示] 的圖示。\",\"[顯示上一個參數提示] 的圖示。\",\"{0} ({1})\",\"上一個\",\"下一頁\",null,null,null,null,null,null,null,null,\"以上一個值取代\",\"以下一個值取代\",\"展開線條選取範圍\",\"將行向上複製\",\"將行向上複製(&&C)\",\"將行向下複製\",\"將行向下複製(&&P)\",\"重複選取項目\",\"重複選取項目(&&D)\",\"上移一行\",\"上移一行(&&V)\",\"下移一行\",\"下移一行(&&L)\",\"遞增排序行\",\"遞減排序行\",\"刪除重複的行\",\"修剪尾端空白\",\"刪除行\",\"縮排行\",\"凸排行\",\"在上方插入行\",\"在下方插入行\",\"左邊全部刪除\",\"刪除所有右方項目\",\"連接線\",\"轉置游標周圍的字元數\",\"轉換到大寫\",\"轉換到小寫\",\"轉換為字首大寫\",\"轉換為底線連接字\",\"轉換為 Camel 案例\",\"轉換為 Pascal 命名法的大小寫\",\"轉換成 Kebab Case\",\"開始連結的編輯\",\"當編輯器自動重新命名類型時的背景色彩。\",\"因為此連結的格式不正確，所以無法開啟: {0}\",\"因為此連結目標遺失，所以無法開啟。\",\"執行命令\",\"追蹤連結\",\"cmd + 按一下\",\"ctrl + 按一下\",\"選項 + 按一下\",\"alt + 按一下\",\"執行命令 {0}\",\"開啟連結\",\"編輯器目前是否正在顯示內嵌訊息\",\"新增的資料指標: {0}\",\"新增的資料指標: {0}\",\"在上方加入游標\",\"在上方新增游標(&&A)\",\"在下方加入游標\",\"在下方新增游標(&&D)\",\"在行尾新增游標\",\"在行尾新增游標(&&U)\",\"將游標新增到底部 \",\"將游標新增到頂部\",\"將選取項目加入下一個找到的相符項\",\"新增下一個項目(&&N)\",\"將選取項目加入前一個找到的相符項中\",\"新增上一個項目(&&R)\",\"將最後一個選擇項目移至下一個找到的相符項\",\"將最後一個選擇項目移至前一個找到的相符項\",\"選取所有找到的相符項目\",\"選取所有項目(&&O)\",\"變更所有發生次數\",\"聚焦下一個游標\",\"聚焦下一個游標\",\"聚焦上一個游標\",\"聚焦前一個游標\",\"觸發參數提示\",\"[顯示下一個參數提示] 的圖示。\",\"[顯示上一個參數提示] 的圖示。\",\"{0}，提示\",\"參數提示中使用中項目的前景色彩。\",\"目前的程式碼編輯器是否內嵌於瞄核內\",\"關閉\",\"預覽檢視標題區域的背景色彩。\",\"預覽檢視標題的色彩。\",\"預覽檢視標題資訊的色彩。\",\"預覽檢視之框線與箭頭的色彩。\",\"預覽檢視中結果清單的背景色彩。\",\"預覽檢視結果列表中行節點的前景色彩\",\"預覽檢視結果列表中檔案節點的前景色彩\",\"在預覽檢視之結果清單中選取項目時的背景色彩。\",\"在預覽檢視之結果清單中選取項目時的前景色彩。\",\"預覽檢視編輯器的背景色彩。\",\"預覽檢視編輯器邊框(含行號或字形圖示)的背景色彩。\",\"預覽檢視編輯器中黏性滾動的背景色彩。\",\"在預覽檢視編輯器中比對時的反白顯示色彩。\",\"預覽檢視編輯器中比對時的反白顯示色彩。\",\"在預覽檢視編輯器中比對時的反白顯示邊界。\",\"編輯器中預留位置文字的前景色彩。\",\"先開啟文字編輯器，前往某一行。\",\"前往第 {0} 行的第 {1} 個字元。\",\"前往第 {0} 行。\",\"目前行: {0}，字元: {1}。請鍵入介於 1 到 {2} 之間行號，導覽至該行。\",\"目前行: {0}，字元: {1}。請鍵入要導覽至的行號。\",\"若要前往符號，請先開啟包含符號資訊的文字編輯器。\",\"使用中的文字編輯器不提供符號資訊。\",\"沒有相符的編輯器符號\",\"沒有編輯器符號\",\"開至側邊\",\"開啟到底部\",\"符號 ({0})\",\"屬性 ({0})\",\"方法 ({0})\",\"函式 ({0})\",\"建構函式 ({0})\",\"變數 ({0})\",\"類別 ({0})\",\"結構 ({0})\",\"事件 ({0})\",\"運算子 ({0})\",\"介面 ({0})\",\"命名空間 ({0})\",\"套件 ({0})\",\"型別參數 ({0})\",\"模組 ({0})\",\"屬性 ({0})\",\"列舉 ({0})\",\"列舉成員 ({0})\",\"字串 ({0})\",\"檔案 ({0})\",\"陣列 ({0})\",\"數字 ({0})\",\"布林值 ({0})\",\"物件 ({0})\",\"索引鍵 ({0})\",\"欄位 ({0})\",\"常數 ({0})\",\"無法在唯讀輸入中編輯\",\"無法在唯讀編輯器中編輯\",\"沒有結果。\",\"解析重新命名位置時發生未知的錯誤\",\"正在將 '{0}' 重新命名為 '{1}'\",\"正在將 {0} 重新命名為 {1}\",\"已成功將 '{0}' 重新命名為 '{1}'。摘要: {2}\",\"重命名無法套用編輯\",\"重新命名無法計算編輯\",\"重新命名符號\",\"啟用/停用重新命名前先預覽變更的功能\",\"聚焦下一個重新命名建議\",\"聚焦上一個重新命名建議\",\"是否顯示重新命名輸入小工具\",\"是否聚焦於重新命名輸入小工具\",\"按 {0} 進行重新命名，按 {1} 進行預覽\",\"已收到 {0} 重新命名建議\",\"為輸入重新命名。請鍵入新名稱，然後按 Enter 以提交。\",\"產生新名稱建議\",\"取消\",\"展開選取項目\",\"展開選取範圍(&&E)\",\"縮小選取項目\",\"壓縮選取範圍(&&S)\",\"編輯器目前是否在程式碼片段模式中\",\"在程式碼片段模式中是否有下一個定位停駐點\",\"在程式碼片段模式中是否有上一個定位停駐點\",\"移至下一個預留位置...\",\"星期天\",\"星期一\",\"星期二\",\"星期三\",\"星期四\",\"星期五\",\"星期六\",\"週日\",\"週一\",\"週二\",\"週三\",\"週四\",\"週五\",\"週六\",\"一月\",\"二月\",\"三月\",\"四月\",\"五月\",\"六月\",\"七月\",\"八月\",\"九月\",\"十月\",\"十一月\",\"十二月\",\"1月\",\"2月\",\"3 月\",\"4月\",\"五月\",\"6月\",\"7 月\",\"8 月\",\"9 月\",\"10 月\",\"11 月\",\"12 月\",\"切換編輯器自黏捲動(&&T)\",\"自黏捲動\",\"自黏捲動(&&S)\",\"焦點自黏捲動(&&F)\",\"切換編輯器自黏捲動\",\"切換/啟用顯示檢視區頂端巢狀範圍的編輯器自黏捲動\",\"專注於編輯器自黏捲動\",\"選取下一個編輯器自黏捲動行\",\"選取上一個自黏捲動行\",\"移至焦點自黏捲動行\",\"選取編輯器\",\"是否聚焦任何建議\",\"是否顯示建議詳細資料\",\"是否有多個建議可以挑選\",\"插入目前的建議會產生變更，或已鍵入所有項目\",\"是否在按下 Enter 時插入建議\",\"目前的建議是否有插入和取代行為\",\"預設行為是插入或取代\",\"目前的建議是否支援解決更多詳細資料\",\"接受 ‘{0}’ 進行了其他 {1} 項編輯\",\"觸發建議\",\"插入\",\"插入\",\"取代\",\"取代\",\"插入\",\"顯示較少\",\"顯示較多\",\"重設建議小工具大小\",\"建議小工具的背景色彩。\",\"建議小工具的邊界色彩。\",\"建議小工具的前景色彩。\",\"建議小工具中所選項目的前景色彩。\",\"建議小工具中所選項目的圖示前景色彩。\",\"建議小工具中所選項目的背景色彩。\",\"建議小工具中相符醒目提示的色彩。\",\"當項目成為焦點時，相符項目的色彩在建議小工具中會醒目顯示。\",\"建議小工具狀態的前景色彩。\",\"正在載入...\",\"無建議。\",\"建議\",\"{0} {1}，{2}\",\"{0} {1}\",\"{0}，{1}\",\"{0}，文件: {1}\",\"關閉\",\"正在載入...\",\"建議小工具中 [更多詳細資訊] 的圖示。\",\"閱讀更多\",\"陣列符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"布林值符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"類別符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"色彩符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"常數符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"建構函式符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"列舉值符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"列舉值成員符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"事件符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"欄位符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"檔案符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"資料夾符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"函式符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"介面符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"索引鍵符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"關鍵字符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"方法符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"模組符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"命名空間符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"Null 符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"數字符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"物件符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"運算子符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"套件符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"屬性符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"參考符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"程式碼片段符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"字串符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"結構符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"文字符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"型別參數符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"單位符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"變數符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\"按 Tab 現在會將焦點移至下一個可設定焦點的元素。\",\"按 Tab 現在會插入定位字元。\",\"切換 TAB 鍵移動焦點\",\"決定 Tab 鍵要在工作台四處移動焦點，或在目前的編輯器中插入定位字元。這也稱為定位點補漏白、定位點瀏覽或定位點焦點模式。\",\"開發人員: 強制重新置放\",\"延伸模組編輯器中顯示含有警告訊息的圖示。\",\"此文件包含許多非基本 ASCII Unicode 字元\",\"此文件包含許多不明確的 Unicode 字元\",\"此文件包含許多隱藏的 Unicode 字元\",\"設定 Unicode 醒目提示選項\",\"字元 {0} 可能與 ASCII 字元 {1} 混淆，這在原始程式碼中比較常見。\",\"字元 {0} 可能與字元 {1} 混淆，這在原始程式碼中比較常見。\",\"字元 {0} 隱藏。\",\"字元 {0} 不是基本的 ASCII 字元。\",\"調整設定\",\"停用註解中的醒目提示\",\"停用註解中字元的醒目提示\",\"停用字串中的醒目提示\",\"停用字串中字元的醒目提示\",\"停用不明確的醒目提示\",\"停用不明確字元的醒目提示\",\"停用隱藏醒目提示\",\"停用隱藏字元的醒目提示\",\"停用非 ASCII 醒目提示\",\"停用非基本 ASCII 字元的醒目提示\",\"顯示排除選項\",\"排除 {0} (隱藏字元) 的反白顯示\",\"將 {0} 排除在已醒目提示\",\"允許在語言「{0}」中較常用的 Unicode 字元。\",\"異常的行結束字元\",\"偵測到異常的行結束字元\",\"檔案 '{0}' 包含一或多個異常的行結束字元，例如行分隔符號 (LS) 或段落分隔符號 (PS)。\\r\\n\\r\\n建議您將其從檔案中移除。這可以透過 `editor.unusualLineTerminators` 進行設定。\",\"移除異常的行結束字元(&&R)\",\"忽略\",\"讀取權限期間 (如讀取變數) 符號的背景色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\"寫入權限期間 (如寫入變數) 符號的背景色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\"符號文字出現的背景色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\"讀取存取期間 (例如讀取變數時) 符號的邊框顏色。\",\"寫入存取期間 (例如寫入變數時) 符號的邊框顏色。 \",\"符號文字出現的框線色彩。\",\"符號醒目提示的概觀尺規標記色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\"寫入權限符號醒目提示的概觀尺規標記色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\"符號文字出現的概觀尺規標記色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\"移至下一個反白符號\",\"移至上一個反白符號\",\"觸發符號反白顯示\",\"刪除字組\",\"在位置發生錯誤\",\"錯誤\",\"警告位置\",\"警告\",\"行上發生錯誤\",\"錯誤於行\",\"行上的警告\",\"警告於行\",\"行上的摺疊區域\",\"已摺疊\",\"行上的中斷點\",\"中斷點\",\"行上的內嵌建議\",\"終端機快速修正\",\"快速修正\",\"在中斷點停止偵錯工具\",\"中斷點\",\"行上沒有嵌入提示\",\"無內嵌提示\",\"工作完成\",\"工作完成\",\"工作失敗\",\"工作失敗\",\"終端機命令失敗\",\"命令失敗\",\"終端機命令已成功\",\"命令已成功\",\"終端鈴聲\",\"終端鈴\",\"Notebook 儲存格已完成\",\"Notebook 儲存格已完成\",\"Notebook 儲存格失敗\",\"Notebook 儲存格失敗\",\"差異行已插入\",\"差異行已刪除\",\"差異行已修改\",\"聊天要求已傳送\",\"聊天要求已傳送\",\"聊天回應已接收\",\"進度\",\"進度\",\"清除\",\"清除\",\"儲存\",\"儲存\",\"格式\",\"格式\",\"語音錄製已開始\",\"語音錄製已停止\",\"檢視\",\"說明\",\"測試\",\"檔案\",\"喜好設定\",\"開發人員\",\"{0} ({1})\",\"{0} ({1})\",\"{0}\\r\\n[{1}] {2}\",\"{1} 到 {0}\",\"{0} ({1})\",\"隱藏\",\"重設功能表\",\"隱藏 '{0}'\",\"設定按鍵繫結關係\",\"{0} 套用，{1} 至預覽\",\"{0} 以套用\",\"{0}，停用原因: {1}\",\"動作小工具\",\"動作列中切換動作項目的背景色彩。\",\"是否顯示動作小工具清單\",\"隱藏動作小工具\",\"選取上一個動作\",\"選取下一個動作\",\"接受選取的動作\",\"預覽選取的動作\",\"預設語言組態覆寫\",\"設定要針對 {0} 語言覆寫的設定。\",\"設定要針對語言覆寫的編輯器設定。\",\"這個設定不支援以語言為根據的組態。\",\"設定要針對語言覆寫的編輯器設定。\",\"這個設定不支援以語言為根據的組態。\",\"無法註冊空白屬性\",\"無法註冊 '{0}'。這符合用於描述語言專用編輯器設定的屬性模式 '\\\\\\\\[.*\\\\\\\\]$'。請使用 'configurationDefaults' 貢獻。\",\"無法註冊 '{0}'。此屬性已經註冊。\",\"無法註冊 '{0}'。已向 {2} 註冊關聯的原則 {1}。\",\"傳回有關內容索引鍵資訊的命令\",\"空的內容索引鍵運算式\",\"您是否忘記撰寫運算式? 您也可以分別放置 'false' 或 'true'，以一律評估為 False 或 True。\",\"'not' 後為 'in'。\",\"右括弧 ')'\",\"未預期的權杖\",\"您是否忘記在權杖之前放置 && 或 ||?\",\"運算式未預期的結尾\",\"您是否忘記放置內容金鑰?\",\"預期: {0}\\r\\n收到: '{1}'。\",\"作業系統是否為 macOS\",\"作業系統是否為 Linux\",\"作業系統是否為 Windows\",\"平台是否為網頁瀏覽器\",\"非瀏覽器平台上的作業系統是否為 macOS\",\"作業系統是否為 iOS\",\"平臺是否為行動網頁瀏覽器\",\"VS Code 的品質類型\",\"鍵盤焦點是否位於輸入方塊內\",\"您是指 '{0}'?\",\"您是指 {0} 或 {1}?\",\"您是指 {0}、{1} 或 {2}?\",\"您是否忘記左括弧或右括弧?\",\"您是否忘記逸出 '/' (斜線) 字元? 在反斜線前放兩個反斜線以逸出，例如 '\\\\\\\\/'。\",\"是否顯示建議\",\"已按下 ({0})。等待第二個套索鍵...\",\"({0}) 已按下。正在等待下一個套索鍵...\",\"按鍵組合 ({0}, {1}) 不是命令。\",\"按鍵組合 ({0}, {1}) 不是命令。\",\"工作台\",\"對應Windows和Linux的'Control'與對應 macOS 的'Command'。\",\"對應Windows和Linux的'Alt'與對應macOS的'Option'。\",\"透過滑鼠多選，用於在樹狀目錄與清單中新增項目的輔助按鍵 (例如在總管中開啟編輯器 及 SCM 檢視)。'在側邊開啟' 滑鼠手勢 (若支援) 將會適應以避免和多選輔助按鍵衝突。\",\"控制如何使用滑鼠 (如支援此用法) 開啟樹狀目錄與清單中的項目。若不適用，某些樹狀目錄與清單可能會選擇忽略此設定。\",\"控制在工作台中，清單與樹狀結構是否支援水平捲動。警告: 開啟此設定將會影響效能。\",\"控制按一下捲軸是否逐頁捲動。\",\"控制樹狀結構縮排 (像素)。\",\"控制樹系是否應轉譯縮排輔助線。\",\"控制清單和樹狀結構是否具有平滑捲動。\",\"要用於滑鼠滾輪捲動事件 `deltaX` 和 `deltaY` 的乘數。\",\"按下 `Alt` 時的捲動速度乘數。\",\"搜尋時會醒目提示元素。進一步的向上和向下瀏覽只會周遊已醒目提示的元素。\",\"搜尋時篩選元素。\",\"控制 Workbench 中清單和樹狀結構的預設尋找模式。\",\"比對按鍵輸入的簡易按鍵瀏覽焦點元素。僅比對前置詞。\",\"醒目提示鍵盤瀏覽會醒目提示符合鍵盤輸入的元素。進一步向上或向下瀏覽只會周遊醒目提示的元素。\",\"篩選鍵盤瀏覽會篩掉並隱藏不符合鍵盤輸入的所有元素。\",\"控制 Workbench 中清單和樹狀結構的鍵盤瀏覽樣式。可以是簡易的、醒目提示和篩選。\",\"請改為使用 'workbench.list.defaultFindMode' 和 'workbench.list.typeNavigationMode'。\",\"搜尋時使用模糊比對。\",\"搜尋時使用連續比對。\",\"控制在工作台中搜尋清單和樹狀結構時所使用的比對類型。\",\"控制當按下資料夾名稱時，樹狀目錄資料夾的展開方式。請注意，若不適用，某些樹狀目錄和清單可能會選擇忽略此設定。\",\"控制是否要在樹狀中啟動黏性捲動。\",\"控制啟用 {0} 時，樹狀中顯示的黏性元素數目。\",\"控制工作台中清單和樹狀目錄的類型瀏覽運作方式。設定為 'trigger' 時，類型瀏覽會在執行 'list.triggerTypeNavigation' 命令時隨即開始。\",\"錯誤\",\"警告\",\"資訊\",\"最近使用的\",\"類似的命令\",\"經常使用\",\"其他命令\",\"類似的命令\",\"{0}, {1}\",\"命令 '{0}' 造成錯誤\",\"{0}, {1}\",\"鍵盤焦點是否位於快速輸入控制項內\",\"目前可見的快速輸入類型\",\"快速輸入中的游標是否位於輸入方塊的尾端\",\"上一頁\",\"按 'Enter' 鍵確認您的輸入或按 'Esc' 鍵取消\",\"{0}/{1}\",\"輸入以縮小結果範圍。\",\"快速挑選內容時使用。如果您變更此命令的一個按鍵繫結關係，則也應該變更此命令的所有其他按鍵繫結關係 (修飾元變體)。\",\"如果我們在快速存取模式中，這會瀏覽到下一個項目。如果我們不在快速存取模式中，這會瀏覽到下一個分隔線。\",\"如果我們在快速存取模式中，這會瀏覽到上一個項目。如果我們不在快速存取模式中，這會瀏覽到上一個分隔線。\",\"切換所有核取方塊\",\"{0} 個結果\",\"已選擇 {0}\",\"確定\",\"自訂\",\"背面 ({0})\",\"返回\",\"快速輸入\",\"按一下以執行命令 ‘{0}’\",\"整體的前景色彩。僅當未被任何元件覆疊時，才會使用此色彩。\",\"已停用元素的整體前景。只有在元件未覆蓋時，才能使用這個色彩。\",\"整體錯誤訊息的前景色彩。僅當未被任何元件覆蓋時，才會使用此色彩。\",\"提供附加訊息的前景顏色,例如標籤\",\"工作台中圖示的預設色彩。\",\"焦點項目的整體框線色彩。只在沒有任何元件覆寫此色彩時，才會加以使用。\",\"項目周圍的額外框線，可將項目從其他項目中區隔出來以提高對比。\",\"使用中項目周圍的額外邊界，可將項目從其他項目中區隔出來以提高對比。\",\"作業區域選取的背景顏色(例如輸入或文字區域)。請注意，這不適用於編輯器中的選取。\",\"內文連結的前景色彩\",\"當滑鼠點擊或懸停時，文字中連結的前景色彩。\",\"文字分隔符號的顏色。\",\"提示及建議文字的前景色彩。\",\"預先格式化文字區段的背景色彩。\",\"文內引用區塊背景色彩。\",\"引用文字的框線顏色。\",\"文字區塊的背景顏色。\",\"圖表中使用的前景色彩。\",\"用於圖表中水平線的色彩。\",\"圖表視覺效果中所使用的紅色。\",\"圖表視覺效果中所使用的藍色。\",\"圖表視覺效果中所使用的黃色。\",\"圖表視覺效果中所使用的橙色。\",\"圖表視覺效果中所使用的綠色。\",\"圖表視覺效果中所使用的紫色。\",\"編輯器的背景色彩。\",\"編輯器的預設前景色彩。\",\"編輯器中黏性滾動的背景色彩。\",\"在編輯器暫留時時，黏性滾動的背景色彩。\",\"編輯器中自黏捲動的邊界色彩\",\" 編輯器中自黏捲動的陰影色彩\",\"編輯器小工具的背景色彩，例如尋找/取代。\",\"編輯器小工具 (例如尋找/取代) 的前景色彩。\",\"編輯器小工具的邊界色彩。小工具選擇擁有邊界或色彩未被小工具覆寫時，才會使用色彩。\",\"編輯器小工具之調整大小列的邊界色彩。只在小工具選擇具有調整大小邊界且未覆寫該色彩時，才使用該色彩。\",\"編輯器中錯誤文字的背景色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\"編輯器內錯誤提示線的前景色彩.\",\"如果設定，編輯器中的錯誤會顯示雙底線色彩。\",\"編輯器中警告文字的背景色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\"編輯器內警告提示線的前景色彩.\",\"如果設定，編輯器中的警告會顯示雙底線色彩。\",\"編輯器中資訊文字的背景色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\"編輯器內資訊提示線的前景色彩\",\"如果設定，編輯器中的提示會顯示雙底線色彩。\",\"編輯器內提示訊息的提示線前景色彩\",\"如果設定，編輯器中的提示會顯示雙底線色彩。\",\"使用中之連結的色彩。\",\"編輯器選取範圍的色彩。\",\"為選取的文字顏色高對比化\",\"非使用中編輯器內的選取項目色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\"與選取項目內容相同之區域的色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\"選取時，內容相同之區域的框線色彩。\",\"符合目前搜尋的色彩。\",\"符合目前搜尋的文字色彩。\",\"其他搜尋相符項目的色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\"符合其他搜尋的前景色彩。\",\"限制搜尋之範圍的色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\"符合目前搜尋的框線色彩。\",\"符合其他搜尋的框線色彩。\",\"限制搜尋之範圍的框線色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\"在顯示動態顯示的文字下醒目提示。其不得為不透明色彩，以免隱藏底層裝飾。\",\"編輯器動態顯示的背景色彩。\",\"編輯器動態顯示的前景色彩。\",\"編輯器動態顯示的框線色彩。\",\"編輯器暫留狀態列的背景色彩。\",\"內嵌提示的前景色彩\",\"內嵌提示的背景色彩\",\"類型內嵌提示的前景色彩\",\"類型內嵌提示的背景色彩\",\"參數內嵌提示的前景色彩\",\"參數內嵌提示的背景色彩\",\"用於燈泡動作圖示的色彩。\",\"用於燈泡自動修正動作圖示的色彩。\",\"燈泡 AI 圖示使用的色彩。\",\"程式碼片段定位停駐點的反白顯示背景色彩。\",\"程式碼片段定位停駐點的反白顯示邊界色彩。\",\"程式碼片段最終定位停駐點的反白顯示背景色彩。\",\"程式碼片段最終定位停駐點的醒目提示框線色彩。\",\"已插入文字的背景色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\"已移除文字的背景色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\"已插入程式行的背景色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\"已移除程式行的背景色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\"插入程式行所在邊界的背景色彩。\",\"移除程式行所在邊界的背景色彩。\",\"插入內容的差異概觀尺規前景。\",\"移除內容的差異概觀尺規前景。\",\"插入的文字外框色彩。\",\"移除的文字外框色彩。\",\"兩個文字編輯器之間的框線色彩。\",\"Diff 編輯器的斜紋填滿色彩。斜紋填滿用於並排 Diff 檢視。\",\"Diff 編輯器中未變更區塊的背景色彩。\",\"Diff 編輯器中未變更區塊的前景色彩。\",\"Diff 編輯器中未變更程式碼的背景色彩。\",\"小工具的陰影色彩，例如編輯器中的尋找/取代。\",\"小工具的框線色彩，例如編輯器中的尋找/取代。\",\"使用滑鼠將游標停留在動作上方時的工具列背景\",\"使用滑鼠將游標停留在動作上方時的工具列外框\",\"將滑鼠移到動作上方時的工具列背景\",\"焦點階層連結項目的色彩。\",\"階層連結的背景色。\",\"焦點階層連結項目的色彩。\",\"所選階層連結項目的色彩。\",\"階層連結項目選擇器的背景色彩。\",\"內嵌合併衝突中目前的標頭背景。其不得為不透明色彩，以免隱藏底層裝飾。\",\"內嵌合併衝突中的目前內容背景。其不得為不透明色彩，以免隱藏底層裝飾。\",\"內嵌合併衝突中的傳入標頭背景。其不得為不透明色彩，以免隱藏底層裝飾。\",\"內嵌合併衝突中的傳入內容背景。其不得為不透明色彩，以免隱藏底層裝飾。\",\"內嵌合併衝突中的一般上階標頭背景。其不得為不透明色彩，以免隱藏底層裝飾。\",\"內嵌合併衝突中的一般上階內容背景。其不得為不透明色彩，以免隱藏底層裝飾。\",\"內嵌合併衝突中標頭及分隔器的邊界色彩。\",\"目前內嵌合併衝突的概觀尺規前景。\",\"傳入內嵌合併衝突的概觀尺規前景。\",\"內嵌合併衝突中的共同上階概觀尺規前景。\",\"尋找相符項目的概觀尺規標記色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\"選取項目醒目提示的概觀尺規標記。其不得為不透明色彩，以免隱藏底層裝飾。\",\"用於問題錯誤圖示的色彩。\",\"用於問題警告圖示的色彩。\",\"用於問題資訊圖示的色彩。\",\"輸入方塊的背景。\",\"輸入方塊的前景。\",\"輸入方塊的框線。\",\"輸入欄位中可使用之項目的框線色彩。\",\"在輸入欄位中所啟動選項的背景色彩。\",\"輸入欄位中選項的背景暫留色彩。\",\"在輸入欄位中所啟動選項的前景色彩。\",\"文字輸入替代字符的前景顏色。\",\"資訊嚴重性的輸入驗證背景色彩。\",\"資訊嚴重性的輸入驗證前景色彩。\",\"資訊嚴重性的輸入驗證邊界色彩。\",\"警告嚴重性的輸入驗證背景色彩。\",\"警告嚴重性的輸入驗證前景色彩。\",\"警告嚴重性的輸入驗證邊界色彩。\",\"錯誤嚴重性的輸入驗證背景色彩。\",\"錯誤嚴重性的輸入驗證前景色彩。\",\"錯誤嚴重性的輸入驗證邊界色彩。\",\"下拉式清單的背景。\",\"下拉式清單的背景。\",\"下拉式清單的前景。\",\"下拉式清單的框線。\",\"按鈕前景色彩。\",\"分隔線色彩按鈕。\",\"按鈕背景色彩。\",\"暫留時的按鈕背景色彩。\",\"按鈕框線色彩。\",\"次要按鈕前景色彩。\",\"次要按鈕背景色彩。\",\"滑鼠暫留時的次要按鈕背景色彩。\",\"使用中無線電選項的前景色彩。\",\"使用中無線電選項的背景色彩。\",\"使用中無線電選項的框線色彩。\",\"非使用中無線電選項的前景色彩。\",\"非使用中無線電選項的背景色彩。\",\"非使用中無線電選項的框線色彩。\",\"暫留時非使用中無線電選項的背景色彩。\",\"核取方塊小工具的背景色彩。\",\"選取其所處元素時，核取方塊小工具的背景色彩。\",\"核取方塊小工具的前景色彩。\",\"核取方塊小工具的框線色彩。\",\"選取其所處元素時，核取方塊小工具的框線色彩。\",\"金鑰綁定標籤背景色彩。按鍵綁定標籤用來代表鍵盤快速鍵。\",\"金鑰綁定標籤前景色彩。按鍵綁定標籤用來代表鍵盤快速鍵。\",\"金鑰綁定標籤邊框色彩。按鍵綁定標籤用來代表鍵盤快速鍵。\",\"金鑰綁定標籤邊框底部色彩。按鍵綁定標籤用來代表鍵盤快速鍵。\",\"當清單/樹狀為使用中狀態時，焦點項目的清單/樹狀背景色彩。使用中的清單/樹狀有鍵盤焦點，非使用中者則沒有。\",\"當清單/樹狀為使用中狀態時，焦點項目的清單/樹狀前景色彩。使用中的清單/樹狀有鍵盤焦點，非使用中者則沒有。\",\"當清單/樹狀目錄為使用中狀態時，焦點項目的清單/樹狀目錄外框色彩。使用中的清單/樹狀目錄有鍵盤焦點，非使用中者則沒有。\",\"當清單/樹狀目錄為使用中狀態並已選取時，焦點項目的清單/樹狀目錄外框色彩。使用中的清單/樹狀目錄具有鍵盤焦點，非使用中者則沒有。\",\"當清單/樹狀為使用中狀態時，所選項目的清單/樹狀背景色彩。使用中的清單/樹狀有鍵盤焦點，非使用中者則沒有。\",\"當清單/樹狀為使用中狀態時，所選項目的清單/樹狀前景色彩。使用中的清單/樹狀有鍵盤焦點，非使用中者則沒有。\",\"當清單/樹狀為使用中狀態時，所選項目的清單/樹狀圖示前景色彩。使用中的清單/樹狀有鍵盤焦點，非使用中者則沒有。\",\"當清單/樹狀為非使用中狀態時，所選項目的清單/樹狀背景色彩。使用中的清單/樹狀有鍵盤焦點，非使用中者則沒有。\",\"當清單/樹狀為使用中狀態時，所選項目的清單/樹狀前景色彩。使用中的清單/樹狀有鍵盤焦點，非使用中則沒有。\",\"當清單/樹狀為非使用中狀態時，所選項目的清單/樹狀圖示前景色彩。使用中的清單/樹狀有鍵盤焦點，非使用中者則沒有。\",\"當清單/樹狀為非使用中狀態時，焦點項目的清單/樹狀背景色彩。使用中的清單/樹狀有鍵盤焦點，非使用中者則沒有。\",\"當清單/樹狀目錄為非使用中狀態時，焦點項目的清單/樹狀目錄外框色彩。使用中的清單/樹狀目錄有鍵盤焦點，非使用中者則沒有。\",\"使用滑鼠暫留在項目時的清單/樹狀背景。\",\"滑鼠暫留在項目時的清單/樹狀前景。\",\"使用滑鼠將項目移到其他項目上時的清單/樹狀拖放背景。\",\"使用滑鼠移動項目時的清單/樹狀目錄拖放邊界色彩。\",\"在清單/樹狀內搜尋時，相符醒目提示的清單/樹狀前景色彩。\",\"在清單/樹狀內搜尋時，相符項目的清單/樹狀結構前景色彩會針對主動焦點項目進行強調顯示。\",\"列表/樹狀 無效項目的前景色彩，例如在瀏覽視窗無法解析的根目錄\",\"包含錯誤清單項目的前景色彩\",\"包含警告清單項目的前景色彩\",\"清單和樹狀結構中類型篩選小工具的背景色彩。\",\"清單和樹狀結構中類型篩選小工具的大綱色彩。\",\"在沒有相符項目時，清單和樹狀結構中類型篩選小工具的大綱色彩。\",\"清單和樹狀結構中類型篩選小工具的陰影色彩。\",\"已篩選相符項的背景色彩。\",\"已篩選相符項的框線色彩。\",\"已取消強調的清單/樹狀前景色彩。\",\"縮排輔助線的樹狀筆觸色彩。\",\"非使用中縮排輔助線的樹狀筆觸色彩。\",\"資料行之間的資料表邊界色彩。\",\"奇數資料表資料列的背景色彩。\",\"動作清單背景色彩。\",\"動作清單前景色彩。\",\"焦點項目的動作清單前景色彩。\",\"焦點項目的動作清單背景色彩。\",\"功能表的邊框色彩。\",\"功能表項目的前景色彩。\",\"功能表項目的背景色彩。\",\"功能表中所選功能表項目的前景色彩。\",\"功能表中所選功能表項目的背景色彩。\",\"功能表中所選功能表項目的框線色彩。\",\"功能表中分隔線功能表項目的色彩。\",\"用於尋找相符項目的縮圖標記色彩。\",\"重複編輯器選取項目的縮圖標記色彩。\",\"編輯器選取範圍的迷你地圖標記色彩。\",\"資訊的縮圖標記色彩。\",\"警告的縮圖標記色彩。\",\"錯誤的縮圖標記色彩。\",\"縮圖背景色彩。\",\"在縮圖中呈現的前景元素不透明度。例如，\\\"#000000c0\\\" 會以不透明度 75% 轉譯元素。\",\"縮圖滑桿背景色彩。\",\"暫留時的縮圖滑桿背景色彩。\",\"按一下時的縮圖滑桿背景色彩。\",\"使用中飾帶的框線色彩。\",\"標記的背景顏色。標記為小型的訊息標籤,例如搜尋結果的數量。\",\"標記的前景顏色。標記為小型的訊息標籤,例如搜尋結果的數量。\",\"指出在捲動該檢視的捲軸陰影。\",\"捲軸滑桿的背景顏色。\",\"動態顯示時捲軸滑桿的背景顏色。\",\"當點擊時捲軸滑桿的背景顏色。\",\"長時間運行進度條的背景色彩.\",\"快速選擇器背景色彩。該快速選擇器小工具是類似命令選擇區的選擇器容器。\",\"快速選擇器前景色彩。快速選擇器小工具是類似命令選擇區等選擇器的容器。\",\"快速選擇器標題背景色彩。快速選擇器小工具是類似命令選擇區的選擇器容器。\",\"分組標籤的快速選擇器色彩。\",\"分組邊界的快速選擇器色彩。\",\"請改用 quickInputList.focusBackground\",\"焦點項目的快速選擇器前景色彩。\",\"焦點項目的快速選擇器圖示前景色彩。\",\"焦點項目的快速選擇器背景色彩。\",\"搜尋 Viewlet 完成訊息中文字的色彩。\",\"搜尋編輯器查詢符合的色彩。\",\"搜索編輯器查詢符合的邊框色彩。\",\"此色彩必須是透明的，否則會遮住內容\",\"使用預設色彩。\",\"要使用的字型識別碼。如未設定，就會使用最先定義的字型。\",\"與圖示定義建立關聯的字型字元。\",\"小工具中關閉動作的圖示。\",\"移至上一個編輯器位置的圖示。\",\"移至下一個編輯器位置的圖示。\",\"已在磁碟上關閉並修改以下檔案: {0}。\",\"下列檔案已使用不相容的方式修改: {0}。\",\"無法復原所有檔案的 '{0}'。{1}\",\"無法復原所有檔案的 '{0}'。{1}\",\"因為已對 {1} 進行變更，所以無法復原所有檔案的 '{0}'\",\"因為 {1} 中已經有正在執行的復原或重做作業，所以無法為所有檔案復原 '{0}'\",\"因為同時發生其他復原或重做作業，所以無法為所有檔案復原 '{0}'\",\"要復原所有檔案的 '{0}' 嗎?\",\"在 {0} 個檔案中復原(&&U)\",\"復原此檔案(&&F)\",\"因為已經有正在執行的復原或重做作業，所以無法復原 '{0}'。\",\"要復原 '{0}' 嗎?\",\"是(&&Y)\",\"否\",\"無法復原所有檔案的 '{0}'。{1}\",\"無法復原所有檔案的 '{0}'。{1}\",\"因為已對 {1} 進行變更，所以無法復原所有檔案的 '{0}'\",\"因為 {1} 中已經有正在執行的復原或重做作業，所以無法為所有檔案重做 '{0}'\",\"因為同時發生其他復原或重做作業，所以無法為所有檔案重做 '{0}'\",\"因為已經有正在執行的復原或重做作業，所以無法重做 '{0}'。\",\"Code 工作區\"];\nglobalThis._VSCODE_NLS_LANGUAGE=\"zh-tw\";"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+DAOA,WAAW,qBAAqB,CAAC,YAAY,eAAK,uCAAS,6CAAU,6CAAU,eAAK,iCAAQ,kGAAuB,mPAA2C,oBAAU,oBAAU,oBAAU,yDAAiB,oDAAiB,iCAAQ,qBAAM,2BAAO,2BAAO,eAAK,2BAAO,uCAAS,uCAAS,uCAAS,eAAK,2BAAO,uCAAS,KAAK,iBAAO,WAAW,6CAAe,uIAAyB,uIAAyB,4CAAmB,uIAAyB,OAAO,QAAQ,MAAM,UAAU,OAAO,QAAQ,MAAM,qBAAM,UAAU,QAAQ,eAAK,eAAK,UAAU,QAAQ,MAAM,UAAU,UAAU,QAAQ,MAAM,qBAAM,KAAK,KAAK,KAAK,KAAK,KAAK,mGAAmB,mGAAmB,yDAAY,oBAAU,eAAK,gCAAY,eAAK,oBAAU,eAAK,0EAAmB,8BAAU,2VAA+H,mDAAW,+DAAa,2EAAe,uFAAiB,0BAAW,2BAAO,uCAAS,+DAAa,+DAAa,eAAK,mDAAgB,6CAAU,6CAAU,iGAAsB,iGAAsB,iGAAsB,eAAK,uIAA8B,uCAAS,8BAAU,gCAAY,4HAA4C,eAAK,mCAAe,0DAAuB,+BAAgB,+BAAgB,iFAAqB,6CAAU,6CAAU,uCAAS,uCAAS,6DAAgB,6CAAe,iCAAQ,qEAAc,+DAAa,2BAAO,uCAAS,+DAAa,mDAAW,+BAAW,mGAAmB,mDAAW,mGAAmB,+BAAW,uCAAS,gGAA0B,gGAA0B,qDAAkB,4DAAoB,6CAAU,2BAAO,yGAAyB,2HAA4B,yGAAoB,4FAAsB,4FAAsB,oEAAkB,iFAAqB,iFAAqB,6CAAU,qBAAM,qLAAyC,wQAAmG,2KAAyC,4JAAoC,uFAAiB,+JAA6B,6CAAe,6FAAkB,iIAAwB,mGAAmB,uLAAiC,yGAAoB,yGAAoB,qKAAkD,6GAAuC,0IAAiC,sHAA4B,gIAA4B,iIAAwB,gPAA4D,qOAA0E,6FAAkB,qEAAc,qEAAc,2KAA+B,qEAAc,qEAAc,kJAAoC,yIAAgC,kGAA4B,uIAAyB,uIAAyB,8JAAiC,wJAAgC,sIAA6B,6IAAoC,wEAAsB,uCAAS,yDAAY,oEAAkB,+DAAa,qEAAc,+GAAqB,iFAAgB,6FAAkB,6IAA0B,uIAA8B,mMAAmC,uLAAiC,wGAAwB,yGAAoB,2EAAe,uJAA+B,mGAAmB,2KAA+B,6IAA0B,6IAA0B,+GAAqB,qKAA8B,2HAAuB,6JAAgC,+GAA0B,gGAAqB,oIAA2B,mHAAyB,0JAAkC,oTAA0D,6MAAwC,6OAAkF,yMAA6D,mPAAoE,6PAAuG,mOAAiG,uSAA8G,4DAAe,wKAAsC,4MAA4C,wEAAiB,iFAAgB,qHAAsB,iNAA4H,yIAA0C,8IAA+C,0IAA2C,8IAA+C,yIAA0C,uKAA+C,4KAAoD,wKAAgD,2KAAmD,sKAA8C,yDAAY,kHAAwB,qHAAsB,sKAAmD,+GAAqB,0UAA4D,ySAAoD,uOAAyC,2EAAe,+JAA6B,iLAAgC,6FAAkB,2HAAuB,uFAAiB,iXAAgE,iIAAwB,2EAAe,6CAAU,8GAAyB,8GAAyB,6CAAU,8QAAuD,qLAAoC,+GAAqB;AAAA;AAAA;AAAA,wGAA0F,yDAAY,2EAAe,wHAAyB,gJAA6B,gJAA6B,mDAAW,qEAAc,iFAAgB,8FAAwB,+GAAqB,qHAAsB,iIAAwB,4HAA6B,6FAAkB,iMAAsC,qHAAsB,2HAAuB,mJAA2B,iIAAwB,uFAAiB,iFAAgB,6CAAU,iFAAgB,iFAAgB,qHAAsB,sgBAA6F,uCAAS,yDAAY,uFAAiB,iDAAc,mDAAW,yGAAoB,+DAAa,+SAAqD,uFAAiB,yDAAY,yDAAY,2EAAe,uFAAiB,yDAAY,yDAAY,2EAAe,mDAAW,mDAAW,uIAAyB,iIAAwB,6RAA2E,yJAA4B,8OAAgD,8HAA+B,8HAA+B,iFAAgB,oIAAgC,qHAAsB,2HAAuB,yJAA4B,2EAAe,uFAAiB,2QAA+C,2EAAe,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,8JAAiC,iIAAwB,+DAAa,+GAAqB,+DAAa,uFAAiB,mJAA2B,yGAAoB,2EAAe,yGAAoB,iIAAwB,mGAAmB,uFAAiB,qKAA8B,mGAAmB,uIAAyB,6FAAkB,uFAAiB,qNAAsC,qHAAsB,mGAAmB,0MAA8D,yFAA6B,yFAA6B,6HAAmC,uHAAkC,+VAAiF,uIAAyB,6FAAkB,yGAAoB,yGAAoB,2NAAuC,qHAAsB,uKAAkF,8FAA6B,8FAA6B,0GAA+B,oGAA8B,2aAAyI,8FAA6B,8FAA6B,8FAA6B,8FAA6B,8FAA6B,8FAA6B,8FAA6B,8FAA6B,oGAA8B,8FAA6B,wFAA4B,8FAA6B,8FAA6B,4FAAqC,oGAA8B,8FAA6B,8FAA6B,8FAA6B,8FAA6B,6FAAsC,oGAA8B,+FAAwC,gHAAgC,yFAA4C,qFAA6C,+GAAqB,qHAA+C,2TAAgF,2TAAgF,8EAAkB,2EAAe,4DAAe,sDAAc,mDAAW,iOAAkD,2NAAuC,2HAAuB,qKAA8B,mGAAmB,iOAAwC,2HAAuB,qKAA8B,4QAA+D,8HAA+B,qOAAsD,iXAAqE,iCAAQ,yGAAoB,yGAAoB,qHAAsB,qKAA8B,yGAAoB,qHAAsB,iLAAgC,6IAA0B,mJAA2B,uIAAyB,qHAAsB,yGAAoB,qHAAsB,iLAAgC,2EAAe,iFAAgB,6IAA0B,0MAA+C,wRAA2E,mMAAmC,2HAAuB,iFAAgB,kFAAiB,+JAA6B,6OAA0C,wEAAsB,6DAAqB,oKAAgE,yJAA4B,mJAA2B,+GAAqB,qHAAsB,2HAAuB,mJAA2B,2HAAuB,+GAAqB,qEAAc,qEAAc,uLAAiC,2EAAe,8FAAmB,6FAAkB,uSAAqF,iIAAiD,gEAAkC,wGAAiD,iHAAgD,iIAAwB,8DAAiB,6FAAkB,yDAAY,iIAAwB,4EAAqB,yGAAoB,4JAA+B,+DAAa,iFAAgB,qHAAsB,yGAAoB,6RAAkD,6IAA0B,6CAAU,iUAAwD,iIAAwB,uLAAiC,mGAAmB,4DAAe,+NAAgD,2HAAuB,qEAAc,uHAAuC,4HAA6B,6HAA8B,iFAAgB,8FAAiD,uFAA0C,4YAA6I,iFAAgB,qEAAc,6IAA0B,iIAAwB,+DAAa,mGAAmB,mIAA0B,2HAAuB,yGAAoB,yDAAY,mDAAW,iIAAwB,yJAA4B,8HAA0B,+GAAqB,0EAAkC,6FAAkB,2HAAuB,iFAAgB,+GAAqB,iLAAgC,2HAAuB,iFAAgB,+DAAa,6FAAkB,qEAAc,mGAAmB,yGAAoB,yPAA4C,kFAAsB,yJAA4B,+DAAa,+GAAqB,iIAAwB,+GAAqB,2EAAe,qEAAc,qHAAsB,qHAAsB,+GAAqB,qEAAc,yJAA4B,uFAAiB,yMAAoC,6JAAqC,iLAA0C,2HAAuB,+DAAa,sPAAuE,6KAA0D,qHAAsB,sGAA2B,wDAAgB,wLAAsD,wDAAgB,iFAAgB,qEAAc,6FAAkB,yGAAoB,yJAA4B,2EAAe,kNAA6C,wHAA8B,yJAA4B,uCAAS,yDAAY,sDAAkC,wHAA6C,6CAAU,iKAAuE,yJAA4B,2KAA+B,yGAAoB,+GAAqB,qQAA8C,mGAAmB,gTAAsD,uFAAiB,yDAAY,qKAA8B,2HAAuB,uOAAyC,2HAAuB,uOAAyC,2EAAe,yDAAY,2EAAe,wHAA2E,6FAAkB,oIAAuF,+EAAmB,+EAAmB,+EAAmB,+EAAmB,+EAAmB,+EAAmB,iGAAsB,iGAAsB,iGAAsB,iGAAsB,iGAAsB,iGAAsB,qEAAc,2FAAmD,qEAAc,+IAAgD,mDAAW,iFAAgB,mDAAW,mDAAW,wFAAkB,iFAAgB,yIAA2B,qIAA4B,8eAAkI,6FAAkB,6FAAkB,6FAAkB,qNAAsC,2EAAe,2EAAe,2EAAe,gIAA4B,gIAA4B,gIAA4B,gIAA4B,gIAA4B,gIAA4B,qEAAc,sLAAqC,sLAAqC,sLAAqC,sLAAqC,sLAAqC,sLAAqC,gLAAoC,gLAAoC,gLAAoC,gLAAoC,gLAAoC,gLAAoC,gGAA0B,gGAA0B,0FAAoB,4KAAqC,qGAA0B,mDAAW,yDAAiB,qEAAmB,KAAK,mGAAwB,wDAAgB,mGAAmB,2EAAe,qHAAsB,yDAAY,uFAAsB,uFAAsB,qCAAY,2CAAa,0DAAiC,+DAAa,2EAAe,2EAAoB,yDAAY,yDAAY,mDAAW,mDAAW,2EAAe,2EAAe,8HAA0B,yDAAY,iFAAgB,uFAAiB,mEAAsB,qEAAc,qEAAc,qEAAc,iFAAgB,qEAAc,6FAAkB,iFAAgB,qEAAc,iFAAgB,iFAAgB,iFAAgB,uFAAiB,+GAAqB,mGAAmB,2HAAuB,eAAK,qBAAM,eAAK,eAAK,2BAAO,eAAK,2BAAO,eAAK,eAAK,eAAK,eAAK,eAAK,qBAAM,eAAK,eAAK,2BAAO,OAAO,eAAK,eAAK,qBAAM,eAAK,eAAK,eAAK,eAAK,2BAAO,eAAK,YAAY,qBAAM,2BAAO,qDAAa,+BAAW,qEAAc,iCAAQ,6CAAU,8BAAU,mDAAgB,iCAAQ,yDAAY,yEAAuB,iCAAa,YAAY,uCAAS,yCAAgB,mDAAW,mDAAW,iFAAgB,mDAAW,uFAAiB,iCAAQ,iCAAQ,2BAAO,gCAAY,mGAAmB,yDAAY,yDAAY,2BAAO,oBAAU,eAAK,eAAK,eAAK,oBAAU,eAAK,eAAK,eAAK,oBAAU,eAAK,eAAK,eAAK,yDAAY,qBAAM,qBAAM,eAAK,eAAK,6FAAkB,2EAAe,iFAAgB,mGAAmB,uIAAyB,iFAAgB,yGAAoB,8BAAU,+DAAa,sEAAsB,sEAAsB,2EAAe,+DAAa,kBAAQ,4EAAqB,oDAAmB,yDAAY,6CAAU,8BAAU,kFAAsB,gEAAqB,qEAAc,yDAAY,2BAAO,iFAAgB,2BAAO,+DAAa,8BAAU,yDAAY,sIAA6B,uJAA+B,2NAAsD,4DAAyB,6CAAU,6CAAU,8BAAU,2BAAO,eAAK,eAAK,eAAK,eAAK,iCAAQ,2BAAO,iLAAgC,uOAAyC,2OAA6C,yQAAkD,yQAAkD,oBAAU,+GAA0B,mDAAgB,6CAAU,8DAAsB,2BAAO,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,iCAAQ,sCAAa,iCAAQ,iCAAQ,uCAAS,4CAAc,eAAK,2BAAO,2BAAO,qBAAM,eAAK,2BAAO,eAAK,uCAAS,eAAK,+DAAa,2BAAO,2BAAO;AAAA,0QAAiE,wBAAS,iCAAQ,yDAAY,0CAAY,0DAAkB,6FAAkB,uIAAyB,uCAAS,mDAAW,iCAAQ,mBAAS,mBAAS,2BAAO,2BAAO,uCAAS,uCAAS,oBAAU,KAAK,yDAAY,0CAAY,yGAAoB;AAAA,KAA2B;AAAA,KAA2B,uIAAyB,mGAAmB,eAAK,oBAAU,uCAAS,uCAAS,iCAAQ,iCAAQ,0CAAY,mGAAmB,6GAA6B,mFAAuB,mFAAuB,yDAAY,yDAAY,eAAK,oBAAU,yGAAoB,yGAAoB,qIAA4B,iGAAsB,6GAAwB,mHAAyB,mHAAyB,4BAAQ,eAAK,eAAK,6CAAU,6CAAU,mDAAW,eAAK,eAAK,eAAK,eAAK,2BAAO,2BAAO,0KAAmC,iBAAY,2BAAO,mBAAS,gCAAiB,sDAAwB,sCAAkB,4QAA8E,eAAK,6CAAU,eAAK,2BAAO,6CAAU,yDAAY,mDAAW,uCAAS,uCAAS,iFAAgB,iFAAgB,2BAAO,2BAAO,uCAAS,yDAAY,yDAAY,qEAAc,mDAAW,+BAAW,yMAAoC,yGAAoB,6FAAkB,6GAAwB,6GAAwB,+GAAqB,+GAAqB,yDAAY,yDAAY,yDAAY,yDAAY,yDAAY,iCAAQ,6CAAU,wFAAuB,wEAAiB,wFAAuB,wEAAiB,sHAA4B,sCAAa,sHAA4B,sCAAa,eAAK,eAAK,eAAK,eAAK,uBAAa,6CAAoB,6CAAoB,mGAAmB,yGAAoB,mGAAmB,yGAAoB,6FAAkB,yGAAoB,uFAAiB,eAAK,eAAK,8CAAgB,6CAAU,gCAAY,eAAK,+CAAiB,6CAAU,gCAAY,+CAAiB,6CAAU,2BAAO,sEAAoB,yDAAY,4CAAc,eAAK,0DAAkB,6CAAU,gCAAY,8CAAkB,iCAAQ,gCAAY,eAAK,eAAK,eAAK,iCAAa,eAAK,2BAAO,6CAAU,2BAAO,2BAAO,2BAAO,uCAAS,uCAAS,2BAAO,2BAAO,2BAAO,2BAAO,uCAAS,oEAAkB,mJAA2B,8BAAU,YAAY,yBAAU,yBAAU,eAAK,2BAAO,2BAAO,eAAK,4DAAyB,uEAA+B,qEAAwB,uEAA0B,iCAAQ,qDAAkB,uDAAoB,yEAAuB,yGAAoB,+DAAuB,6BAAc,+DAAa,+DAAa,6CAAU,2EAAe,6FAAkB,iFAAgB,mDAAW,uCAAS,uCAAS,uCAAS,uCAAS,iCAAQ,iCAAQ,uCAAS,uCAAS,qNAAsC,uFAAiB,+DAAa,+DAAa,+DAAa,+DAAa,qEAAc,qEAAc,qEAAc,qEAAc,2EAAe,2EAAe,8BAAU,wKAA0D,wLAAqE,yDAAiB,mDAAW,yDAAiB,mDAAW,2BAAO,yDAAY,sDAAc,yDAAY,8BAAU,mDAAW,yDAAY,yDAAY,mDAAW,yDAAY,2EAAe,4CAAc,yDAAY,+DAAa,mDAAW,uCAAS,yDAAY,iFAAgB,iFAAgB,yDAAY,6CAAU,6FAAkB,yDAAY,+DAAa,2EAAe,uCAAS,2BAAY,4BAAa,oCAAW,2BAAY,kHAA6B,iCAAa,2BAAO,yDAAY,yDAAY,uCAAS,qEAAc,2BAAO,+DAAa,qBAAM,uCAAS,eAAK,uCAAS,6CAAU,mDAAW,iFAAgB,kJAA+B,uFAAiB,iFAAqB,gBAAM,oFAAmB,oFAAmB,YAAY,qBAAM,qBAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,6CAAU,6CAAU,mDAAW,uCAAS,4CAAc,uCAAS,4CAAc,uCAAS,4CAAc,2BAAO,gCAAY,2BAAO,gCAAY,iCAAQ,iCAAQ,uCAAS,uCAAS,qBAAM,qBAAM,qBAAM,uCAAS,uCAAS,uCAAS,mDAAW,qBAAM,+DAAa,iCAAQ,iCAAQ,6CAAU,mDAAW,wCAAe,uEAAqB,gCAAiB,6CAAU,qHAAsB,oHAA0B,yGAAoB,2BAAO,2BAAO,2BAAY,4BAAa,oCAAW,2BAAY,+BAAW,2BAAO,6FAAkB,kDAAe,kDAAe,6CAAU,kDAAe,6CAAU,kDAAe,6CAAU,kDAAe,oDAAY,mDAAW,mGAAmB,kDAAe,yGAAoB,kDAAe,2HAAuB,2HAAuB,qEAAc,4CAAc,mDAAW,6CAAU,6CAAU,6CAAU,6CAAU,uCAAS,oFAAmB,oFAAmB,wBAAS,mGAAmB,yGAAoB,eAAK,uFAAiB,+DAAa,2EAAe,uFAAiB,6FAAkB,yGAAoB,+GAAqB,uIAAyB,uIAAyB,iFAAgB,+IAA4B,+GAAqB,2HAAuB,qHAAsB,2HAAuB,mGAAmB,6FAAkB,yEAAuB,sCAAa,qKAA6C,yHAA+B,mJAA2B,yGAAoB,+DAAa,6CAAU,2BAAO,iCAAQ,qBAAW,qBAAW,qBAAW,qBAAW,iCAAa,qBAAW,qBAAW,qBAAW,qBAAW,2BAAY,qBAAW,iCAAa,qBAAW,iCAAa,qBAAW,qBAAW,qBAAW,iCAAa,qBAAW,qBAAW,qBAAW,qBAAW,2BAAY,qBAAW,2BAAY,qBAAW,qBAAW,+DAAa,qEAAc,iCAAQ,mGAAmB,gEAAwB,4DAAoB,6FAAiC,yDAAY,+DAAa,uCAAS,0GAAqB,qEAAc,qEAAc,iFAAgB,uFAAiB,2FAA0B,8DAAiB,8IAAgC,6CAAU,eAAK,uCAAS,4CAAc,uCAAS,4CAAc,mGAAmB,2HAAuB,2HAAuB,4DAAe,qBAAM,qBAAM,qBAAM,qBAAM,qBAAM,qBAAM,qBAAM,eAAK,eAAK,eAAK,eAAK,eAAK,eAAK,eAAK,eAAK,eAAK,eAAK,eAAK,eAAK,eAAK,eAAK,eAAK,eAAK,eAAK,qBAAM,qBAAM,UAAK,UAAK,WAAM,UAAK,eAAK,UAAK,WAAM,WAAM,WAAM,YAAO,YAAO,YAAO,8DAAiB,2BAAO,gCAAY,4CAAc,yDAAY,8IAA2B,+DAAa,iFAAgB,+DAAa,yDAAY,iCAAQ,mDAAW,+DAAa,qEAAc,iIAAwB,sEAAoB,6FAAkB,+DAAa,yGAAoB,qFAAyB,2BAAO,eAAK,eAAK,eAAK,eAAK,eAAK,2BAAO,2BAAO,yDAAY,qEAAc,qEAAc,qEAAc,mGAAmB,+GAAqB,mGAAmB,mGAAmB,iLAAgC,iFAAgB,8BAAU,2BAAO,eAAK,mBAAc,UAAU,eAAU,6BAAc,eAAK,8BAAU,uGAAuB,2BAAO,yMAAoC,+MAAqC,yMAAoC,yMAAoC,yMAAoC,qNAAsC,+MAAqC,2NAAuC,yMAAoC,yMAAoC,yMAAoC,+MAAqC,yMAAoC,yMAAoC,+MAAqC,+MAAqC,yMAAoC,yMAAoC,qNAAsC,kMAAuC,yMAAoC,yMAAoC,+MAAqC,yMAAoC,yMAAoC,yMAAoC,2NAAuC,yMAAoC,yMAAoC,yMAAoC,qNAAsC,yMAAoC,yMAAoC,sIAA6B,0EAAmB,kDAAe,wVAAgE,iEAAe,2HAAuB,0FAA8B,0FAAyB,oFAAwB,4DAAoB,8JAA2C,uJAAoC,sCAAa,2EAAyB,2BAAO,+DAAa,2EAAe,+DAAa,2EAAe,+DAAa,2EAAe,mDAAW,qEAAc,oDAAiB,kFAAsB,uCAAS,6EAAsB,8DAAiB,yGAA8B,mDAAW,qEAAc,kYAAoH,oEAAkB,eAAK,2OAA6C,2OAA6C,6LAAkC,qIAA4B,sIAA6B,2EAAe,qNAAsC,6OAA0C,qNAAsC,yDAAY,yDAAY,mDAAW,2BAAO,6CAAU,eAAK,2BAAO,eAAK,uCAAS,2BAAO,iCAAQ,2BAAO,6CAAU,qBAAM,uCAAS,qBAAM,6CAAU,6CAAU,2BAAO,+DAAa,qBAAM,mDAAW,iCAAQ,2BAAO,2BAAO,2BAAO,2BAAO,6CAAU,2BAAO,mDAAW,iCAAQ,2BAAO,qBAAM,gDAAkB,gDAAkB,0CAAiB,0CAAiB,uCAAS,uCAAS,uCAAS,6CAAU,6CAAU,6CAAU,eAAK,eAAK,eAAK,eAAK,eAAK,eAAK,eAAK,eAAK,6CAAU,6CAAU,eAAK,eAAK,eAAK,eAAK,2BAAO,2BAAO,YAAY,YAAY;AAAA,WAAmB,iBAAY,YAAY,eAAK,iCAAQ,qBAAW,mDAAW,+CAAiB,yBAAU,yCAAgB,iCAAQ,mGAAmB,qEAAc,6CAAU,6CAAU,6CAAU,6CAAU,6CAAU,mDAAW,sFAAqB,mGAAmB,yGAAoB,mGAAmB,yGAAoB,mDAAW,wPAAmF,uFAAsB,4GAAiC,uFAAiB,+DAAa,yMAA6D,gCAAiB,yBAAU,uCAAS,yFAAwB,yDAAY,sEAAe;AAAA,2BAAwB,mDAAgB,mDAAgB,qDAAkB,+DAAa,mGAAwB,iDAAc,2EAAe,yCAAgB,iFAAgB,4BAAa,qCAAiB,8CAAqB,4EAAgB,mMAAkD,uCAAS,oFAAwB,gGAA0B,qEAAwB,qEAAwB,qBAAM,8FAAiD,uFAA0C,6bAA0F,qUAA4D,yOAA2C,uFAAiB,wEAAiB,6FAAkB,+GAAqB,uHAAuC,4EAAqB,qNAAsC,mDAAW,0HAAgC,yJAA4B,iRAAgD,yJAA4B,oNAA+C,mHAAgF,+DAAa,+DAAa,+JAA6B,uUAAyD,mGAAmB,0HAA2B,oTAAwF,eAAK,eAAK,eAAK,iCAAQ,iCAAQ,2BAAO,2BAAO,iCAAQ,WAAW,8CAAgB,WAAW,mGAAmB,qEAAc,qHAAsB,qBAAM,iGAAgC,UAAU,+DAAa,0UAA4D,+SAAqD,+SAAqD,mDAAW,yBAAU,yBAAU,eAAK,eAAK,qBAAW,eAAK,2BAAO,mEAAiB,2KAA+B,uLAAiC,mMAAmC,8FAAmB,2EAAe,+MAAqC,uLAAiC,yMAAoC,yOAA2C,yDAAY,iIAAwB,+DAAa,iFAAgB,6FAAkB,qEAAc,+DAAa,+DAAa,qEAAc,2EAAe,uFAAiB,uFAAiB,uFAAiB,uFAAiB,uFAAiB,uFAAiB,yDAAY,qEAAc,uFAAiB,qHAAsB,iFAAgB,kFAAiB,sHAAuB,oHAA0B,mPAA2C,ySAAoD,yMAAoC,wFAAkB,iIAAwB,yMAAoC,wFAAkB,iIAAwB,yMAAoC,uFAAiB,iIAAwB,mGAAmB,iIAAwB,+DAAa,qEAAc,2EAAe,qNAAsC,qNAAsC,yGAAoB,+DAAa,2EAAe,6LAAkC,2EAAe,uLAAiC,2EAAe,2EAAe,mMAAmC,qNAAsC,iFAAgB,iFAAgB,iFAAgB,uFAAiB,yDAAY,yDAAY,qEAAc,qEAAc,qEAAc,qEAAc,2EAAe,mGAAmB,mEAAiB,2HAAuB,2HAAuB,uIAAyB,uIAAyB,uLAAiC,uLAAiC,6LAAkC,6LAAkC,6FAAkB,6FAAkB,uFAAiB,uFAAiB,+DAAa,+DAAa,6FAAkB,kJAAoC,kGAAuB,kGAAuB,wGAAwB,kIAAyB,kIAAyB,iIAAwB,iIAAwB,mGAAmB,2EAAe,yDAAY,2EAAe,2EAAe,6FAAkB,+MAAqC,+MAAqC,+MAAqC,+MAAqC,2NAAuC,2NAAuC,qHAAsB,mGAAmB,mGAAmB,qHAAsB,qNAAsC,qNAAsC,2EAAe,2EAAe,2EAAe,mDAAW,mDAAW,mDAAW,yGAAoB,yGAAoB,6FAAkB,yGAAoB,uFAAiB,6FAAkB,6FAAkB,6FAAkB,6FAAkB,6FAAkB,6FAAkB,6FAAkB,6FAAkB,6FAAkB,yDAAY,yDAAY,yDAAY,yDAAY,6CAAU,mDAAW,6CAAU,qEAAc,6CAAU,yDAAY,yDAAY,6FAAkB,uFAAiB,uFAAiB,uFAAiB,6FAAkB,6FAAkB,6FAAkB,+GAAqB,iFAAgB,uIAAyB,iFAAgB,iFAAgB,uIAAyB,qKAA8B,qKAA8B,qKAA8B,iLAAgC,kTAAwD,kTAAwD,sVAA8D,oXAAmE,kTAAwD,kTAAwD,8TAA0D,wTAAyD,4SAAuD,oUAA2D,wTAAyD,4VAA+D,gHAAsB,oGAAoB,0JAA6B,8IAA2B,iKAA+B,2PAA8C,mLAAkC,iFAAgB,iFAAgB,iIAAwB,iIAAwB,uLAAiC,iIAAwB,2EAAe,2EAAe,8FAAmB,iFAAgB,yGAAoB,uFAAiB,uFAAiB,yDAAY,yDAAY,uFAAiB,uFAAiB,yDAAY,qEAAc,qEAAc,yGAAoB,yGAAoB,yGAAoB,mGAAmB,mGAAmB,yGAAoB,yGAAoB,+DAAa,+DAAa,+DAAa,6CAAU,wMAAoD,yDAAY,iFAAgB,uFAAiB,qEAAc,4KAAgC,4KAAgC,uFAAiB,+DAAa,6FAAkB,uFAAiB,kFAAiB,+MAAqC,+MAAqC,qNAAsC,iFAAgB,iFAAgB,oDAAqC,6FAAkB,yGAAoB,6FAAkB,0FAAyB,iFAAgB,6FAAkB,yGAAoB,6CAAU,qKAA8B,6FAAkB,2EAAe,uFAAiB,uFAAiB,kGAAuB,wGAAwB,wEAAsB,wEAAsB,sIAAkC,kMAA4C,2KAAoC,iEAAoB,uDAAoB,sCAAa,+JAAkC,mCAAe,cAAS,SAAI,wEAAsB,wEAAsB,sIAAkC,kMAA4C,2KAAoC,+JAAkC,yBAAU,EACnkoC,WAAW,qBAAqB", "names": [], "file": "nls.messages.zh-tw.js"}