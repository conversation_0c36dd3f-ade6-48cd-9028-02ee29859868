{"name": "monaco-editor", "version": "0.52.2", "vscodeRef": "a7d9e2c32d573e29e68975838196722ae9bb0f15", "private": false, "description": "A browser based code editor", "homepage": "https://github.com/microsoft/monaco-editor", "author": "Microsoft Corporation", "license": "MIT", "scripts": {"import-typescript": "ts-node ./build/importTypescript", "playwright-install": "node ./node_modules/playwright/install.js", "playwright-install-deps": "playwright install-deps", "prettier-check": "prettier --check .", "prettier": "prettier --write .", "pretty-quick": "pretty-quick --staged", "simpleserver": "ts-node ./build/simpleserver", "package-for-smoketest-webpack": "ts-node ./test/smoke/package-webpack", "package-for-smoketest-webpack-cross-origin": "ts-node ./test/smoke/package-webpack --cross-origin", "package-for-smoketest-esbuild": "ts-node ./test/smoke/package-esbuild", "package-for-smoketest-vite": "ts-node ./test/smoke/package-vite", "smoketest": "node ./test/smoke/runner.js", "smoketest-debug": "node ./test/smoke/runner.js --debug-tests", "test": "mocha test/unit/all.js && ts-node ./build/check-samples", "deps-all-remove": "ts-node ./build/npm/removeAll", "deps-all-install": "ts-node ./build/npm/installAll", "update-actions": "pin-github-action ./.github/workflows/website.yml", "watch": "tsc -w -p ./src", "build": "ts-node ./build/build-languages", "build-monaco-editor": "npm run build && ts-node ./build/build-monaco-editor"}, "typings": "./esm/vs/editor/editor.api.d.ts", "module": "./esm/vs/editor/editor.main.js", "repository": {"type": "git", "url": "https://github.com/microsoft/monaco-editor"}, "devDependencies": {"@types/mocha": "^9.1.0", "@types/shelljs": "^0.8.11", "@typescript/vfs": "^1.3.5", "chai": "^4.3.6", "clean-css": "^5.2.4", "css-loader": "^6.7.1", "esbuild": "^0.20.0", "esbuild-plugin-alias": "^0.2.1", "file-loader": "^6.2.0", "glob": "^7.2.0", "http-server": "^14.1.1", "husky": "^7.0.4", "jsdom": "^19.0.0", "jsonc-parser": "^3.0.0", "mocha": "^9.2.0", "monaco-editor-core": "0.52.2", "parcel": "^2.7.0", "pin-github-action": "^1.8.0", "playwright": "^1.32.2", "prettier": "^2.5.1", "pretty-quick": "^3.1.3", "requirejs": "^2.3.6", "shelljs": "^0.8.5", "style-loader": "^3.3.1", "terser": "^5.14.2", "ts-node": "^10.6.0", "typescript": "^5.4.5", "vite": "^3.2.8", "vscode-css-languageservice": "6.2.14", "vscode-html-languageservice": "5.2.0", "vscode-json-languageservice": "5.3.11", "vscode-languageserver-textdocument": "^1.0.11", "vscode-languageserver-types": "3.17.5", "vscode-uri": "3.0.8", "webpack": "^5.76.0", "yaserver": "^0.4.0"}, "alias": {"process": false, "buffer": false}, "vscodeCommitId": "a7d9e2c32d573e29e68975838196722ae9bb0f15", "monacoCommitId": "404545bded1df6ffa41ea0af4e8ddb219018c6c1"}