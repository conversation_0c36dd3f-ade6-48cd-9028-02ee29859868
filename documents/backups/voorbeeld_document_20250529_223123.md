# Voorbeeld Document

Dit is een voorbeeld document om de functionaliteit van het Document Schrijver systeem te demonstreren.

## Wat kan dit systeem?

Het Document Schrijver systeem is een krachtige multi-agent applicatie die verschillende AI agents gebruikt voor documentbeheer:

### 1. DocumentWriter Agent
- Schrijft nieuwe uitgebreide documenten
- Gebruikt GROQ DeepSeek-R1 70B model
- Genereert gestructureerde Markdown content

### 2. DocumentRewriter Agent  
- Herschrijft geselecteerde tekst
- Verbetert stijl en leesbaarheid
- Past content aan volgens instructies

### 3. StructureAgent
- Verbetert documentorganisatie
- Optimaliseert koppenstructuur
- Zorgt voor logische flow

## Hoe te gebruiken

1. **Selecteer een agent** in het rechterpaneel
2. **Geef een instructie** voor wat je wilt bereiken
3. **Bekijk de real-time generatie** van content
4. **Selecteer tekst** in de preview om te herschrijven
5. **Sla documenten op** met Ctrl+S

## Technische Details

- **Backend**: Python FastAPI met LangChain
- **Frontend**: React met Monaco Editor
- **AI Model**: GROQ DeepSeek-R1 70B
- **Real-time**: WebSocket communicatie
- **Format**: Markdown met GitHub Flavored Markdown

Dit document kan worden gebruikt om de verschillende functies te testen!
