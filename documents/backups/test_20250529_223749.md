# Technisch Ontwerp – Automatisering Authenticiteitscontrole (AC) bij EVC Academy

## 1. Inleiding

Het authenticiteitscontroleproces (AC) is een kritische stap binnen het EVC-traject van EVC Academy. Het valideert of de door kandidaten aangeleverde bewijsstukken, zoals diploma’s, certificaten en werkervaring, authentiek en betrouwbaar zijn. De huidige uitvoering van dit proces is grote<PERSON><PERSON> handmatig, foutgevoelig en tijdrovend.

Dit document beschrijft een voorstel voor een geautomatiseerde oplossing die het proces efficiënter, scha<PERSON><PERSON><PERSON><PERSON> en betrouwbaarder maakt, met volledige naleving van de AVG.

## 2. Doelstelling

De doelstellingen van de automatisering zijn:

* Het reduceren van handmatige handelingen en doorlooptijd.
* Het verhogen van de betrouwbaarheid en consistentie van de controles.
* Het bieden van realtime inzicht in de status van het AC-proces.
* Het waarborgen van gegevensbescherming en privacy (AVG).
* Het integreren met bestaande systemen zoals Pipedrive.

## 3. <PERSON><PERSON> van de Proof of Concept (PoC)

**In scope:**

* Keuze uit type AC: standaard, verzwaard of maatwerk.
* Automatische verzending van garantstellermails.
* Herinneringsmails bij uitblijvende reactie.
* Logging en statusupdates in Pipedrive.
* Automatische rapportgeneratie.
* Escalatieproces bij ontbrekende of verdachte informatie.

**Buiten scope:**

* Integratie met Remindo.
* Planning en uitvoering van het assessment.

## 4. Architectuur en Componenten

### 4.1 Systeemcomponenten

1. **Inputverwerker:** Verwerkt data vanuit Pipedrive.
2. **Mailer-service:** Verstuurt mails en reminders.
3. **Pipedrive-connector:** Synchroniseert statussen en reacties.
4. **Rapportgenerator:** Bouwt automatische rapportages op.
5. **Beheermodule:** Handmatige controle of interventie door beheerder.

### 4.2 Technische Stack

* Python 3.x voor backend-logica.
* Pipedrive API voor CRM-integratie.
* SMTP of Mailgun voor e-mailverkeer.
* PDFKit of ReportLab voor rapportage.
* Optioneel: Docker voor containerisatie en schaalbaarheid.

## 5. Datamodel

| Entiteit      | Velden                                    |
| ------------- | ----------------------------------------- |
| Kandidaat     | Naam, e-mail, status, garantstellers\[]   |
| Garantsteller | Naam, e-mail, reactie, tijdstempels       |
| AC-status     | Pending, Reminder Sent, Approved, Blocked |
| Logboek       | Actie, tijdstip, kandidaat-ID             |

## 6. Procesflow

1. Portfolio wordt goedgekeurd.
2. Type AC wordt geselecteerd.
3. Garantstellermail wordt verzonden.
4. Reactie wordt gelogd in Pipedrive.
5. Na 7 dagen zonder reactie: automatische reminder.
6. Na 14 dagen zonder reactie: verzoek om vervanging garantsteller.
7. Bij verdachte documenten: escalatie naar QA/MT.
8. Bij goedkeuring: rapport wordt gegenereerd.
9. Kandidaat wordt gemarkeerd als "Gereed voor assessment".

## 7. Beveiliging en AVG

* Toegang alleen voor geautoriseerde gebruikers.
* Alle acties worden gelogd met tijdstip en gebruiker.
* Gegevens worden 1 jaar bewaard (instelbaar).
* Alleen noodzakelijke gegevens worden opgeslagen.
* Geen gegevensdeling met externe partijen.

## 8. Rollen en Verantwoordelijkheden

| Rol                | Verantwoordelijkheden                        |
| ------------------ | -------------------------------------------- |
| Ronald (beheerder) | Procescontrole, logging, escalatiebeheer     |
| QA / MT            | Besluitvorming bij fraude of afwijkingen     |
| Developer          | Bouw en onderhoud van het systeem            |
| Begeleiders        | Indirecte feedback op AC-status en processen |

## 9. Implementatieplanning

| Week | Activiteit                                  |
| ---- | ------------------------------------------- |
| 1–2  | Ontwikkeling flow en rapportage             |
| 3    | Bouwen en testen van Pipedrive-koppeling    |
| 4    | Inrichten van e-mails en logging            |
| 5    | Uitvoeren van pilot                         |
| 6    | Feedback verwerken en voorbereiden livegang |

## 10. Acceptatiecriteria

* 90% van de garantstellers reageert binnen 14 dagen.
* Statusupdates zijn zichtbaar in Pipedrive binnen 1 uur.
* Rapportage is volledig, correct en automatisch.
* Reminders worden automatisch verzonden.
* Logging is volledig en traceerbaar.

## 11. Risico’s en Mitigaties

| Risico                         | Mitigatie                                |
| ------------------------------ | ---------------------------------------- |
| Geen reactie garantsteller     | Reminderflow + escalatie naar kandidaat  |
| Onbereikbare Pipedrive API     | Retry-logica + fallback mailoplossing    |
| AVG-incident                   | Privacycheck, logging, en toegangsbeheer |
| Onvolledige of verdachte input | Handmatige interventie via beheermodule  |

## 12. Evaluatie en Vervolg

Na afloop van de pilot worden de volgende aspecten geëvalueerd:

* Tijdswinst per kandidaattraject
* Aantal geslaagde automatische processen vs. handmatig
* Tevredenheid van QA en Ronald
* Aantal escalaties en afhandelduur

**Mogelijke vervolgstappen:**

* Opschaling naar intakeproces en diploma-validatie
* Koppeling met certificaatvalidatie
* Integratie met Remindo
