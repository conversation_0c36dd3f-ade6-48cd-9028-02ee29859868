version: '3.8'

services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    ports:
      - "8000:8000"
    environment:
      - GROQ_API_KEY=${GROQ_API_KEY}
      - DATABASE_URL=sqlite:///./documents.db
      - FRONTEND_URL=http://localhost:3000
    volumes:
      - ./documents:/app/documents
      - ./backend:/app/backend
    depends_on:
      - db
    restart: unless-stopped

  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    ports:
      - "3000:3000"
    environment:
      - VITE_BACKEND_URL=http://localhost:8000
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    restart: unless-stopped

  db:
    image: sqlite:latest
    volumes:
      - ./data:/data
    restart: unless-stopped

volumes:
  documents:
  data:
