from fastapi import <PERSON><PERSON><PERSON>, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from typing import List
import json
import asyncio
import time

from backend.config import settings
from backend.models.schemas import (
    DocumentRequest, DocumentResponse, RewriteRequest, RewriteResponse,
    AgentTask, AgentResponse, FileInfo
)
from backend.agents.agent_manager import agent_manager
from backend.services.file_service import file_service
from backend.utils.logger import llm_logger

app = FastAPI(title="Document Writer API", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[settings.FRONTEND_URL, "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            await connection.send_text(message)

manager = ConnectionManager()

# API Routes
@app.get("/")
async def root():
    return {"message": "Document Writer API", "version": "1.0.0"}

@app.get("/agents", response_model=dict)
async def list_agents():
    """List all available agents"""
    return agent_manager.list_agents()

@app.post("/documents/create", response_model=AgentResponse)
async def create_document(request: DocumentRequest):
    """Create a new document using specified agent"""
    try:
        result = await agent_manager.process_task(
            agent_type=request.agent_type,
            instruction=request.task_description,
            context=request.content
        )

        # Save document if successful
        if result["status"] == "completed":
            filename = file_service.get_safe_filename(request.title)
            await file_service.write_file(filename, result["result"])
            result["metadata"]["filename"] = filename

        return AgentResponse(**result)

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/documents/rewrite", response_model=AgentResponse)
async def rewrite_text(request: RewriteRequest):
    """Rewrite selected text"""
    try:
        result = await agent_manager.process_task(
            agent_type="rewriter",
            instruction=request.instruction,
            context=request.selected_text
        )

        return AgentResponse(**result)

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/files")
async def list_files_and_folders(folder_path: str = ""):
    """List files and folders in a directory"""
    try:
        return await file_service.list_files_and_folders(folder_path)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/folders")
async def create_folder(folder_data: dict):
    """Create a new folder"""
    try:
        folder_path = folder_data.get("folder_path", "")
        folder_name = folder_data.get("folder_name", "")

        if not folder_name:
            raise HTTPException(status_code=400, detail="Folder name is required")

        return await file_service.create_folder(folder_path, folder_name)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/folders/{folder_path:path}")
async def delete_folder(folder_path: str, permanent: bool = False):
    """Delete a folder"""
    try:
        success = await file_service.delete_folder(folder_path, not permanent)
        if not success:
            raise HTTPException(status_code=404, detail="Folder not found")
        return {"message": "Folder deleted"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/search")
async def search_files(query: str, folder_path: str = ""):
    """Search for files and folders"""
    try:
        if not query or len(query) < 2:
            raise HTTPException(status_code=400, detail="Query must be at least 2 characters")

        return await file_service.search_files(query, folder_path)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/stats")
async def get_file_stats():
    """Get file and folder statistics"""
    try:
        return await file_service.get_file_stats()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/files/{file_path:path}")
async def get_file_content(file_path: str):
    """Get content of a specific file"""
    try:
        content = await file_service.read_file(file_path)
        return {"file_path": file_path, "content": content}
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="File not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/files/{file_path:path}")
async def save_file_content(file_path: str, content: dict):
    """Save content to a file"""
    try:
        saved_path = await file_service.write_file(file_path, content["content"])
        return {"message": "File saved", "path": saved_path}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/files/{file_path:path}")
async def delete_file(file_path: str, permanent: bool = False):
    """Delete a file"""
    try:
        success = await file_service.delete_file(file_path, not permanent)
        if not success:
            raise HTTPException(status_code=404, detail="File not found")
        return {"message": "File deleted"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.put("/files/{file_path:path}/rename")
async def rename_file(file_path: str, rename_data: dict):
    """Rename a file or folder"""
    try:
        new_name = rename_data.get("new_name", "")
        if not new_name:
            raise HTTPException(status_code=400, detail="New name is required")

        return await file_service.rename_item(file_path, new_name)
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="File not found")
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/settings")
async def get_settings():
    """Get current settings"""
    try:
        return {
            "groq_api_key": "***" if settings.GROQ_API_KEY else "",
            "root_folder": settings.ROOT_FOLDER,
            "model_name": settings.MODEL_NAME,
            "max_tokens": settings.MAX_TOKENS,
            "temperature": settings.TEMPERATURE,
            "auto_save_interval": settings.get_setting("auto_save_interval", 30),
            "theme": settings.get_setting("theme", "dark"),
            "language": settings.get_setting("language", "nl"),
            "enable_ai_suggestions": settings.get_setting("enable_ai_suggestions", True),
            "enable_auto_backup": settings.get_setting("enable_auto_backup", True),
            "backup_interval": settings.get_setting("backup_interval", 300),
            "max_backups": settings.get_setting("max_backups", 10),
            "shortcuts": settings.get_setting("shortcuts", {})
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/settings")
async def update_settings(settings_data: dict):
    """Update settings"""
    try:
        for key, value in settings_data.items():
            if key == "groq_api_key" and value and value != "***":
                settings.update_setting("groq_api_key", value)
            elif key in ["root_folder", "model_name", "max_tokens", "temperature",
                        "auto_save_interval", "theme", "language", "enable_ai_suggestions",
                        "enable_auto_backup", "backup_interval", "max_backups", "shortcuts"]:
                settings.update_setting(key, value)

                # Refresh file service paths if root_folder changed
                if key == "root_folder":
                    file_service.refresh_paths()

        return {"message": "Settings updated successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/logs/stats")
async def get_log_stats():
    """Get logging statistics"""
    try:
        return llm_logger.get_log_stats()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/logs/{log_type}")
async def get_logs(log_type: str, lines: int = 100):
    """Get recent log entries"""
    try:
        import os
        from pathlib import Path

        log_dir = Path("logs")
        log_file = log_dir / f"{log_type}.log"

        if not log_file.exists():
            raise HTTPException(status_code=404, detail=f"Log file {log_type} not found")

        # Read last N lines
        with open(log_file, 'r', encoding='utf-8') as f:
            all_lines = f.readlines()
            recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines

        return {
            "log_type": log_type,
            "total_lines": len(all_lines),
            "returned_lines": len(recent_lines),
            "lines": [line.strip() for line in recent_lines]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# WebSocket endpoint for streaming
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    client_id = f"client_{int(time.time() * 1000)}"
    await manager.connect(websocket)

    # Log WebSocket connection
    llm_logger.log_websocket_message(
        direction="CONNECTION",
        message_type="CONNECT",
        content={"client_id": client_id, "timestamp": time.time()},
        client_id=client_id
    )

    try:
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)

            # Log incoming WebSocket message
            llm_logger.log_websocket_message(
                direction="INCOMING",
                message_type=message.get("type", "unknown"),
                content=message,
                client_id=client_id
            )

            if message["type"] == "generate":
                # Handle single generation request
                start_response = {
                    "type": "stream",
                    "content": "",
                    "task_id": message.get("task_id", "")
                }

                await websocket.send_text(json.dumps(start_response))
                llm_logger.log_websocket_message(
                    direction="OUTGOING",
                    message_type="stream_start",
                    content=start_response,
                    client_id=client_id
                )

                try:
                    async for chunk in agent_manager.process_task_stream(
                        agent_type=message.get("agent", "writer"),
                        instruction=message.get("instruction", ""),
                        context=message.get("context", "")
                    ):
                        chunk_response = {
                            "type": "stream",
                            "content": chunk,
                            "task_id": message.get("task_id", "")
                        }

                        await websocket.send_text(json.dumps(chunk_response))

                    # Send completion message
                    complete_response = {
                        "type": "complete",
                        "content": "",
                        "task_id": message.get("task_id", ""),
                        "replace_document": message.get("replace_document", False)
                    }

                    await websocket.send_text(json.dumps(complete_response))
                    llm_logger.log_websocket_message(
                        direction="OUTGOING",
                        message_type="complete",
                        content=complete_response,
                        client_id=client_id
                    )

                except Exception as e:
                    error_response = {
                        "type": "error",
                        "message": str(e),
                        "task_id": message.get("task_id", "")
                    }

                    await websocket.send_text(json.dumps(error_response))
                    llm_logger.log_websocket_message(
                        direction="OUTGOING",
                        message_type="error",
                        content=error_response,
                        client_id=client_id
                    )

                    # Log error details
                    llm_logger.log_error(
                        error_type="WEBSOCKET_GENERATION_ERROR",
                        error_message=str(e),
                        context={
                            "client_id": client_id,
                            "message": message,
                            "agent_type": message.get("agent", "unknown")
                        }
                    )

    except WebSocketDisconnect:
        manager.disconnect(websocket)

        # Log WebSocket disconnection
        llm_logger.log_websocket_message(
            direction="CONNECTION",
            message_type="DISCONNECT",
            content={"client_id": client_id, "timestamp": time.time()},
            client_id=client_id
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
