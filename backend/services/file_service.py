import os
import shutil
import aiofiles
from pathlib import Path
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
import mimetypes
import hashlib
import json
from backend.config import settings
from backend.models.schemas import FileInfo

class FileService:
    def __init__(self):
        self._update_paths()

    def _update_paths(self):
        """Update paths based on current settings"""
        self.root_dir = Path(settings.ROOT_FOLDER)
        self.templates_dir = self.root_dir / "templates"
        self.backups_dir = self.root_dir / "backups"
        self.trash_dir = self.root_dir / ".trash"
        self._ensure_directories()

    def _ensure_directories(self):
        """Ensure all required directories exist"""
        for directory in [self.root_dir, self.templates_dir, self.backups_dir, self.trash_dir]:
            directory.mkdir(parents=True, exist_ok=True)

    def refresh_paths(self):
        """Refresh paths when settings change"""
        self._update_paths()

    async def list_files_and_folders(self, folder_path: str = "") -> Dict[str, Any]:
        """List files and folders in a directory with tree structure"""
        current_path = self.root_dir / folder_path if folder_path else self.root_dir

        if not current_path.exists() or not current_path.is_dir():
            return {"files": [], "folders": [], "path": folder_path}

        files = []
        folders = []

        try:
            for item in current_path.iterdir():
                if item.name.startswith('.') and item.name != '.trash':
                    continue

                if item.is_dir():
                    folder_info = {
                        "name": item.name,
                        "path": str(item.relative_to(self.root_dir)),
                        "type": "folder",
                        "created_at": datetime.fromtimestamp(item.stat().st_ctime),
                        "modified_at": datetime.fromtimestamp(item.stat().st_mtime),
                        "item_count": len(list(item.iterdir())) if item.exists() else 0
                    }
                    folders.append(folder_info)

                elif item.is_file() and item.suffix.lower() in ['.md', '.txt', '.json']:
                    stat = item.stat()
                    preview = await self._read_file_preview(str(item))

                    file_info = {
                        "filename": item.name,
                        "path": str(item.relative_to(self.root_dir)),
                        "type": "file",
                        "size": stat.st_size,
                        "created_at": datetime.fromtimestamp(stat.st_ctime),
                        "modified_at": datetime.fromtimestamp(stat.st_mtime),
                        "content_preview": preview,
                        "extension": item.suffix.lower(),
                        "mime_type": mimetypes.guess_type(str(item))[0] or "text/plain"
                    }
                    files.append(file_info)

        except PermissionError:
            pass

        # Sort folders and files
        folders.sort(key=lambda x: x["name"].lower())
        files.sort(key=lambda x: x["modified_at"], reverse=True)

        return {
            "files": files,
            "folders": folders,
            "path": folder_path,
            "total_files": len(files),
            "total_folders": len(folders)
        }

    async def create_folder(self, folder_path: str, folder_name: str) -> Dict[str, Any]:
        """Create a new folder"""
        parent_path = self.root_dir / folder_path if folder_path else self.root_dir
        new_folder_path = parent_path / folder_name

        if new_folder_path.exists():
            raise ValueError(f"Folder '{folder_name}' already exists")

        new_folder_path.mkdir(parents=True, exist_ok=True)

        return {
            "name": folder_name,
            "path": str(new_folder_path.relative_to(self.root_dir)),
            "type": "folder",
            "created_at": datetime.now(),
            "modified_at": datetime.now(),
            "item_count": 0
        }

    async def read_file(self, file_path: str) -> str:
        """Read content of a file"""
        full_path = self.root_dir / file_path

        if not full_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        if not full_path.is_file():
            raise ValueError(f"Path is not a file: {file_path}")

        async with aiofiles.open(full_path, 'r', encoding='utf-8') as f:
            return await f.read()

    async def write_file(self, file_path: str, content: str, create_backup: bool = True) -> str:
        """Write content to a file"""
        full_path = self.root_dir / file_path

        # Create backup if file exists and backup is enabled
        if create_backup and full_path.exists() and settings.get_setting("enable_auto_backup", True):
            await self._create_backup(full_path)

        # Ensure parent directory exists
        full_path.parent.mkdir(parents=True, exist_ok=True)

        async with aiofiles.open(full_path, 'w', encoding='utf-8') as f:
            await f.write(content)

        return str(full_path.relative_to(self.root_dir))

    async def delete_file(self, file_path: str, move_to_trash: bool = True) -> bool:
        """Delete a file (move to trash or permanent delete)"""
        full_path = self.root_dir / file_path

        if not full_path.exists():
            return False

        if move_to_trash:
            # Move to trash
            trash_path = self.trash_dir / f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{full_path.name}"
            shutil.move(str(full_path), str(trash_path))
        else:
            # Permanent delete
            if full_path.is_file():
                full_path.unlink()
            elif full_path.is_dir():
                shutil.rmtree(str(full_path))

        return True

    async def delete_folder(self, folder_path: str, move_to_trash: bool = True) -> bool:
        """Delete a folder and its contents"""
        return await self.delete_file(folder_path, move_to_trash)

    async def rename_item(self, old_path: str, new_name: str) -> Dict[str, Any]:
        """Rename a file or folder"""
        old_full_path = self.root_dir / old_path
        new_full_path = old_full_path.parent / new_name

        if not old_full_path.exists():
            raise FileNotFoundError(f"Item not found: {old_path}")

        if new_full_path.exists():
            raise ValueError(f"Item with name '{new_name}' already exists")

        old_full_path.rename(new_full_path)

        stat = new_full_path.stat()
        return {
            "name": new_name,
            "path": str(new_full_path.relative_to(self.root_dir)),
            "type": "folder" if new_full_path.is_dir() else "file",
            "modified_at": datetime.fromtimestamp(stat.st_mtime)
        }

    async def _create_backup(self, file_path: Path):
        """Create a backup of a file"""
        if not file_path.exists():
            return

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f"{file_path.stem}_{timestamp}{file_path.suffix}"
        backup_path = self.backups_dir / backup_name

        shutil.copy2(str(file_path), str(backup_path))

        # Clean old backups
        await self._cleanup_old_backups()

    async def _cleanup_old_backups(self):
        """Remove old backups beyond the limit"""
        max_backups = settings.get_setting("max_backups", 10)

        if not self.backups_dir.exists():
            return

        backups = list(self.backups_dir.glob("*"))
        backups.sort(key=lambda x: x.stat().st_mtime, reverse=True)

        for backup in backups[max_backups:]:
            backup.unlink()

    async def _read_file_preview(self, file_path: str, max_chars: int = 200) -> str:
        """Read first few characters of file for preview"""
        try:
            async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                content = await f.read(max_chars)
                if len(content) == max_chars:
                    content += "..."
                return content
        except Exception:
            return "Preview not available"

    def get_safe_filename(self, title: str, extension: str = ".md") -> str:
        """Convert title to safe filename"""
        # Remove special characters and replace spaces with underscores
        safe_name = "".join(c for c in title if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_name = safe_name.replace(' ', '_')

        # Limit length
        if len(safe_name) > 50:
            safe_name = safe_name[:50]

        # Add timestamp if name is empty or too short
        if len(safe_name) < 3:
            safe_name = f"document_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        return safe_name + extension

    async def search_files(self, query: str, folder_path: str = "") -> List[Dict[str, Any]]:
        """Search for files and folders by name or content"""
        results = []
        search_path = self.root_dir / folder_path if folder_path else self.root_dir

        if not search_path.exists():
            return results

        query_lower = query.lower()

        for item in search_path.rglob("*"):
            if item.name.startswith('.'):
                continue

            # Search by filename
            if query_lower in item.name.lower():
                item_info = {
                    "name": item.name,
                    "path": str(item.relative_to(self.root_dir)),
                    "type": "folder" if item.is_dir() else "file",
                    "match_type": "filename"
                }
                results.append(item_info)

            # Search by content for text files
            elif item.is_file() and item.suffix.lower() in ['.md', '.txt']:
                try:
                    async with aiofiles.open(item, 'r', encoding='utf-8') as f:
                        content = await f.read()
                        if query_lower in content.lower():
                            item_info = {
                                "name": item.name,
                                "path": str(item.relative_to(self.root_dir)),
                                "type": "file",
                                "match_type": "content",
                                "preview": self._extract_search_preview(content, query, 100)
                            }
                            results.append(item_info)
                except Exception:
                    continue

        return results[:50]  # Limit results

    def _extract_search_preview(self, content: str, query: str, context_chars: int = 100) -> str:
        """Extract preview around search match"""
        query_lower = query.lower()
        content_lower = content.lower()

        match_index = content_lower.find(query_lower)
        if match_index == -1:
            return content[:context_chars] + "..."

        start = max(0, match_index - context_chars // 2)
        end = min(len(content), match_index + len(query) + context_chars // 2)

        preview = content[start:end]
        if start > 0:
            preview = "..." + preview
        if end < len(content):
            preview = preview + "..."

        return preview

    async def get_file_stats(self) -> Dict[str, Any]:
        """Get statistics about files and folders"""
        total_files = 0
        total_folders = 0
        total_size = 0
        file_types = {}

        for item in self.root_dir.rglob("*"):
            if item.name.startswith('.'):
                continue

            if item.is_file():
                total_files += 1
                total_size += item.stat().st_size
                ext = item.suffix.lower() or "no_extension"
                file_types[ext] = file_types.get(ext, 0) + 1
            elif item.is_dir():
                total_folders += 1

        return {
            "total_files": total_files,
            "total_folders": total_folders,
            "total_size": total_size,
            "file_types": file_types,
            "root_folder": str(self.root_dir)
        }

# Global instance
file_service = FileService()
