import re
import time
import traceback
from groq import Groq
from backend.config import settings
from backend.utils.logger import llm_logger

class GroqService:
    def __init__(self):
        self.client = Groq(api_key=settings.GROQ_API_KEY)
        self.model = settings.MODEL_NAME

    def _filter_think_tags(self, text: str) -> str:
        """
        Filter out <think> tags from DeepSeek-R1 output
        DeepSeek-R1 sometimes includes reasoning in <think> tags that should be removed
        """
        # Remove <think>...</think> blocks
        pattern = r'<think>.*?</think>'
        cleaned_text = re.sub(pattern, '', text, flags=re.DOTALL)

        # Clean up extra whitespace
        cleaned_text = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_text)
        cleaned_text = cleaned_text.strip()

        return cleaned_text

    def _truncate_to_context_window(self, text: str, max_chars: int = 28000) -> str:
        """
        Truncate text to fit within context window
        DeepSeek-R1 has ~32k token limit, roughly 28k characters to be safe
        """
        if len(text) <= max_chars:
            return text

        # Try to truncate at sentence boundary
        truncated = text[:max_chars]
        last_sentence = truncated.rfind('.')
        if last_sentence > max_chars * 0.8:  # If we can keep 80% and end at sentence
            return truncated[:last_sentence + 1]

        return truncated + "..."

    async def generate_text(self, prompt: str, system_prompt: str = None, agent_type: str = "unknown") -> str:
        """Generate text using GROQ DeepSeek-R1 with uitgebreide logging"""
        start_time = time.time()
        request_id = None

        try:
            # Truncate prompt if too long
            original_prompt = prompt
            prompt = self._truncate_to_context_window(prompt)

            # Log truncation if it occurred
            if len(prompt) != len(original_prompt):
                llm_logger.log_error(
                    "PROMPT_TRUNCATED",
                    f"Prompt truncated from {len(original_prompt)} to {len(prompt)} characters",
                    {"agent_type": agent_type, "original_length": len(original_prompt), "truncated_length": len(prompt)}
                )

            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})

            # Log request
            request_settings = {
                "model": self.model,
                "max_tokens": settings.MAX_TOKENS,
                "temperature": settings.TEMPERATURE,
                "stream": False
            }

            request_id = llm_logger.log_llm_request(
                agent_type=agent_type,
                model=self.model,
                system_prompt=system_prompt,
                user_prompt=prompt,
                settings=request_settings
            )

            # Make API call
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=settings.MAX_TOKENS,
                temperature=settings.TEMPERATURE,
                stream=False
            )

            result = response.choices[0].message.content
            processing_time = time.time() - start_time

            # Log raw response
            llm_logger.log_llm_response(
                request_id=request_id,
                response_content=result,
                response_metadata={
                    "finish_reason": response.choices[0].finish_reason,
                    "usage": response.usage.model_dump() if response.usage else None,
                    "model": response.model,
                    "raw_response_length": len(result)
                },
                processing_time=processing_time
            )

            # Filter out think tags
            filtered_result = self._filter_think_tags(result)

            # Log filtering if content changed
            if len(filtered_result) != len(result):
                llm_logger.llm_logger.info(f"THINK_TAGS_FILTERED | Request: {request_id} | Original: {len(result)} chars | Filtered: {len(filtered_result)} chars")

            return filtered_result

        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = str(e)
            stack_trace = traceback.format_exc()

            # Log error
            if request_id:
                llm_logger.log_llm_response(
                    request_id=request_id,
                    response_content=None,
                    processing_time=processing_time,
                    error=error_msg
                )

            llm_logger.log_error(
                error_type="GROQ_API_ERROR",
                error_message=error_msg,
                context={
                    "agent_type": agent_type,
                    "model": self.model,
                    "prompt_length": len(prompt) if 'prompt' in locals() else 0,
                    "processing_time": processing_time
                },
                stack_trace=stack_trace
            )

            raise Exception(f"GROQ API error: {error_msg}")

    async def generate_stream(self, prompt: str, system_prompt: str = None, agent_type: str = "unknown"):
        """Generate streaming text using GROQ DeepSeek-R1 with uitgebreide logging"""
        start_time = time.time()
        request_id = None
        chunk_index = 0
        accumulated_text = ""

        try:
            # Truncate prompt if too long
            original_prompt = prompt
            prompt = self._truncate_to_context_window(prompt)

            # Log truncation if it occurred
            if len(prompt) != len(original_prompt):
                llm_logger.log_error(
                    "PROMPT_TRUNCATED",
                    f"Streaming prompt truncated from {len(original_prompt)} to {len(prompt)} characters",
                    {"agent_type": agent_type, "original_length": len(original_prompt), "truncated_length": len(prompt)}
                )

            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})

            # Log request
            request_settings = {
                "model": self.model,
                "max_tokens": settings.MAX_TOKENS,
                "temperature": settings.TEMPERATURE,
                "stream": True
            }

            request_id = llm_logger.log_llm_request(
                agent_type=agent_type,
                model=self.model,
                system_prompt=system_prompt,
                user_prompt=prompt,
                settings=request_settings
            )

            # Make streaming API call
            stream = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=settings.MAX_TOKENS,
                temperature=settings.TEMPERATURE,
                stream=True
            )

            for chunk in stream:
                if chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    accumulated_text += content
                    chunk_index += 1

                    # Log chunk (alleen elke 10e chunk om logs beheersbaar te houden)
                    if chunk_index % 10 == 0:
                        llm_logger.log_llm_stream_chunk(
                            request_id=request_id,
                            chunk_content=content,
                            chunk_index=chunk_index,
                            accumulated_content=accumulated_text
                        )

                    yield content

            # Log final response
            processing_time = time.time() - start_time
            final_result = self._filter_think_tags(accumulated_text)

            llm_logger.log_llm_response(
                request_id=request_id,
                response_content=final_result,
                response_metadata={
                    "total_chunks": chunk_index,
                    "raw_response_length": len(accumulated_text),
                    "filtered_response_length": len(final_result),
                    "stream": True
                },
                processing_time=processing_time
            )

        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = str(e)
            stack_trace = traceback.format_exc()

            # Log error
            if request_id:
                llm_logger.log_llm_response(
                    request_id=request_id,
                    response_content=None,
                    processing_time=processing_time,
                    error=error_msg
                )

            llm_logger.log_error(
                error_type="GROQ_STREAMING_ERROR",
                error_message=error_msg,
                context={
                    "agent_type": agent_type,
                    "model": self.model,
                    "prompt_length": len(prompt) if 'prompt' in locals() else 0,
                    "chunks_processed": chunk_index,
                    "processing_time": processing_time
                },
                stack_trace=stack_trace
            )

            raise Exception(f"GROQ streaming error: {error_msg}")

# Global instance
groq_service = GroqService()
