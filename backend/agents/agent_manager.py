from typing import Dict, Any, Optional
from .document_writer import DocumentWriterAgent
from .document_rewriter import DocumentRewriterAgent
from .structure_agent import StructureAgent
from backend.utils.logger import llm_logger

class AgentManager:
    def __init__(self):
        self.agents = {
            "writer": DocumentWriterAgent(),
            "rewriter": DocumentRewriterAgent(),
            "structure": StructureAgent()
        }

    def get_agent(self, agent_type: str):
        """Get agent by type"""
        if agent_type not in self.agents:
            raise ValueError(f"Unknown agent type: {agent_type}")
        return self.agents[agent_type]

    def list_agents(self) -> Dict[str, str]:
        """List all available agents"""
        return {
            agent_type: agent.description
            for agent_type, agent in self.agents.items()
        }

    async def process_task(self, agent_type: str, instruction: str, context: Optional[str] = None) -> Dict[str, Any]:
        """Process task with specified agent"""
        # Log agent task start
        llm_logger.llm_logger.info(f"AGENT_TASK_START | Agent: {agent_type} | Instruction: {instruction[:100]}...")

        agent = self.get_agent(agent_type)
        result = await agent.process(instruction, context)

        # Log agent task completion
        llm_logger.llm_logger.info(f"AGENT_TASK_COMPLETE | Agent: {agent_type} | Success: {result.get('success', False)}")

        return result

    async def process_task_stream(self, agent_type: str, instruction: str, context: Optional[str] = None):
        """Process task with streaming response"""
        # Log streaming task start
        llm_logger.llm_logger.info(f"AGENT_STREAM_START | Agent: {agent_type} | Instruction: {instruction[:100]}...")

        agent = self.get_agent(agent_type)
        chunk_count = 0

        try:
            async for chunk in agent.process_stream(instruction, context):
                chunk_count += 1
                yield chunk

            # Log streaming completion
            llm_logger.llm_logger.info(f"AGENT_STREAM_COMPLETE | Agent: {agent_type} | Chunks: {chunk_count}")

        except Exception as e:
            # Log streaming error
            llm_logger.log_error(
                error_type="AGENT_STREAM_ERROR",
                error_message=str(e),
                context={
                    "agent_type": agent_type,
                    "instruction": instruction[:200],
                    "chunks_processed": chunk_count
                }
            )
            raise

# Global instance
agent_manager = AgentManager()
