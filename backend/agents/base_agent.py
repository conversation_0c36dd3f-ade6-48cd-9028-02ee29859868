from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from backend.services.groq_service import groq_service
import time
import uuid

class BaseAgent(ABC):
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self.groq = groq_service

    @abstractmethod
    def get_system_prompt(self) -> str:
        """Return the system prompt for this agent"""
        pass

    @abstractmethod
    async def process(self, instruction: str, context: Optional[str] = None) -> Dict[str, Any]:
        """Process the instruction and return result"""
        pass

    def _create_task_id(self) -> str:
        """Generate unique task ID"""
        return str(uuid.uuid4())

    async def _generate_response(self, prompt: str, system_prompt: str = None) -> str:
        """Generate response using GROQ service"""
        if not system_prompt:
            system_prompt = self.get_system_prompt()

        return await self.groq.generate_text(prompt, system_prompt, agent_type=self.name)

    async def _generate_stream_response(self, prompt: str, system_prompt: str = None):
        """Generate streaming response using GROQ service"""
        if not system_prompt:
            system_prompt = self.get_system_prompt()

        async for chunk in self.groq.generate_stream(prompt, system_prompt, agent_type=self.name):
            yield chunk

    def _format_context(self, context: str, max_length: int = 5000) -> str:
        """Format and truncate context for inclusion in prompts"""
        if not context:
            return ""

        if len(context) > max_length:
            context = context[:max_length] + "..."

        return f"\n\nContext:\n{context}\n"
