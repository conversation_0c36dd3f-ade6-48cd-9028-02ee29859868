import logging
import json
import os
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional
from logging.handlers import RotatingFileHandler
import threading

class LLMLogger:
    """
    Uitgebreid logging systeem voor LLM communicatie met FIFO rotatie
    Max 100MB totaal, gedetailleerde logging van alle LLM interacties
    """
    
    def __init__(self, log_dir: str = "logs", max_total_size_mb: int = 100):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        self.max_total_size = max_total_size_mb * 1024 * 1024  # Convert to bytes
        self.max_file_size = 10 * 1024 * 1024  # 10MB per file
        self.backup_count = max_total_size_mb // 10  # Number of backup files
        
        self._setup_loggers()
        self._lock = threading.Lock()
        
    def _setup_loggers(self):
        """Setup verschillende loggers voor verschillende aspecten"""
        
        # LLM Request/Response Logger
        self.llm_logger = logging.getLogger("llm_communication")
        self.llm_logger.setLevel(logging.DEBUG)
        
        llm_handler = RotatingFileHandler(
            self.log_dir / "llm_communication.log",
            maxBytes=self.max_file_size,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        llm_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        llm_handler.setFormatter(llm_formatter)
        self.llm_logger.addHandler(llm_handler)
        
        # WebSocket Logger
        self.ws_logger = logging.getLogger("websocket_communication")
        self.ws_logger.setLevel(logging.DEBUG)
        
        ws_handler = RotatingFileHandler(
            self.log_dir / "websocket_communication.log",
            maxBytes=self.max_file_size,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        ws_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        ws_handler.setFormatter(ws_formatter)
        self.ws_logger.addHandler(ws_handler)
        
        # API Logger
        self.api_logger = logging.getLogger("api_requests")
        self.api_logger.setLevel(logging.DEBUG)
        
        api_handler = RotatingFileHandler(
            self.log_dir / "api_requests.log",
            maxBytes=self.max_file_size,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        api_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        api_handler.setFormatter(api_formatter)
        self.api_logger.addHandler(api_handler)
        
        # Error Logger
        self.error_logger = logging.getLogger("errors")
        self.error_logger.setLevel(logging.ERROR)
        
        error_handler = RotatingFileHandler(
            self.log_dir / "errors.log",
            maxBytes=self.max_file_size,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        error_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)s | %(filename)s:%(lineno)d | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        error_handler.setFormatter(error_formatter)
        self.error_logger.addHandler(error_handler)
        
    def _cleanup_old_logs(self):
        """Cleanup oude logs als totale grootte > max_total_size"""
        with self._lock:
            total_size = 0
            log_files = []
            
            # Verzamel alle log bestanden met hun timestamps
            for log_file in self.log_dir.glob("*.log*"):
                if log_file.is_file():
                    stat = log_file.stat()
                    total_size += stat.st_size
                    log_files.append((log_file, stat.st_mtime, stat.st_size))
            
            # Sorteer op modification time (oudste eerst)
            log_files.sort(key=lambda x: x[1])
            
            # Verwijder oudste bestanden tot we onder de limiet zijn
            while total_size > self.max_total_size and log_files:
                oldest_file, _, file_size = log_files.pop(0)
                try:
                    oldest_file.unlink()
                    total_size -= file_size
                    print(f"Removed old log file: {oldest_file}")
                except Exception as e:
                    print(f"Error removing log file {oldest_file}: {e}")
    
    def log_llm_request(self, 
                       agent_type: str,
                       model: str,
                       system_prompt: Optional[str],
                       user_prompt: str,
                       settings: Dict[str, Any],
                       request_id: str = None):
        """Log LLM request details"""
        
        request_id = request_id or f"req_{int(time.time() * 1000)}"
        
        log_data = {
            "type": "LLM_REQUEST",
            "request_id": request_id,
            "timestamp": datetime.now().isoformat(),
            "agent_type": agent_type,
            "model": model,
            "settings": settings,
            "system_prompt": system_prompt,
            "user_prompt": user_prompt,
            "prompt_length": len(user_prompt),
            "system_prompt_length": len(system_prompt) if system_prompt else 0
        }
        
        self.llm_logger.info(f"REQUEST | {json.dumps(log_data, ensure_ascii=False, indent=2)}")
        self._cleanup_old_logs()
        
        return request_id
    
    def log_llm_response(self,
                        request_id: str,
                        response_content: str,
                        response_metadata: Dict[str, Any] = None,
                        processing_time: float = None,
                        error: str = None):
        """Log LLM response details"""
        
        log_data = {
            "type": "LLM_RESPONSE",
            "request_id": request_id,
            "timestamp": datetime.now().isoformat(),
            "response_content": response_content,
            "response_length": len(response_content) if response_content else 0,
            "processing_time_seconds": processing_time,
            "metadata": response_metadata or {},
            "error": error,
            "success": error is None
        }
        
        if error:
            self.llm_logger.error(f"RESPONSE_ERROR | {json.dumps(log_data, ensure_ascii=False, indent=2)}")
            self.error_logger.error(f"LLM Error for request {request_id}: {error}")
        else:
            self.llm_logger.info(f"RESPONSE | {json.dumps(log_data, ensure_ascii=False, indent=2)}")
    
    def log_llm_stream_chunk(self,
                            request_id: str,
                            chunk_content: str,
                            chunk_index: int,
                            accumulated_content: str = None):
        """Log streaming chunk"""
        
        log_data = {
            "type": "LLM_STREAM_CHUNK",
            "request_id": request_id,
            "timestamp": datetime.now().isoformat(),
            "chunk_index": chunk_index,
            "chunk_content": chunk_content,
            "chunk_length": len(chunk_content),
            "accumulated_length": len(accumulated_content) if accumulated_content else 0
        }
        
        self.llm_logger.debug(f"STREAM_CHUNK | {json.dumps(log_data, ensure_ascii=False)}")
    
    def log_websocket_message(self,
                             direction: str,  # "INCOMING" or "OUTGOING"
                             message_type: str,
                             content: Dict[str, Any],
                             client_id: str = None):
        """Log WebSocket berichten"""
        
        log_data = {
            "type": "WEBSOCKET_MESSAGE",
            "direction": direction,
            "message_type": message_type,
            "timestamp": datetime.now().isoformat(),
            "client_id": client_id,
            "content": content,
            "content_size": len(json.dumps(content))
        }
        
        self.ws_logger.info(f"{direction} | {json.dumps(log_data, ensure_ascii=False, indent=2)}")
    
    def log_api_request(self,
                       method: str,
                       endpoint: str,
                       headers: Dict[str, str] = None,
                       body: Any = None,
                       response_status: int = None,
                       response_body: Any = None,
                       processing_time: float = None):
        """Log API requests en responses"""
        
        log_data = {
            "type": "API_REQUEST",
            "timestamp": datetime.now().isoformat(),
            "method": method,
            "endpoint": endpoint,
            "headers": headers or {},
            "request_body": body,
            "response_status": response_status,
            "response_body": response_body,
            "processing_time_seconds": processing_time
        }
        
        self.api_logger.info(f"API | {json.dumps(log_data, ensure_ascii=False, indent=2)}")
    
    def log_error(self,
                 error_type: str,
                 error_message: str,
                 context: Dict[str, Any] = None,
                 stack_trace: str = None):
        """Log errors met context"""
        
        log_data = {
            "type": "ERROR",
            "timestamp": datetime.now().isoformat(),
            "error_type": error_type,
            "error_message": error_message,
            "context": context or {},
            "stack_trace": stack_trace
        }
        
        self.error_logger.error(f"ERROR | {json.dumps(log_data, ensure_ascii=False, indent=2)}")
    
    def get_log_stats(self) -> Dict[str, Any]:
        """Get logging statistieken"""
        stats = {
            "log_directory": str(self.log_dir),
            "max_total_size_mb": self.max_total_size / (1024 * 1024),
            "current_total_size_mb": 0,
            "files": []
        }
        
        total_size = 0
        for log_file in self.log_dir.glob("*.log*"):
            if log_file.is_file():
                file_size = log_file.stat().st_size
                total_size += file_size
                stats["files"].append({
                    "name": log_file.name,
                    "size_mb": file_size / (1024 * 1024),
                    "modified": datetime.fromtimestamp(log_file.stat().st_mtime).isoformat()
                })
        
        stats["current_total_size_mb"] = total_size / (1024 * 1024)
        stats["files"].sort(key=lambda x: x["modified"], reverse=True)
        
        return stats

# Global logger instance
llm_logger = LLMLogger()
