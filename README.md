# Document Schrijver - Multi-Agent Document Writing System

Een geavanceerd multi-agent systeem voor het schrijven en herschrij<PERSON> van documenten, g<PERSON><PERSON><PERSON>d met <PERSON><PERSON><PERSON><PERSON> en GROQ DeepSeek-R1.

## Functies

- **Multi-Agent Systeem**: Verschillende gespecialiseerde AI agents voor verschillende taken
  - **DocumentWriter**: Schrijft nieuwe uitgebreide documenten
  - **DocumentRewriter**: Herschri<PERSON>ft geselecteerde tekst
  - **StructureAgent**: Verbetert documentstructuur
- **File Explorer**: Beheer je Markdown bestanden
- **Document Canvas**: Split-view editor met live preview
- **Text Selection**: Selecteer tekst in de preview en laat het herschrijven
- **Real-time Streaming**: Live generatie van content via WebSockets
- **GROQ DeepSeek-R1**: Krachtige AI met optimalisaties voor context window

## Technologie Stack

### Backend
- **FastAPI**: Modern Python web framework
- **<PERSON><PERSON>hain**: AI agent framework
- **GROQ**: DeepSeek-R1 70B model
- **WebSockets**: Real-time communicatie
- **SQLite**: Lokale database

### Frontend
- **React**: UI framework
- **Vite**: Build tool
- **Monaco Editor**: Code editor (VS Code editor)
- **React Markdown**: Markdown rendering
- **React Resizable Panels**: Resizable layout

## Installatie

### Vereisten
- Python 3.11+
- Node.js 18+
- GROQ API key

### Backend Setup

1. **Installeer Python dependencies:**
```bash
pip install -r requirements.txt
```

2. **Configureer environment variables:**
```bash
cp .env.example .env
# Bewerk .env en voeg je GROQ_API_KEY toe
```

3. **Start de backend:**
```bash
cd backend
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### Frontend Setup

1. **Installeer Node.js dependencies:**
```bash
cd frontend
npm install
```

2. **Start de frontend:**
```bash
npm run dev
```

## Gebruik

1. **Open de applicatie** in je browser: `http://localhost:3000`

2. **Verbind met backend**: De status indicator toont de verbinding

3. **Maak een nieuw document**:
   - Klik op de "+" knop in de File Explorer
   - Geef een naam op voor je document

4. **Gebruik de AI Agents**:
   - Selecteer een agent type (Writer, Rewriter, Structure)
   - Geef een instructie in het Agent Interface
   - Bekijk de real-time generatie

5. **Herschrijf tekst**:
   - Selecteer tekst in de preview
   - Klik op "Herschrijven"
   - Geef instructies voor de herschrijving

6. **Sla documenten op**:
   - Gebruik Ctrl+S of de save knop
   - Bestanden worden automatisch opgeslagen als .md

## Agent Types

### DocumentWriter
- **Doel**: Schrijft nieuwe uitgebreide documenten
- **Input**: Titel en beschrijving van gewenste content
- **Output**: Volledig gestructureerd Markdown document

### DocumentRewriter  
- **Doel**: Herschrijft bestaande tekst
- **Input**: Geselecteerde tekst + herschrijfinstructies
- **Output**: Verbeterde versie van de originele tekst

### StructureAgent
- **Doel**: Verbetert documentstructuur
- **Input**: Bestaand document
- **Output**: Geherstructureerd document met betere organisatie

## GROQ DeepSeek-R1 Optimalisaties

Het systeem is geoptimaliseerd voor DeepSeek-R1:

- **Think Tag Filtering**: Automatische verwijdering van `<think>` tags
- **Context Window Management**: Intelligente truncatie voor 32k token limit
- **Streaming Support**: Real-time content generatie
- **Error Handling**: Robuuste foutafhandeling voor API calls

## Docker Deployment

Voor productie deployment:

```bash
# Kopieer environment file
cp .env.example .env
# Bewerk .env met je GROQ_API_KEY

# Start met Docker Compose
docker-compose up -d
```

## Ontwikkeling

### Project Structuur
```
DocumentenSchrijver/
├── backend/
│   ├── agents/          # AI agents
│   ├── models/          # Data models
│   ├── services/        # Business logic
│   └── main.py          # FastAPI app
├── frontend/
│   ├── src/
│   │   ├── components/  # React components
│   │   └── App.jsx      # Main app
│   └── package.json
├── documents/           # Generated documents
├── requirements.txt     # Python deps
└── docker-compose.yml   # Docker setup
```

### Belangrijke Bestanden

- `backend/agents/agent_manager.py`: Centrale agent management
- `backend/services/groq_service.py`: GROQ API integratie
- `frontend/src/components/DocumentCanvas.jsx`: Editor + preview
- `frontend/src/components/AgentInterface.jsx`: AI agent interface

## Troubleshooting

### Backend Issues
- **GROQ API errors**: Controleer je API key in `.env`
- **WebSocket connection**: Zorg dat backend draait op port 8000
- **File permissions**: Controleer schrijfrechten voor `documents/` directory

### Frontend Issues  
- **Connection failed**: Controleer of backend draait
- **Monaco Editor**: Mogelijk browser compatibility issue
- **WebSocket errors**: Controleer CORS instellingen

## Licentie

MIT License - zie LICENSE bestand voor details.

## Bijdragen

1. Fork het project
2. Maak een feature branch
3. Commit je wijzigingen  
4. Push naar de branch
5. Open een Pull Request

## Support

Voor vragen of problemen, open een issue in de GitHub repository.
